# Deviation API Documentation

## Overview
The Deviation API provides endpoints for managing deviation reports in a multi-step workflow. It handles:
- Retrieving form fields for each step
- Creating/updating deviations
- Searching deviations
- Viewing deviation details

All endpoints require authentication.

## Endpoints

### POST /api/deviation/upload
Uploads an attachment file for a deviation. If the upload is in the first page, then create a new random uuid and make sure to send this id to the /add endpoint when you add the first page.

**Form Data:**
- `id`: "string (UUID of deviation)"
- `file`: "file (attachment to upload)"

**Response:**
```json
{
  "file_name": "string",
  "uploaded_on": "datetime",
  "attachment_id": "string (UUID)"
}
```

**Errors:**
- 400 - Invalid request or UUID
- 400 - File upload error
- 400 - Unknown error

### GET /api/deviation/fields
Retrieves form fields and options for a specific page of the deviation workflow.

**Request Parameters:**
```json
{
  "page": "integer (1-3)",
  "id": "string (UUID, optional)"
}
```

**Response:**
```json
{
  "fields": {
    "field_id": {
      "df_id": "string",
      "input": "string",
      "required": "boolean",
      "required_kvalprak": "boolean", 
      "page": "integer",
      "order": "integer",
      "title": "string",
      "description": "string"
    }
  },
  "options": {
    "field_id": {
      "option_id": {
        "option_id": "string",
        "name": "string",
        "default": "boolean"
      }
    }
  }
}
```

**Errors:**
- 400 - Invalid page number
- 403 - No access permissions for page

---

### POST /api/deviation/add
Creates or updates a deviation report for a specific page.

**Request Parameters:**
```json
{
  "page": "integer (1-3)",
  "id": "string (UUID, optional for new deviations)",
  "form_data": "object (field values)"
}
```

**Response:**
```json
{
  "success": "boolean",
  "id": "string (UUID)",
  "page": "integer"
}
```

**Errors:**
- 400 - Invalid UUID or page number
- 403 - No access permissions for page
- 404 - Deviation not found
- 500 - Failed to save deviation data

---

### GET /api/deviation/search
Searches for deviations based on permissions.

**Response:**
```json
{
  "list": {
    "field_id": "field_title"
  },
  "deviations": {
    "deviation_id": {
      "field_id": {
        "df_id": "string",
        "input": "string",
        "search": "integer",
        "list": "integer",
        "title": "string",
        "status": "string",
        "answer": "string"
      }
    }
  },
  "fields": {
    "field_id": {
      "df_id": "string",
      "input": "string",
      "required": "boolean",
      "required_kvalprak": "boolean",
      "page": "integer",
      "order": "integer",
      "title": "string",
      "description": "string"
    }
  },
  "heading_df_id": "string"
}
```

**Errors:**
- 403 - No access permissions

---

### GET /api/deviation/view/{id}
Retrieves details for a specific deviation.

**Response:**
```json
{
  "id": "string",
  "fields": {
    "field_id": {
      "df_id": "string",
      "input": "string",
      "required": "boolean",
      "required_kvalprak": "boolean",
      "page": "integer",
      "order": "integer",
      "title": "string",
      "description": "string",
      "answer": "string"
    }
  },
  "attachments": [
    {
      "attachment_id": "string",
      "uploaded_on": "datetime",
      "file_name": "string",
      "file_ext": "string"
    }
  ],
  "options": {
    "field_id": {
      "option_id": {
        "option_id": "string",
        "name": "string",
        "default": "boolean"
      }
    }
  },
  "selected": {
    "field_id": ["value"]
  },
  "rightsPage2": "boolean",
  "rightsPage3": "boolean"
}
```

**Errors:**
- 400 - Invalid UUID format
- 403 - No access permissions
- 404 - Deviation not found

## Workflow
The deviation process has 3 pages/steps:
1. Initial report
2. Analysis/assessment
3. Resolution/follow-up

Each page has different access controls and required fields.
