# Authentication API Documentation

Base url `https://{company_base_url}.orna.vardna.se`. e.g. https://kvalprak.orna.vardna.se

## Login Endpoint

**Endpoint**: `POST /api/auth/login`

Authenticates a user and returns JWT and refresh tokens.

### Request Body
```json
{
  "login_email": "<EMAIL>",
  "login_password": "password123",
  "otp": "123456"  // Only required if 2FA is enabled
}
```

### Responses

**Success (200 OK)**:
```json
{
  "refresh_token": "hexadecimal_string",
  "refresh_token_expiry": "YYYY-MM-DD HH:MM:SS",
  "name": "User Name",
  "email": "<EMAIL>",
  "token": "jwt_token_string"
}
```

**Error Responses**:
- 401 Unauthorized:
  - Invalid credentials
  - 2FA required (when user has 2FA enabled but OTP not provided)
  - Invalid 2FA OTP

## Refresh Token Endpoint

**Endpoint**: `POST /api/auth/refresh`

Generates a new JWT token using a valid refresh token.

### Request Body
```json
{
  "refresh_token": "hexadecimal_string",
  "login_email": "<EMAIL>"
}
```

### Responses

**Success (200 OK)**:
```json
{
  "refresh_token": "new_hexadecimal_string",
  "refresh_token_expiry": "YYYY-MM-DD HH:MM:SS",
  "token": "new_jwt_token_string"
}
```

**Error Responses**:
- 400 Bad Request: Refresh token required
- 401 Unauthorized:
  - Expired refresh token
  - Invalid refresh token

## Token Expiration
- JWT tokens expire after 1 hour (configurable)
- Refresh tokens expire after 30 days
