<?php
// Simple JWT token generator for testing
require_once 'vendor/autoload.php';
use Firebase\JWT\JWT;

$secret_key = 'wxaKIynqiqVaZX0ZhFe0lFIRjISESf2o';
$issued_at = time();
$expiration = $issued_at + 3600; // 1 hour

$payload = [
    'iss' => 'http://localhost',
    'aud' => 'http://localhost', 
    'iat' => $issued_at,
    'exp' => $expiration,
    'data' => [
        'user_id' => '8b93d828-1312-4bfa-9314-08331d49cd8d', // Test user ID
        'email' => '<EMAIL>',
        'session' => 'test_session_id'
    ]
];

$token = JWT::encode($payload, $secret_key, 'HS256');
echo "JWT Token: " . $token . "\n";
?>
