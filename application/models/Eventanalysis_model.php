<?php
class Eventanalysis_model extends MY_Model {
	
	public function get_empty_answers($ea_id)
	{
		$answers = [];
		
		$q = $this->db
				->group_start()
					->where('answer IS NULL', NULL, FALSE)
					->or_where('answer','')
				->group_end()
				->where('ea_id',UUID_TO_BIN($ea_id))
				->get($this->db_table('eventanalysis_questions'));
		
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $answer)
			{
				$answer->id          = BIN_TO_UUID($answer->id);
				$answer->ea_id       = BIN_TO_UUID($answer->ea_id);
				$answer->parent      = BIN_TO_UUID($answer->parent);
				$answer->responsible = BIN_TO_UUID($answer->responsible);
				$answers[$answer->responsible][] = $answer;
			}
		}
		
		return $answers;
	}
	
}
