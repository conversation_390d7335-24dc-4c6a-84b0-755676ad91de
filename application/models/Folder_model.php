<?php
class Folder_model extends MY_Model {
	
	private $data = array();
	
	public function _get_folder_name()
	{
		return trim($this->input->post('folder_name') ?: '');
	}
	
	public function _get_folder_order_by()
	{
		return trim($this->input->post('folder_order_by') ?: '');
	}
	
	public function _get_folder_position()
	{
		return $this->input->post('folder_position') ? $this->input->post('folder_position') : array();
	}
	
	public function _get_folder_department()
	{
		return $this->input->post('folder_department') ? $this->input->post('folder_department') : array();
	}
	
	private function _get_data()
	{
		$this->data['name']     = $this->_get_folder_name();
		$this->data['order_by'] = $this->_get_folder_order_by();
	}
	
	public function create( $folder_id, $menu_id )
	{
		$this->_get_data();
		$this->data['folder_id'] = UUID_TO_BIN($folder_id);
		$this->data['menu_id']   = UUID_TO_BIN($menu_id);
		return $this->db->insert($this->db_table('folders'),$this->data);
	}
	
	public function get( $folder_id )
	{
		$folder    = NULL;
		$folder_id = UUID_TO_BIN($folder_id);
		
		$q = $this->db
				->where('folder_id',$folder_id)
				->limit(1)
				->get($this->db_table('folders'));
		if( $q->num_rows() === 1)
		{
			$folder = $q->row();
			$folder->folder_id = BIN_TO_UUID($folder->folder_id);
			$folder->menu_id   = BIN_TO_UUID($folder->menu_id);
		}
		return $folder;
	}
	
	public function get_all( $id = NULL, $return_id = FALSE )
	{
		if( empty($id) ) { return NULL; }
		
		$folders = array();
		$menu_id = array();
		$is_array = FALSE;
		
		if( is_array($id) )
		{
			$is_array = TRUE;
			foreach($id as $menu)
			{
				$menu_id[] = UUID_TO_BIN($menu);
			}
		}
		else
		{
			$menu_id[] = UUID_TO_BIN($id);
		}
		
		$q = $this->db
				->select('folder_id,menu_id,order_by,name')
				->where_in('menu_id',$menu_id)
				->order_by('name', 'ASC')
				->get($this->db_table('folders'));
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $folder)
			{
				$folder->folder_id = BIN_TO_UUID($folder->folder_id);
				$folder->menu_id   = BIN_TO_UUID($folder->menu_id);
				$folder->name      = preg_replace('/^[0-9\.\s]+/u', '',$folder->name);
				if( $is_array )
					$folders[$folder->menu_id][$folder->folder_id] = $folder;
				else
					$folders[$folder->folder_id] = $folder;
				if( $return_id ) {
					$folders['id'][$folder->folder_id] = $folder->menu_id;
					// $folders['menu_id'][] = $folder->menu_id;
				}
			}
		}
		
		return $folders;
	}
	
	public function update( $folder )
	{
		$folder->name      = $this->_get_folder_name();
		$folder->order_by  = $this->_get_folder_order_by();
		$folder->folder_id = UUID_TO_BIN($folder->folder_id);
		$folder->menu_id   = UUID_TO_BIN($folder->menu_id);
		
		$q = $this->db
				->where('folder_id',$folder->folder_id)
				->update($this->db_table('folders'),$folder);
		return $q;
	}
	
	public function delete( $folder_id )
	{
		$folder_id = UUID_TO_BIN($folder_id);
		
		if( $this->db->delete($this->db_table('folders'),array('folder_id' => $folder_id)) )
		{
			return $this->db->delete($this->db_table('folder_group'),array('folder_id' => $folder_id));
		}
		
		return FALSE;
	}
}
