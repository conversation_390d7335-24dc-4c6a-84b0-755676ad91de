<?php
class User_model extends MY_Model {
	
	public function _get_users_email()
	{
		return trim($this->input->post('users_email') ?: '');
	}

	public function _get_users_username()
	{
		return trim($this->input->post('users_username') ?: '');
	}
	
	public function _get_users_name()
	{
		return trim($this->input->post('users_name') ?: '');
	}
	
	public function _get_users_position()
	{
		return trim($this->input->post('users_position') ?: '');
	}
	
	public function _get_users_HSAID()
	{
		return trim($this->input->post('users_HSAID') ?: '');
	}
	
	public function _get_users_phone()
	{
		return trim($this->input->post('users_phone') ?: '');
	}
	
	public function _get_users_mobile()
	{
		return trim($this->input->post('users_mobile') ?: '');
	}
	
	public function _get_users_address()
	{
		return trim($this->input->post('users_address') ?: '');
	}
	
	public function _get_users_zip()
	{
		return trim($this->input->post('users_zip') ?: '');
	}
	
	public function _get_users_city()
	{
		return trim($this->input->post('users_city') ?: '');
	}
	
	public function _get_users_phone_private()
	{
		return trim($this->input->post('users_phone_private') ?: '');
	}
	
	public function _get_users_mobile_private()
	{
		return trim($this->input->post('users_mobile_private') ?: '');
	}
	
	public function _get_users_email_private()
	{
		return trim($this->input->post('users_email_private') ?: '');
	}

	
	private function _get_data()
	{
		$data = array(
			'email'          => $this->_get_users_email(),
			'username'       => $this->_get_users_username(),
			'name'           => $this->_get_users_name(),
			'position'       => $this->_get_users_position(),
			'HSAID'          => $this->_get_users_HSAID(),
			'phone'          => $this->_get_users_phone(),
			'mobile'         => $this->_get_users_mobile(),
			'address'        => $this->_get_users_address(),
			'zip'            => $this->_get_users_zip(),
			'city'           => $this->_get_users_city(),
			'phone_private'  => $this->_get_users_phone_private(),
			'mobile_private' => $this->_get_users_mobile_private(),
			'email_private'  => $this->_get_users_email_private(),
		);
		return $data;
	}
	
	public function create( $user_id, $company_id = NULL )
	{
		$company_id = $company_id !== NULL ? $company_id : $this->auth_company_id;
		$company_id = UUID_TO_BIN($company_id);
		
		$data = $this->_get_data();
		$data['company_id'] = $company_id;
		$data['user_id']    = UUID_TO_BIN($user_id);
		$data['auth_level'] = 0;
		$data['completed']  = 1;
		$data['created_at'] = date('Y-m-d H:i:s');
		return $this->db->insert($this->db_table('user_table'),$data);
	}
	
	public function assign_roles($position, $department, $user_id)
	{
		$position   = UUID_TO_BIN($position);
		$department = UUID_TO_BIN($department);
		$user_id    = UUID_TO_BIN($user_id);
		
		$user_group[] = [
			'user_id'  => $user_id,
			'group_id' => $position
		];
		
		$user_group[] = [
			'user_id'  => $user_id,
			'group_id' => $department
		];
		
		// ACL
		$acl_users_and_groups[] = [
			'user_id'   => $user_id,
			'group_id'  => $position,
			'action_id' => UUID_TO_BIN('b7562f85-38ac-400d-98ac-7d82d3d9ff6c') // Read
		];
		
		$acl_users_and_groups[] = [
			'user_id'   => $user_id,
			'group_id'  => $position,
			'action_id' => UUID_TO_BIN('1886185d-cac6-4ca8-9d94-e8d507355207') // Create
		];
		
		$acl_users_and_groups[] = [
			'user_id'   => $user_id,
			'group_id'  => $position,
			'action_id' => UUID_TO_BIN('b116acf0-d699-4d61-a28d-d9a315967361') // Update
		];
		
		$acl_users_and_groups[] = [
			'user_id'   => $user_id,
			'group_id'  => $department,
			'action_id' => UUID_TO_BIN('b7562f85-38ac-400d-98ac-7d82d3d9ff6c') // Read
		];
		
		$acl_users_and_groups[] = [
			'user_id'   => $user_id,
			'group_id'  => $department,
			'action_id' => UUID_TO_BIN('7cab2d38-c4a8-4dfd-8b8d-bce7782d0088') // create
		];
		
		$acl_users_and_groups[] = [
			'user_id'   => $user_id,
			'group_id'  => $department,
			'action_id' => UUID_TO_BIN('5e7db833-7f91-4ace-ba05-566c0f3510ef') // deviation_two
		];
		
		$acl_users_and_groups[] = [
			'user_id'   => $user_id,
			'group_id'  => $department,
			'action_id' => UUID_TO_BIN('8d40873f-4750-4839-b784-53ebf22245ad') // deviation_three
		];
		
		$acl_users_and_groups[] = [
			'user_id'   => $user_id,
			'group_id'  => $department,
			'action_id' => UUID_TO_BIN('69cf90fd-c8ea-4eb8-bc7e-b4f00649345d') // read
		];
		
		$acl_users_and_groups[] = [
			'user_id'   => $user_id,
			'group_id'  => $department,
			'action_id' => UUID_TO_BIN('a850de4f-4bcd-4d5c-a457-e317041dbf62') // update
		];
		
		$this->db->insert_batch('user_group', $user_group);
		$this->db->insert_batch('acl_users_and_groups', $acl_users_and_groups);
	}
	
	public function assign_membership($user_id, $data)
	{
		return $this->db->update($this->db_table('user_table'), $data, ['user_id' => UUID_TO_BIN($user_id)]);
	}

	public function update_2fa($user, $secret) {
		return $this->db->update($this->db_table('user_table'), ['totp_secret' => $secret], ['user_id' => UUID_TO_BIN($user->user_id)]);
	}
	
	public function update( $user )
	{
		$user->user_id    = UUID_TO_BIN($user->user_id); 
		$user->company_id = UUID_TO_BIN($user->company_id);
		
		$data = $this->_get_data();
		if( ! empty($data) )
		{
			foreach($data as $key => $val)
			{
				$user->$key = $val;
			}
			
			$password = $this->input->post('users_password_new');
			if( ! empty($password) )
			{
				$user->passwd = $this->hash_password($password);
			}
			else
			{
				unset($user->passwd);
			}
			
			return $this->db->update($this->db_table('user_table'), $user, ['user_id' => $user->user_id]);
		}
		
		return FALSE;
	}
	
	public function get( $user_id )
	{
		$user    = NULL;
		$user_id = UUID_TO_BIN($user_id);
		
		$q = $this->db
				->where('user_id',$user_id)
				->limit(1)
				->get($this->db_table('user_table'));
		if( $q->num_rows() === 1)
		{
			$user = $q->row();
			$user->user_id    = BIN_TO_UUID($user->user_id);
			$user->company_id = BIN_TO_UUID($user->company_id);
		}
		return $user;
	}
	
	public function get_all( $users_id = array(), $hidden = FALSE, $company_id = NULL )
	{
		$users        = array();
		$users_id_bin = array();
		$company_id   = $company_id !== NULL ? $company_id : $this->auth_company_id;
		$company_id   = UUID_TO_BIN($company_id);
		
		if( !empty($users_id) )
		{
			foreach($users_id AS $user_id)
			{
				$users_id_bin[] = UUID_TO_BIN($user_id);
			}
			$this->db->where_in('user_id',$users_id_bin);
		}
		
		if( ! $hidden )
		{
			$this->db->where('hidden','0');
		}
		
		$q = $this->db
				->select('user_id,email,name,position,phone,mobile,last_login,last_visit')
				->where('company_id',$company_id)
				->order_by('name', 'ASC')
				->get($this->db_table('user_table'));
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $user)
			{
				$user->user_id = BIN_TO_UUID($user->user_id);
				$users[$user->user_id] = $user;
			}
		}
		
		return $users;
	}
	
	public function get_all_active()
	{
		$users = array();
		
		$q = $this->db
				->select('company_id,user_id,name')
				->where('hidden','0')
				->order_by('created_at', 'ASC')
				->get($this->db_table('user_table'));
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $user)
			{
				$user->company_id = BIN_TO_UUID($user->company_id);
				$user->user_id    = BIN_TO_UUID($user->user_id);
				$users[$user->company_id][$user->user_id] = $user->name;
			}
		}
		
		return $users;
	}
	
	public function get_users_by_groups( $position, $department )
	{
		$users = array();
		$position_bin = array();
		$department_bin = array();
		
		if(empty($position) OR empty($department))
			return $users;
		foreach($position as $p)
			$position_bin[] = UUID_TO_BIN($p);
		foreach($department as $d)
			$department_bin[] = UUID_TO_BIN($d);
		
		$company_id = UUID_TO_BIN($this->auth_company_id);
		$query = "
				SELECT user_id, name 
				FROM  `" . $this->db_table('user_table') . "`  
				WHERE `user_id` IN (
					SELECT `user_id` 
					FROM   `" . $this->db_table('user_group') . "` 
					WHERE  `group_id` IN ?
					GROUP  BY `user_id`
				) 
				AND `user_id` IN (
					SELECT `user_id` 
					FROM   `" . $this->db_table('user_group') . "` 
					WHERE  `group_id` IN ?
					GROUP  BY `user_id`
				) 
				AND `company_id` = ?
				AND `hidden` = '0'
				GROUP BY 
						`user_id`
				ORDER BY 
						`name` ASC
				";
		
		$q = $this->db->query($query, [$position_bin,$department_bin,$company_id]);
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $user)
			{
				$user->user_id = BIN_TO_UUID($user->user_id);
				$users[$user->user_id] = $user->name;
			}
		}
		return $users;
	}
	
	public function get_users_by_all_groups( $groups )
	{
		$users = array();
		$groups_bin = array();
		
		if(empty($groups))
			return $users;
		foreach($groups as $g)
			$groups_bin[] = UUID_TO_BIN($g);
		
		$company_id = UUID_TO_BIN($this->auth_company_id);
		$query = "
				SELECT user_id, name 
				FROM  `" . $this->db_table('user_table') . "`  
				WHERE `user_id` IN (
					SELECT `user_id` 
					FROM   `" . $this->db_table('user_group') . "` 
					WHERE  `group_id` IN ?
					GROUP  BY `user_id`
				) 
				AND `company_id` = ?
				AND `hidden` = '0'
				GROUP BY 
						`user_id`
				ORDER BY 
						`name` ASC
				";
		
		$q = $this->db->query($query, [$groups_bin,$company_id]);
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $user)
			{
				$user->user_id = BIN_TO_UUID($user->user_id);
				$users[$user->user_id] = $user->name;
			}
		}
		return $users;
	}
	
	
	public function delete( $user_id )
	{
		// acl_users_and_groups
		// auth_sessions
		// deviation_email_default
		// document_editor
		// document_education
		// document_education_done
		// document_readlog
		// post_read
		// user_group
		// user_messages
		
		// users [hidden=1]
		if( $this->db->delete($this->db_table('auth_sessions_table'),array('user_id' => UUID_TO_BIN($user_id))) )
		{
			$this->db->update($this->db_table('documents'), [
				'owner' => UUID_TO_BIN($this->auth_user_id)
			],
			[
				'owner' => UUID_TO_BIN($user_id),
			]);
			return $this->db->update($this->db_table('user_table'),[
				'username' => '',
				'email' => '',
				'hidden' => '1',
				'passwd' => '',
				'passwd_recovery_code' => NULL,
				'passwd_recovery_date' => NULL,
				'passwd_modified_at' => NULL,
				'position' => NULL,
				'HSAID' => NULL,
				'phone' => NULL,
				'mobile' => NULL,
				'address' => NULL,
				'zip' => NULL,
				'city' => NULL,
				'phone_private' => NULL,
				'mobile_private' => NULL,
				'email_private' => NULL,
			],[
				'user_id' => UUID_TO_BIN($user_id)
			]);


		}
		
		return FALSE;
	}
	
	public function completed($user_id, $done = FALSE)
	{
		if( $done )
		{
			return $this->db
						->where('user_id', UUID_TO_BIN($user_id))
						->set('completed', '0', FALSE)
						->update($this->db_table('user_table'));
		}
		else
		{
			return $this->db
						->where('user_id', UUID_TO_BIN($user_id))
						->set('completed', 'completed + 1', FALSE)
						->update($this->db_table('user_table'));
		}
	}

	public function save( $table, $column, $id, $users, $delete_users = array() )
	{
		if( $this->remove($table,$column,$id,$delete_users) === FALSE) { return FALSE; }
		if( empty($users) ) { return TRUE; }
		$data = array();
		foreach($users as $user_id)
		{
			$data[] = array(
				$column   => UUID_TO_BIN($id),
				'user_id' => UUID_TO_BIN($user_id)
			);
		}

		if( ! empty($data) )
			return $this->db->insert_batch($this->db_table($table),$data);
		
		return FALSE;
	}
	
	public function remove( $table, $column, $id, $delete_users )
	{
		$delete = array();
		if( !empty($delete_users) )
		{
			foreach($delete_users as $user_id)
			{
				$delete[] = UUID_TO_BIN($user_id);
			}
			$this->db->where_in('user_id', $delete);
		}
		return $this->db->delete($this->db_table($table),array($column => UUID_TO_BIN($id)));
	}
	
	public function is_unique()
	{
		$q = $this->db
				->select('user_id')
				->where('email',$this->_get_users_email())
				->limit(1)
				->get($this->db_table('user_table'));

		if( $q->row() )
		{
			return FALSE;
		}
		return TRUE;
	}
	
	public function is_unique_update( $user )
	{
		$user_id = UUID_TO_BIN($user->user_id);
		
		$q = $this->db
				->select('user_id')
				->where('email',$this->_get_users_email())
				->limit(2)
				->get($this->db_table('user_table'));

		if( $q->num_rows() >= 2 )
		{
			return FALSE;
		}
		
		if( $row = $q->row() )
		{
			if( $row->user_id !== $user_id )
			{
				return FALSE;				
			}
		}
		
		return TRUE;
	}
	
	/**
	 * Update a user record with data not from POST
	 *
	 * @param  int     the user ID to update
	 * @param  array   the data to update in the user table
	 * @return bool
	 */
	public function update_user_raw_data( $the_user, $user_data = [] )
	{
		$this->db->where('user_id', UUID_TO_BIN($the_user))
			->update( $this->db_table('user_table'), $user_data );
	}
	
	/**
	 * Get data for a recovery
	 * 
	 * @param   string  the email address
	 * @return  mixed   either query data or FALSE
	 */
	public function get_recovery_data( $email )
	{
		$query = $this->db->select( 'u.user_id, u.email, u.name, u.banned' )
			->from( $this->db_table('user_table') . ' u' )
			->where( 'LOWER( u.email ) =', strtolower( $email ) )
			->limit(1)
			->get();

		if( $query->num_rows() == 1 )
		{
			$user = $query->row();
			$user->user_id = BIN_TO_UUID($user->user_id);
			return $user;
		}

		return FALSE;
	}
	
	/**
	 * Get the user name, user salt, and hashed recovery code,
	 * but only if the recovery code hasn't expired.
	 *
	 * @param  int  the user ID
	 */
	public function get_recovery_verification_data( $user_id )
	{
		$recovery_code_expiration = date('Y-m-d H:i:s', time() - config_item('recovery_code_expiration') );

		$query = $this->db->select( 'email, name, passwd_recovery_code' )
			->from( $this->db_table('user_table') )
			->where( 'user_id', UUID_TO_BIN($user_id) )
			->where( 'passwd_recovery_date >', $recovery_code_expiration )
			->limit(1)
			->get();

		if ( $query->num_rows() == 1 )
			return $query->row();
		
		return FALSE;
	}
	
	/**
	 * Hash users password before storing it in the database.
	 * @param string $password
	 * @return hashed password
	 */
	public function hash_password( $password ) {
		return password_hash(
			base64_encode(
				hash('sha384', $password, true)
			),
			PASSWORD_DEFAULT
		);
	}
	
}
