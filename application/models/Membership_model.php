<?php
class Membership_model extends MY_Model {
	
	public function _get_memberships_name()
	{
		return trim($this->input->post('memberships_name') ?: '');
	}
	
	public function _get_memberships_default()
	{
		return trim($this->input->post('memberships_default') ?: '');
	}

	private function _get_data()
	{
		$data = array(
			'name'    => $this->_get_memberships_name(),
			'default' => $this->_get_memberships_default(),
		);
		return $data;
	}
	
	public function create( $membership_id )
	{
		$data = $this->_get_data();
		$data['membership_id']   = UUID_TO_BIN($membership_id);
		
		if( $this->db->insert($this->db_table('memberships'),$data) )
		{
			return $this->update_default($data['membership_id']);
		}
		
		return FALSE;
	}
	
	public function get( $membership_id )
	{
		$membership    = NULL;
		$membership_id = UUID_TO_BIN($membership_id);
		
		$q = $this->db
				->where('membership_id',$membership_id)
				->limit(1)
				->get($this->db_table('memberships'));
		if( $q->num_rows() === 1)
		{
			$membership = $q->row();
			$membership->membership_id = BIN_TO_UUID($membership->membership_id);
		}
		
		return $membership;
	}
	
	public function get_all( $limit = NULL, $offset = NULL )
	{
		$memberships     = array();
		
		$q = $this->db
				->order_by('default', 'DESC')
				->order_by('name', 'ASC')
				->get($this->db_table('memberships'),$limit,$offset);
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $membership)
			{
				$membership->membership_id = BIN_TO_UUID($membership->membership_id);
				$memberships[$membership->membership_id] = $membership;
			}
		}
		
		return $memberships;
	}
	
	public function get_all_dropdown( $limit = NULL, $offset = NULL )
	{
		$memberships = array(
			'membership' => array(),
			'default'    => NULL
		);
		
		$q = $this->db
				->order_by('default', 'DESC')
				->order_by('name', 'ASC')
				->get($this->db_table('memberships'),$limit,$offset);
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $membership)
			{
				$membership->membership_id = BIN_TO_UUID($membership->membership_id);
				$memberships['memberships'][$membership->membership_id] = $membership->name;
				if( $membership->default )
				{
					$memberships['default'] = $membership->membership_id;
				}
			}
		}
		
		return $memberships;
	}
	
	public function update( $membership )
	{
		$membership->membership_id = UUID_TO_BIN($membership->membership_id);
		
		$data = $this->_get_data();
		
		$q = $this->db
				->where('membership_id',$membership->membership_id)
				->update($this->db_table('memberships'),$data);
				
		if( $q )
		{
			return $this->update_default($membership->membership_id);
		}
		
		return FALSE;
	}
	
	private function update_default( $membership_id )
	{
		if( $this->_get_memberships_default() == 1 )
		{
			return $this->db
						->set('default',0)
						->where('membership_id !=',$membership_id)
						->update($this->db_table('memberships'));
		}
		
		return TRUE;
	}
	
	// public function update_position( $group )
	// {
		// $group->name       = $this->_get_groups_name();
		// $group->group_id   = UUID_TO_BIN($group->group_id);
		// $group->company_id = UUID_TO_BIN($group->company_id);
		
		// $q = $this->db
				// ->where('group_id',$group->group_id)
				// ->update($this->db_table('groups'),$group);
		// return $q;
	// }
	
	public function is_unique( $membership = NULL )
	{
		$q = $this->db
				->select('membership_id')
				->where('name',$this->_get_memberships_name())
				->limit(2)
				->get($this->db_table('memberships'));

		if( $q->num_rows() >= 2 )
		{
			return FALSE;
		}
		
		if( $row = $q->row() )
		{
			if( $membership === NULL || $row->membership_id !== UUID_TO_BIN($membership->membership_id) )
			{
				return FALSE;				
			}
		}
		
		return TRUE;
	}
	
	// public function is_unique_update( $group )
	// {
		// $group->group_id   = UUID_TO_BIN($group->group_id);
		// $group->company_id = UUID_TO_BIN($group->company_id);
		
		// $q = $this->db
				// ->select('group_id')
				// ->where('company_id',$group->company_id)
				// ->where('name',$this->_get_groups_name())
				// ->where('type',$group->type)
				// ->get($this->db_table('groups'));

		// if( $q->num_rows() >= 2 )
		// {
			// return FALSE;
		// }
		
		// if( $row = $q->row() )
		// {
			// if( $row->group_id !== $group->group_id )
			// {
				// return FALSE;				
			// }
		// }
		
		// return TRUE;
	// }
}
