<?php
class Form_model extends MY_Model {
	
	private $data = array();
	
	public function _get_parent_id()
	{
		return trim($this->input->post('forms_parent_id') ?: '');
	}
	
	public function _get_forms_validate()
	{
		return trim($this->input->post('forms_validate') ?: '');
	}
	
	public function _get_sort()
	{
		return trim($this->input->post('forms_sort') ?: '');
	}
	
	public function _get_question_id_old()
	{
		return trim($this->input->post('forms_question_id_old') ?: '');
	}
	
	public function _get_forms_name()
	{
		return trim($this->input->post('forms_name') ?: '');
	}
	
	public function _get_forms_description()
	{
		return trim($this->input->post('forms_description') ?: '');
	}
	
	public function _get_forms_type()
	{
		return $this->input->post('forms_types');
	}
	
	public function _get_forms_field()
	{
		return $this->input->post('forms_field');
	}
	
	public function _get_forms_global()
	{
		return $this->input->post('forms_global');
	}
	public function _get_tree_documents()
	{
		return $this->input->post('tree_documents');
	}
	// TODO
	private function _get_data()
	{
		$this->data['name']        = $this->_get_forms_name();
		$this->data['description'] = $this->_get_forms_description();
		$this->data['sticky']      = $this->_get_forms_sticky();
	}

	private function _generate_survey_id()
	{
		$survey_id = bin2hex(random_bytes(5));
		$q = $this->db->get_where($this->db_table('forms'), ['survey_id' => $survey_id]);
		if( $q->num_rows() !== 0 )
		{
			return $this->_generate_survey_id();
		}
		return $survey_id;
	}

	private function _create_table($survey_id)
	{
		$table = $this->db_table('form_survey') . $survey_id;
		$q = "CREATE TABLE IF NOT EXISTS `{$table}` (
			`company_id` binary(16) NOT NULL,
			`page_id` binary(16) NOT NULL,
			`question_id` binary(16) NOT NULL,
			`answer` text COLLATE utf8mb4_swedish_ci NOT NULL,
			PRIMARY KEY (`company_id`,`page_id`,`question_id`)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_swedish_ci;";

		$this->db->query($q);
	}
	
	public function create_form( $form_id, $create_id, $parent_id )
	{
		$this->data['company_id']  = UUID_TO_BIN($this->auth_company_id);
		$this->data['form_id']     = UUID_TO_BIN($create_id);
		$this->data['survey_id']   = $this->_generate_survey_id();
		$this->data['name']        = $this->_get_forms_name();
		$this->data['type']        = $this->_get_forms_type();
		$this->data['description'] = $this->_get_forms_description();
		$this->data['global']      = $this->_get_forms_global() ?? 0;

		$this->_create_table($this->data['survey_id']);
		return $this->db->insert($this->db_table('forms'),$this->data);
	}

	public function edit_form( $form, $page = NULL )
	{
		$form->company_id = UUID_TO_BIN($form->company_id);
		$form->form_id    = UUID_TO_BIN($form->form_id);

		$form->name        = $this->_get_forms_name();
		$form->description = $this->_get_forms_description();
		$form->type        = $this->_get_forms_type();
		$form->global      = $this->_get_forms_global();

		return $this->db->update($this->db_table('forms'), $form, [
			'form_id' => $form->form_id
		]);
	}
	
	public function create_page( $form_id, $create_id, $parent_id )
	{
		$this->data['form_id']     = UUID_TO_BIN($form_id);
		$this->data['page_id']     = UUID_TO_BIN($create_id);
		$this->data['name']        = $this->_get_forms_name();
		$this->data['description'] = $this->_get_forms_description();
		if( $parent_id && $form_id !== $parent_id )
			$this->data['parent_id'] = UUID_TO_BIN($parent_id);
		return $this->db->insert($this->db_table('form_pages'),$this->data);
	}

	public function edit_page( $form = NULL, $page = NULL )
	{
		$page->form_id = UUID_TO_BIN($page->form_id);
		$page->page_id = UUID_TO_BIN($page->page_id);

		if( ! empty($page->parent_id) )
			$page->parent_id = UUID_TO_BIN($page->parent_id);

		$page->name        = $this->_get_forms_name();
		$page->description = $this->_get_forms_description();

		return $this->db->update($this->db_table('form_pages'), $page, [
			'page_id' => $page->page_id
		]);
	}
	
	public function delete_checklist( $form, $page )
	{
		$this->db->delete($this->db_table('surveys'), [
			'page_id' => UUID_TO_BIN($page->page_id),
		]);
		
		$this->db->delete($this->db_table('survey_dates'), [
			'page_id' => UUID_TO_BIN($page->page_id),
		]);
		
		$this->db->delete($this->db_table('survey_group'), [
			'page_id' => UUID_TO_BIN($page->page_id),
		]);
		
		$this->db->delete($this->db_table('survey_user'), [
			'page_id' => UUID_TO_BIN($page->page_id),
		]);
		
		$this->db->delete($this->db_table('form_pages'), [
			'page_id' => UUID_TO_BIN($page->page_id),
		]);
		
		$this->db->delete($this->db_table('forms'), [
			'form_id' => UUID_TO_BIN($form->form_id),
		]);
		
		$table = $this->db_table('form_survey') . $form->survey_id;
		$this->db->query("DROP TABLE IF EXISTS {$table};");
		
		return TRUE;
	}
	
	public function create_question( $current_id, $question_id, $parent_id )
	{
		$this->data['page_id']         = UUID_TO_BIN($current_id);
		$this->data['question_id']     = UUID_TO_BIN($question_id);
		// var_dump($this->_get_max_sort($this->data['page_id']));exit;
		$this->data['sort']            = $this->_get_max_sort_question($this->data['page_id']);
		$this->data['name']            = $this->_get_forms_name();
		$this->data['description']     = $this->_get_forms_description();
		$this->data['field']           = $this->_get_forms_field();
		$this->data['validate']        = $this->_get_forms_validate();
		if( $this->_get_parent_id() ) {
			$this->data['parent_id']       = UUID_TO_BIN($this->_get_parent_id());
			$this->data['sort']            = $this->_get_max_sort_by_parent_id($this->data['parent_id']);
		}
		if( $parent_id ) {
			$this->data['parent_id']       = UUID_TO_BIN($parent_id);
			$this->data['sort']            = $this->_get_max_sort_by_parent_id($this->data['parent_id']);
		}
		if( $this->_get_question_id_old() )
			$this->data['question_id_old'] = $this->_get_question_id_old();
			
		if( $this->_get_forms_field() === 'table' )
		{
			$this->data['settings'] = '{"cols":1,"rows":1,"type":["text"],"header":[""],"body":[""]}';
		}
		
		$this->db->update(
			$this->db_table('form_pages'),
			array(
				'type' => 1
			),
			array(
				'page_id' => $this->data['page_id']
			)
		);
		
		return $this->db->insert($this->db_table('form_page_questions'),$this->data);
	}

	public function update_question( $question )
	{
		$question->page_id     = UUID_TO_BIN($question->page_id);
		$question->question_id = UUID_TO_BIN($question->question_id);

		if( $question->parent_id )
			$question->parent_id = UUID_TO_BIN($question->parent_id);

		if( $this->_get_question_id_old() )
			$question->question_id_old = $this->_get_question_id_old();

		$question->name        = $this->_get_forms_name();
		$question->description = $this->_get_forms_description();
		$question->field       = $this->_get_forms_field();
		$question->validate    = $this->_get_forms_validate();

		// var_dump($question);exit;

		return $this->db->update($this->db_table('form_page_questions'), $question, [
			'question_id' => $question->question_id
		]);
	}
	
	public function delete_question( $question, $form )
	{
		$this->db->delete($this->db_table('form_page_questions'), [
			'question_id' => UUID_TO_BIN($question->question_id),
		]);
		
		$this->db->delete($this->db_table('form_page_question_options'), [
			'question_id' => UUID_TO_BIN($question->question_id),
		]);
		
		$this->db->delete($this->db_table('form_survey') . $form->survey_id, [
			'question_id' => UUID_TO_BIN($question->question_id),
		]);
		
		return TRUE;
	}

	function update_table( $question )
	{
		$question->page_id     = UUID_TO_BIN($question->page_id);
		$question->question_id = UUID_TO_BIN($question->question_id);

		if( $question->parent_id )
			$question->parent_id = UUID_TO_BIN($question->parent_id);

		return $this->db->update($this->db_table('form_page_questions'), $question, [
			'question_id' => $question->question_id
		]);
	}
	
	public function create_option( $question_id, $option_id )
	{
		$this->data['option_id']   = UUID_TO_BIN($option_id);
		$this->data['question_id'] = UUID_TO_BIN($question_id);
		$this->data['name']        = $this->_get_forms_name();
		$this->data['sort']        = $this->_get_max_sort_option($this->data['question_id']);
		if( $this->_get_question_id_old() )
			$this->data['option_id_old'] = $this->_get_question_id_old();
		
		return $this->db->insert($this->db_table('form_page_question_options'),$this->data);
	}
	
	public function edit_option( $option )
	{
		$option->question_id = UUID_TO_BIN($option->question_id);
		$option->option_id   = UUID_TO_BIN($option->option_id);
		$option->name        = $this->_get_forms_name();
		
		if( strlen($this->_get_question_id_old()) >= 1 )
			$option->option_id_old = $this->_get_question_id_old();
		else
			$option->option_id_old = NULL;
		
		return $this->db->update($this->db_table('form_page_question_options'), $option, [
			'option_id' => $option->option_id
		]);
	}
	
	public function delete_option( $option, $question, $form )
	{
		return $this->db->delete($this->db_table('form_page_question_options'), [
			'question_id' => UUID_TO_BIN($question->question_id),
			'option_id'   => UUID_TO_BIN($option->option_id),
		]);
		
		// @TODO: Seperate delete functions
		if($question->field === 'radio')
		{
			$this->db->delete($this->db_table('form_survey') . $form->survey_id, [
				'company_id'  => UUID_TO_BIN($this->auth_company_id),
				'question_id' => UUID_TO_BIN($question->question_id),
				'answer'      => $option->option_id
			]);
			
			return $this->db->delete($this->db_table('form_page_question_options'), [
				'question_id' => UUID_TO_BIN($question->question_id),
				'option_id'   => UUID_TO_BIN($option->option_id),
			]);
		}
		// Not done
		if($question->field === 'checkbox')
		{
			$answers = $this->get_answers($form->survey_id, NULL, $question->question_id);
			if( ! empty($answers) )
			{
				foreach($answers as $page_id => $answer)
				{
					$answer = json_decode($answer);
					if(json_last_error() !== JSON_ERROR_NONE)
					{
						continue;
					}
				}
			}
		}
	}
	
	public function get_form_done( $survey_id = NULL, $fiscal = NULL )
	{
		return $this->db->select('done')->get_where($this->db_table('form_survey') . $survey_id . '_done',[
			'company_id' => UUID_TO_BIN($this->auth_company_id),
			'fiscal'     => $fiscal
		])->row();
	}
	
	public function get_form_done_flex( $survey_id = NULL, $fiscal = NULL )
	{
		return $this->db->select('done')->get_where($this->db_table('form_survey') . $survey_id . '_done',[
			'user_id' => UUID_TO_BIN($this->auth_user_id),
			'fiscal'  => $fiscal
		])->row();
	}
	
	public function save_form_done( $survey_id = NULL, $form_id = NULL, $fiscal = NULL )
	{
		$binding = [];
		$answers = [];
		$binding[] = $user_id = UUID_TO_BIN($this->auth_user_id);
		// $binding[] = UUID_TO_BIN($form_id);
		$sql = '
			SELECT *
			FROM '. $this->db_table('form_survey') . $survey_id .'
			WHERE
				`user_id` = ?';
				
		// $sql = '
			// SELECT *
			// FROM '. $this->db_table('form_survey') . $survey_id .'
			// WHERE
				// `company_id` = ? AND
				// `page_id` IN(
					// SELECT `page_id` FROM '. $this->db_table('form_pages') .' WHERE `form_id` = ?
				// )';
		
		$q = $this->db->query($sql, $binding);
		// var_dump($q->num_rows());exit;
		if( $q->num_rows() !== 0 )
		{
			// var_dump($q->result());
			$answers = $q->result();
		}
		
		if( ! empty($answers) )
		{
			if( $this->db->insert_batch($this->db_table('form_survey') . $survey_id . '_' . $fiscal, $answers) )
			{
				return $this->db->insert($this->db_table('form_survey') . $survey_id . '_done',[
					'user_id' => $user_id,
					'fiscal'  => $fiscal,
					'done'    => date('Y-m-d H:i:s'),
				]);
			}
			return FALSE;
		}
		
		return TRUE;
	}
	
	public function save_form_done_kiv( $survey_id = NULL, $form_id = NULL, $fiscal = NULL )
	{
		return $this->db->insert($this->db_table('form_survey') . $survey_id . '_done',[
			'company_id' => UUID_TO_BIN($this->auth_company_id),
			'fiscal'     => $fiscal,
			'done'       => date('Y-m-d H:i:s'),
		]);
	}
	
	public function save_form( $survey_id = NULL, $page_id = NULL, $all = array(), $field = array() )
	{
		$data = array();
		$delete = array();
		$company_id = UUID_TO_BIN($this->auth_company_id);
		$page_id    = UUID_TO_BIN($page_id);
		
		if( !empty($all) && $this->input->method(TRUE) === 'POST' )
		{
			foreach($all as $id)
			{
				$post = $this->input->post('form_' . $id);
				if(is_array($post))
					$post = json_encode($post);
				
				$dbid = UUID_TO_BIN($id);
				// $dbid = $id;
				
				// $delete[] = $dbid;
				
				if( !empty($post) )
				{
					$data[] = array(
						'company_id'  => $company_id,
						'page_id'     => $page_id,
						'question_id' => $dbid,
						'answer'      => $post
					);
				}
			}
		}
		else
		{
			return FALSE;
		}
		
		$q = $this->db
				->where('company_id',$company_id)
				->where('page_id',$page_id)
				->delete($this->db_table('form_survey') . $survey_id);
		
		if(!$q)
			return FALSE;
			
		if( ! empty($data) )
			return $this->db->insert_batch($this->db_table('form_survey') . $survey_id, $data);
		
		return TRUE;
	}
	
	public function save_form_flex( $survey_id = NULL, $page_id = NULL, $all = array(), $field = array() )
	{
		$data = array();
		$delete = array();
		$user_id = UUID_TO_BIN($this->auth_user_id);
		$page_id = UUID_TO_BIN($page_id);
		
		if( !empty($all) && $this->input->method(TRUE) === 'POST' )
		{
			foreach($all as $id)
			{
				$post = $this->input->post('form_' . $id);
				if(is_array($post))
					$post = json_encode($post);
				
				$dbid = UUID_TO_BIN($id);
				// $dbid = $id;
				
				// $delete[] = $dbid;
				
				if( !empty($post) )
				{
					$data[] = array(
						'user_id'     => $user_id,
						'page_id'     => $page_id,
						'question_id' => $dbid,
						'answer'      => $post
					);
				}
			}
		}
		else
		{
			return FALSE;
		}
		
		$q = $this->db
				->where('user_id',$user_id)
				->where('page_id',$page_id)
				->delete($this->db_table('form_survey') . $survey_id);
		
		if(!$q)
			return FALSE;
			
		if( ! empty($data) )
			return $this->db->insert_batch($this->db_table('form_survey') . $survey_id, $data);
		
		return TRUE;
	}
	
	public function get_formUpload( $survey_id, $question_id )
	{
		$table   = 'form_survey_' . $survey_id . '_attachment';
		$uploads = array();
		
		$q = $this->db
				->select('document_id')
				->where('document_id !=', NULL)
				->where('question_id',UUID_TO_BIN($question_id))
				->get($table);
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $upload)
			{
				$uploads[] = BIN_TO_UUID($upload->document_id);
			}
		}
		
		return $uploads;
	}
	
	public function formUpload( $survey_id, $question_id )
	{
		$table = 'form_survey_' . $survey_id . '_attachment';
		$question_id = UUID_TO_BIN($question_id);
		
		$this->db->delete($table,array('question_id' => $question_id));
		
		$tree_documents = $this->_get_tree_documents();
		if( empty($tree_documents) ) { return TRUE; }
		
		$data = array();
		$date = date('Y-m-d H:i:s');
		foreach($tree_documents as $document_version)
		{
			list($document_id,$version) = explode('_',$document_version);
			$data[] = array(
				'question_id'   => $question_id,
				'attachment_id' => UUID_TO_BIN(UUIDv4()),
				'document_id'   => UUID_TO_BIN($document_id),
				'uploaded_on'   => $date,
				'file_name'     => '',
				'file_ext'      => '',
			);
		}

		if( ! empty($data) )
			return $this->db->insert_batch($table,$data);
		
		return FALSE;
	}
	
	private function _get_max_sort_option( $question_id, $child = FALSE )
	{
		$query = '
			SELECT count(`question_id`) as sort 
			FROM `' . $this->db_table('form_page_question_options') . '` 
			WHERE `question_id`=? ';
		
		return $this->db->query($query,
			array($question_id)
		)->row()->sort;
	}
	
	private function _get_max_sort_question( $page_id, $child = FALSE )
	{
		$query = '
			SELECT count(`page_id`) as sort 
			FROM `' . $this->db_table('form_page_questions') . '` 
			WHERE `page_id`=? ';
		if( $child )
			$query .= 'AND `parent_id` IS NOT NULL';
		else
			$query .= 'AND `parent_id` IS NULL';
		
		return $this->db->query($query,
			array($page_id)
		)->row()->sort;
	}
	
	private function _get_max_sort_by_parent_id( $parent_id )
	{
		$query = '
			SELECT count(`parent_id`) as sort 
			FROM `' . $this->db_table('form_page_questions') . '` 
			WHERE `parent_id`=? ';
		
		return $this->db->query($query,
			array($parent_id)
		)->row()->sort;
	}
	
	public function get( $form_id )
	{
		$form    = NULL;
		$form_id = UUID_TO_BIN($form_id);
		
		$q = $this->db
				->where('form_id',$form_id)
				->limit(1)
				->get($this->db_table('forms'));
		if( $q->num_rows() === 1)
		{
			$form = $q->row();
			$form->form_id    = BIN_TO_UUID($form->form_id);
			$form->company_id = BIN_TO_UUID($form->company_id);
		}
		return $form;
	}
	
	public function get_page( $page_id )
	{
		$form    = NULL;
		$page_id = UUID_TO_BIN($page_id);
		
		$q = $this->db
				->where('page_id',$page_id)
				->limit(1)
				->get($this->db_table('form_pages'));
		if( $q->num_rows() === 1)
		{
			$form = $q->row();
			$form->form_id   = BIN_TO_UUID($form->form_id);
			$form->page_id   = BIN_TO_UUID($form->page_id);
			$form->parent_id = BIN_TO_UUID($form->parent_id);
		}
		return $form;
	}

	public function get_pages( $ids = [] )
	{
		if( empty($ids) ) { return NULL; }

		$pages = NULL;
		$page_id = [];
		foreach($ids as $id)
		{
			$page_id[] = UUID_TO_BIN($id);
		}
		
		$q = $this->db
				->where_in('page_id',$page_id)
				->get($this->db_table('form_pages'));
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $page)
			{
				$page->form_id   = BIN_TO_UUID($page->form_id);
				$page->page_id   = BIN_TO_UUID($page->page_id);
				$page->parent_id = BIN_TO_UUID($page->parent_id);
				$pages[$page->page_id] = $page;
			}
		}
		return $pages;
	}
	
	public function get_question( $question_id )
	{
		$form    = NULL;
		$question_id = UUID_TO_BIN($question_id);
		
		$q = $this->db
				->where('question_id',$question_id)
				->limit(1)
				->get($this->db_table('form_page_questions'));
		if( $q->num_rows() === 1)
		{
			$form = $q->row();
			$form->page_id     = BIN_TO_UUID($form->page_id);
			$form->question_id = BIN_TO_UUID($form->question_id);
			$form->parent_id   = BIN_TO_UUID($form->parent_id);
		}
		return $form;
	}
	
	public function get_option( $option_id )
	{
		$form    = NULL;
		$option_id = UUID_TO_BIN($option_id);
		
		$q = $this->db
				->where('option_id',$option_id)
				->limit(1)
				->get($this->db_table('form_page_question_options'));
		if( $q->num_rows() === 1)
		{
			$form = $q->row();
			$form->question_id = BIN_TO_UUID($form->question_id);
			$form->option_id   = BIN_TO_UUID($form->option_id);
		}
		return $form;
	}
	
	public function get_answers( $survey_id = NULL, $page_id = NULL, $question_id = NULL )
	{
		$forms       = array();
		$company_id  = UUID_TO_BIN($this->auth_company_id);
		$page_id     = UUID_TO_BIN($page_id);
		$question_id = UUID_TO_BIN($question_id);
		
		if( ! empty($question_id) )
		{
			$this->db->where('question_id', $question_id);
		}
		
		if( ! empty($page_id) )
		{
			$this->db->where('page_id', $page_id);
		}
		
		$q = $this->db
				->select('page_id,question_id,answer')
				->where('company_id',$company_id)
				->get($this->db_table('form_survey') . $survey_id);
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $form)
			{
				$form->page_id     = BIN_TO_UUID($form->page_id);
				$form->question_id = BIN_TO_UUID($form->question_id);
				if($form->question_id === NULL)
				{
					$forms[$form->page_id] = $form->answer;
				}
				else
				{
					$forms[$form->question_id] = $form->answer;
				}
			}
		}
		
		return $forms;
	}
	
	public function get_answers_flex( $survey_id = NULL, $page_id = NULL )
	{
		$forms      = array();
		$user_id = UUID_TO_BIN($this->auth_user_id);
		$page_id = UUID_TO_BIN($page_id);
		
		$q = $this->db
				->select('question_id,answer')
				->where('user_id',$user_id)
				->where('page_id',$page_id)
				->get($this->db_table('form_survey') . $survey_id);
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $form)
			{
				$form->question_id   = BIN_TO_UUID($form->question_id);
				$forms[$form->question_id] = $form->answer;
			}
		}
		
		return $forms;
	}

	public function get_answers_flex_by_fiscal( $survey_id = NULL, $fiscal = NULL, $user_id = NULL)
	{
		$forms   = array();
		$user_id = UUID_TO_BIN($user_id);
		
		$q = $this->db
				->select('question_id,answer')
				->where('user_id',$user_id)
				->get($this->db_table('form_survey') . $survey_id . '_' . $fiscal);
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $form)
			{
				$form->question_id = BIN_TO_UUID($form->question_id);
				$forms[$form->question_id] = $form->answer;
			}
		}
		
		return $forms;
	}
	
	public function get_all( $type = [] )
	{
		$forms      = array();
		$company_id = UUID_TO_BIN($this->auth_company_id);

		if( ! empty($type) )
		{
			$this->db->where_in('type', $type);
		}
		
		$q = $this->db
				->select('form_id,name,type,global')
				->where('company_id',$company_id)
				->order_by('name', 'ASC')
				->get($this->db_table('forms'));
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $form)
			{
				$form->form_id = BIN_TO_UUID($form->form_id);
				$forms[$form->form_id] = $form;
			}
		}
		
		return $forms;
	}
	
	public function get_form_pages_by_form( $form_id = NULL )
	{
		$forms = array();
		if( is_array($form_id) )
		{
			$tmp = [];
			foreach($form_id as $id)
			{
				$tmp[] = UUID_TO_BIN($id);
			}
			$this->db->where_in('form_id', $tmp);
		}
		else
		{
			$this->db->where('form_id', UUID_TO_BIN($form_id));
		}
		
		$q = $this->db
				->select('form_id,page_id,parent_id,name,type')
				->where('parent_id',NULL)
				->order_by('00+`name`', 'ASC', FALSE)
				->get($this->db_table('form_pages'));
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $form)
			{
				$form->form_id   = BIN_TO_UUID($form->form_id);
				$form->page_id   = BIN_TO_UUID($form->page_id);
				$form->parent_id = BIN_TO_UUID($form->parent_id);
				// $form->name      = preg_replace('/^[0-9\.\s]+/u', '',$form->name);
				$forms[$form->page_id] = $form;
			}
		}
		
		return $forms;
	}
	
	public function get_form_pages_by_parent( $parent_id = NULL )
	{
		$forms     = array();
		if( $parent_id )
			$parent_id = UUID_TO_BIN($parent_id);
		
		$q = $this->db
				->select('form_id,page_id,parent_id,name,type')
				->where('parent_id',$parent_id)
				->order_by('00+`name`', 'ASC', FALSE)
				->get($this->db_table('form_pages'));
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $form)
			{
				$form->form_id   = BIN_TO_UUID($form->form_id);
				$form->page_id   = BIN_TO_UUID($form->page_id);
				$form->parent_id = BIN_TO_UUID($form->parent_id);
				// $form->name      = preg_replace('/^[0-9\.\s]+/u', '',$form->name);
				$forms[] = $form;
			}
		}
		
		return $forms;
	}
	
	public function get_page_questions_by_page_id( $page_id = NULL )
	{
		$forms     = array();
		if( $page_id )
			$page_id = UUID_TO_BIN($page_id);
		
		$q = $this->db
				->where('page_id',$page_id)
				->where('parent_id',NULL)
				->order_by('00+`sort`', 'ASC', FALSE)
				->get($this->db_table('form_page_questions'));
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $form)
			{
				$form->page_id     = BIN_TO_UUID($form->page_id);
				$form->question_id = BIN_TO_UUID($form->question_id);
				$form->parent_id   = BIN_TO_UUID($form->parent_id);
				// $form->name      = preg_replace('/^[0-9\.\s]+/u', '',$form->name);
				$forms[$form->question_id] = $form;
			}
		}
		
		return $forms;
	}
	
	public function get_page_questions_by_parent_id( $parent_id = NULL )
	{
		$forms     = array();
		if( $parent_id )
			$parent_id = UUID_TO_BIN($parent_id);
		
		$q = $this->db
				->where('parent_id',$parent_id)
				->order_by('00+`sort`', 'ASC', FALSE)
				->get($this->db_table('form_page_questions'));
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $form)
			{
				$form->page_id     = BIN_TO_UUID($form->page_id);
				$form->question_id = BIN_TO_UUID($form->question_id);
				$form->parent_id   = BIN_TO_UUID($form->parent_id);
				// $form->name      = preg_replace('/^[0-9\.\s]+/u', '',$form->name);
				$forms[] = $form;
			}
		}
		
		return $forms;
	}
	
	public function get_page_questions_parents( $page_id = NULL )
	{
		$forms = array(0 => lang('no'));
		
		if( $page_id )
			$page_id = UUID_TO_BIN($page_id);
		
		$q = $this->db
				->where('page_id',$page_id)
				->where('parent_id',NULL)
				->order_by('00+`sort`', 'ASC', FALSE)
				->get($this->db_table('form_page_questions'));
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $form)
			{
				$form->question_id = BIN_TO_UUID($form->question_id);
				$forms[$form->question_id] = $form->name;
			}
		}
		
		return $forms;
	}
	
	public function get_page_questions_children_by_page_id( $questions = NULL )
	{
		if( empty($questions) ) { return NULL; }
		
		$forms = [];
		$ids = [];
		foreach($questions as $question_id)
		{
			$ids[] = UUID_TO_BIN($question_id);
		}
		
		$q = $this->db
				->where_in('parent_id',$ids)
				->order_by('00+`sort`', 'ASC', FALSE)
				->get($this->db_table('form_page_questions'));
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $form)
			{
				// var_dump($form);exit;
				$form->page_id     = BIN_TO_UUID($form->page_id);
				$form->question_id = BIN_TO_UUID($form->question_id);
				$form->parent_id   = BIN_TO_UUID($form->parent_id);
				$forms[$form->parent_id][$form->question_id] = $form;
			}
		}
		
		return $forms;
	}
	
	public function get_question_options_by_question_id( $question_id = NULL )
	{
		$forms     = array();
		if( $question_id )
			$question_id = UUID_TO_BIN($question_id);
		
		$q = $this->db
				->where('question_id',$question_id)
				->order_by('00+`sort`', 'ASC', FALSE)
				->get($this->db_table('form_page_question_options'));
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $form)
			{
				$form->question_id = BIN_TO_UUID($form->question_id);
				$form->option_id   = BIN_TO_UUID($form->option_id);
				// $form->name      = preg_replace('/^[0-9\.\s]+/u', '',$form->name);
				$forms[] = $form;
			}
		}
		
		return $forms;
	}
	
	public function get_survey( $survey_id )
	{
		$survey      = NULL;
		$survey_id = UUID_TO_BIN($survey_id);
		
		$q = $this->db
				->where('survey_id',$survey_id)
				->limit(1)
				->get($this->db_table('surveys'));
		if( $q->num_rows() === 1)
		{
			$survey = $q->row();
			$survey->survey_id  = BIN_TO_UUID($survey->survey_id);
			$survey->page_id    = BIN_TO_UUID($survey->page_id);
			$survey->parent_id  = BIN_TO_UUID($survey->parent_id);
			$survey->created_by = BIN_TO_UUID($survey->created_by);
			$survey->edited_by  = BIN_TO_UUID($survey->edited_by);
		}
		return $survey;
	}
	
	public function get_all_surveys($page_id = NULL)
	{
		$surveys    = array();
		$page_id    = UUID_TO_BIN($page_id);
		$company_id = UUID_TO_BIN($this->auth_company_id);
		
		$q = $this->db
				->select('survey_id,parent_id,survey_date,created_by,created_date,edited_by,edited_date')
				->where('page_id',$page_id)
				->where('company_id',$company_id)
				->where('parent_id',NULL)
				->order_by('created_date', 'DESC')
				->get($this->db_table('surveys'));
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $survey)
			{
				$survey->survey_id  = BIN_TO_UUID($survey->survey_id);
				$survey->parent_id  = BIN_TO_UUID($survey->parent_id);
				$survey->created_by = BIN_TO_UUID($survey->created_by);
				$survey->edited_by  = BIN_TO_UUID($survey->edited_by);
				$surveys[$survey->survey_id] = $survey;
			}
		}
		
		return $surveys;
	}

	public function get_all_survey_dates($page_id = NULL)
	{
		$surveys = array();
		$page_id = UUID_TO_BIN($page_id);
		
		$q = $this->db
				->select('date,done')
				->where('page_id',$page_id)
				->order_by('date', 'DESC')
				->get($this->db_table('survey_dates'));
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $survey)
			{
				$surveys[] = $survey->date;
			}
		}
		
		return $surveys;
	}

	public function update_survey_dates( $data )
	{
		if( empty($data) ) { return NULL; }
		foreach($data as $d)
		{
			$this->db->update($this->db_table('survey_dates'), ['done' => '1'],[
				'page_id' => $d->page_id,
				'date'    => $d->date
			]);
		}
		return TRUE;
	}
	
	public function save_surveys($survey_id, $page_id, $survey_date, $parent_id = NULL)
	{
		$this->data['company_id']   = UUID_TO_BIN($this->auth_company_id);
		$this->data['survey_id']    = UUID_TO_BIN($survey_id);
		$this->data['page_id']      = UUID_TO_BIN($page_id);
		$this->data['survey_date']  = $survey_date;
		$this->data['created_by']   = UUID_TO_BIN($this->auth_user_id);
		$this->data['created_date'] = date('Y-m-d H:i:s');
		
		if( $parent_id !== NULL )
		{
			$survey = $this->get_survey( $parent_id );
			$this->data['created_by']   = UUID_TO_BIN($survey->created_by);
			$this->data['created_date'] = $survey->created_date;
			$this->data['edited_by']    = UUID_TO_BIN($this->auth_user_id);
			$this->data['edited_date']  = date('Y-m-d H:i:s');
			
			$this->db->update($this->db_table('surveys'), ['parent_id' => UUID_TO_BIN($survey_id)], [
				'survey_id' => UUID_TO_BIN($parent_id)
			]);
			
			$this->db->update($this->db_table('surveys'), ['parent_id' => UUID_TO_BIN($survey_id)], [
				'parent_id' => UUID_TO_BIN($parent_id)
			]);
		}

		return $this->db->insert($this->db_table('surveys'),$this->data);
	}
	
	public function configure_survey($page_id)
	{
		
	}
	
	public function get_menu_structure( $form_id )
	{
		$forms     = array();
		if( $form_id )
			$form_id = UUID_TO_BIN($form_id);
		
		$q = $this->db
				// ->select('form_id,page_id,parent_id,name,type')
				->where('form_id',$form_id)
				->order_by('00+`name`', 'ASC', FALSE)
				->get($this->db_table('form_pages'));
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $form)
			{
				$form->form_id   = BIN_TO_UUID($form->form_id);
				$form->page_id   = BIN_TO_UUID($form->page_id);
				$form->menu_id   = $form->page_id;
				$form->parent_id = BIN_TO_UUID($form->parent_id);
				$form->type      = $form->type ? 0 : 1;
				// $form->name      = preg_replace('/^[0-9\.\s]+/u', '',$form->name);
				
				if($form->parent_id)
					$forms['structure'][$form->parent_id][$form->page_id] = $form;
				else
					$forms['structure'][$form->form_id][$form->page_id] = $form;
				
				$forms['all'][$form->page_id] = $form;
			}
		}
		
		return $forms;
	}
	
	public function get_question_structure( $page_id )
	{
		$forms = array(
			'structure' => array(),
			'options'   => array(),
			'field'     => array(),
			'all'       => array(),
		);
		$questions_bin = array();
		
		if( $page_id )
			$page_id = UUID_TO_BIN($page_id);
		
		$q = $this->db
				// ->select('form_id,page_id,parent_id,name,type')
				->where('page_id',$page_id)
				->order_by('00+`sort`', 'ASC', FALSE)
				->get($this->db_table('form_page_questions'));
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $form)
			{
				if( in_array($form->field, array('dropdown', 'checkbox', 'radio')) )
					$questions_bin[] = $form->question_id;
				$form->page_id     = BIN_TO_UUID($form->page_id);
				$form->question_id = BIN_TO_UUID($form->question_id);
				$form->parent_id   = BIN_TO_UUID($form->parent_id);
				
				if($form->parent_id)
					$forms['structure'][$form->parent_id][$form->question_id] = $form;
				else
					$forms['structure'][0][$form->question_id] = $form;
				
				$forms['field'][$form->field][] = $form->question_id;
				$forms['all'][] = $form->question_id;
				
			}
		}
		
		if( !empty($questions_bin) )
			$forms['options'] = $this->_get_option_structure($questions_bin);
		
		return $forms;
	}

	public function get_question_structures( $ids = [] )
	{
		if( empty($ids) ) { return NULL; }

		$forms = array(
			'structure' => array(),
			'options'   => array(),
			'field'     => array(),
			'all'       => array(),
		);
		$questions_bin = array();
		
		$page_id = [];
		foreach($ids as $id)
		{
			$page_id[] = UUID_TO_BIN($id);
		}
		
		$q = $this->db
				->where_in('page_id',$page_id)
				->order_by('00+`sort`', 'ASC', FALSE)
				->get($this->db_table('form_page_questions'));
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $form)
			{
				if( in_array($form->field, array('dropdown', 'checkbox', 'radio')) )
					$questions_bin[] = $form->question_id;
				$form->page_id     = BIN_TO_UUID($form->page_id);
				$form->question_id = BIN_TO_UUID($form->question_id);
				$form->parent_id   = BIN_TO_UUID($form->parent_id);
				
				if($form->parent_id)
					$forms['structure'][$form->page_id][$form->parent_id][$form->question_id] = $form;
				else
					$forms['structure'][$form->page_id][0][$form->question_id] = $form;
				
				$forms['field'][$form->page_id][$form->field][] = $form->question_id;
				$forms['all'][$form->page_id][] = $form->question_id;
				
			}
		}
		
		if( !empty($questions_bin) )
			$forms['options'] = $this->_get_option_structure($questions_bin);
		
		return $forms;
	}
	
	private function _get_option_structure( $questions_bin )
	{
		$forms = array();
		
		$q = $this->db
				->where_in('question_id',$questions_bin)
				->order_by('00+`sort`', 'ASC', FALSE)
				->get($this->db_table('form_page_question_options'));
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $form)
			{
				$form->question_id = BIN_TO_UUID($form->question_id);
				$form->option_id   = BIN_TO_UUID($form->option_id);
				
				$forms[$form->question_id][$form->option_id] = $form;
			}
		}
		// var_dump($questions_bin);exit;
		return $forms;
	}
	
	public function save( $table, $column, $id, $items, $delete_items = array(), $uuid = TRUE )
	{
		if( $this->remove($table,$column,$id,$delete_items,$uuid) === FALSE) { return FALSE; }
		if( empty($items) ) { return TRUE; }
		$data = array();
		foreach($items as $item)
		{
			$data[] = array(
				$column   => $uuid ? UUID_TO_BIN($item) : $item,
				'page_id' => UUID_TO_BIN($id)
			);
		}

		if( ! empty($data) )
			return $this->db->insert_batch($this->db_table($table),$data);
		
		return FALSE;
	}
	
	public function remove( $table, $column, $id, $delete_items, $uuid )
	{
		if( empty($delete_items) && ! $uuid ) { return TRUE; }
		$delete = array();
		if( !empty($delete_items) )
		{
			foreach($delete_items as $item_id)
			{
				$delete[] = $uuid ? UUID_TO_BIN($item_id) : $item_id;
			}
			$this->db->where_in($column, $delete);
		}
		return $this->db->delete($this->db_table($table),array('page_id' => UUID_TO_BIN($id)));
	}

	public function checklist_stats() {
		$sql = "SELECT count(*) as count FROM `forms` where type = 'checklist'";
		$res = [];
		$res['checklist'] = $this->db->query($sql)->result()[0]->count;
		$sql = "SELECT count(*) as count FROM surveys 
		INNER JOIN
		form_pages AS fp
				ON fp.page_id=surveys.page_id
		INNER JOIN 
		forms ON fp.form_id = forms.form_id
		where surveys.parent_id is null and created_date > '" . date("Y-m-d", strtotime("-12 month")) . "'";
		$res['checklists_12_months'] = $this->db->query($sql)->result()[0]->count;
		return $res;
	}

	public function checklist_report()
	{
		$sql = "SELECT
				created_date as date,
				created_by as user,
				forms.name as form
			FROM surveys 
			INNER JOIN
			form_pages AS fp
					ON fp.page_id=surveys.page_id
			INNER JOIN 
			forms ON fp.form_id = forms.form_id
			where surveys.parent_id is null
		";
		$q = $this->db->query($sql);
		$res = $q->result();
		foreach($res as $checklist)
		{
			$checklist->user   = BIN_TO_UUID($checklist->user);
		}
		return $res;
	}
}
