<?php
class Report_model extends MY_Model {

	public function documents_valid_date($individual = TRUE)
	{
		$company_id = UUID_TO_BIN($this->auth_company_id);
		$documents = [];
		$bindings  = [
			$company_id,
		];

		if( $individual )
			$bindings[] = UUID_TO_BIN($this->auth_user_id);

		$q = $this->db->query(sprintf("
				SELECT
					`document_id`,
					`name` as document_name,
					`description` as document_description,
					`valid_until` as document_valid_until
				FROM
					`documents`
				WHERE
					`reminder` != 0 AND
					`valid_until` < CURDATE()+INTERVAL `reminder` MONTH AND
					`valid_until` >= CURDATE() AND
					`status` = 'published' AND
					`folder_id` IN
					(
						SELECT
							`folder_id`
						FROM
							`folders` as f
						INNER JOIN
							`menus` as m
								ON f.`menu_id` = m.`menu_id`
						WHERE
							m.`company_id` = ?
					)
					%s
				GROUP BY
						`document_id`
				ORDER BY
						`valid_until` ASC
				",
				($individual ? ' AND `owner` = ? ' : '')
			), $bindings
		);

		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $document)
			{
				$document->document_id = BIN_TO_UUID($document->document_id);
				$documents[] = $document;
			}
		}

		return $documents;
	}

	public function documents_past_valid_date($individual = TRUE)
	{
		$company_id = UUID_TO_BIN($this->auth_company_id);
		$documents = [];
		$bindings  = [
			$company_id,
		];

		if( $individual )
			$bindings[] = UUID_TO_BIN($this->auth_user_id);

		$q = $this->db->query(sprintf("
				SELECT
					`document_id`,
					`name` as document_name,
					`description` as document_description,
					`valid_until` as document_valid_until
				FROM
					`documents`
				WHERE
					`reminder` != 0 AND
					`valid_until` < CURDATE() AND
					`status` = 'published' AND
					`folder_id` IN
					(
						SELECT
							`folder_id`
						FROM
							`folders` as f
						INNER JOIN
							`menus` as m
								ON f.`menu_id` = m.`menu_id`
						WHERE
							m.`company_id` = ?
					)
					%s
				GROUP BY
						`document_id`
				ORDER BY
						`valid_until` ASC
				",
				($individual ? ' AND `owner` = ? ' : '')
			), $bindings
		);

		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $document)
			{
				$document->document_id = BIN_TO_UUID($document->document_id);
				$documents[] = $document;
			}
		}

		return $documents;
	}

	// @STEP2: Save in user_messages
	function deviationNextPage($deviation_two, $deviation_three)
	{
		// var_dump($departments_id);exit;
		if( empty($deviation_two) && empty($deviation_three) ) { return []; }

		$bindings   = [];
		$de_two     = [];
		$de_three   = [];
		$deviations = [];
		if( ! empty($deviation_two) )
		{
			foreach($deviation_two as $dep):
				$de_two[] = UUID_TO_BIN($dep);
			endforeach;
		}

		if( ! empty($deviation_three) )
		{
			foreach($deviation_three as $dep):
				$de_three[] = UUID_TO_BIN($dep);
			endforeach;
		}

		$query ="SELECT
					d.reg_date_one, d.regby_two, d.regby_three, a.a_id, a.answer as title
				FROM
					deviation AS d
				LEFT JOIN
					deviation_department AS de
						ON de.a_id=d.a_id
				LEFT JOIN
					deviation_answers AS a
						ON a.a_id=d.a_id
				WHERE
					a.df_id = ? AND
					d.company_id = ? AND ";

		if( ! empty($deviation_two) && ! empty($deviation_three) )
		{
			$query .= '(';
		}

		if( ! empty($deviation_two) )
		{
			$query .="(d.regby_two IS NULL AND de.de_id IN ?)";
		}

		if( ! empty($deviation_two) && ! empty($deviation_three) )
		{
			$query .= ' OR ';
		}

		if( ! empty($deviation_three) )
		{
			$query .="(d.regby_three IS NULL AND de.de_id IN ?)";
		}

		if( ! empty($deviation_two) && ! empty($deviation_three) )
		{
			$query .= ')';
		}

		$query .= "
				GROUP BY
					d.a_id
				ORDER BY
					d.reg_date_two DESC,
					d.reg_date_one DESC";

		$bindings[] = UUID_TO_BIN('9edbc3cc-2293-4bcf-9004-8e1fdd0e5252');
		$bindings[] = UUID_TO_BIN($this->auth_company_id);
		if( ! empty($deviation_two) )
			$bindings[] = $de_two;
		if( ! empty($deviation_three) )
			$bindings[] = $de_three;

		$q = $this->db->query($query, $bindings);

		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $deviation)
			{
				$deviation->a_id = BIN_TO_UUID($deviation->a_id);
				$deviation->regby_two = BIN_TO_UUID($deviation->regby_two);
				$deviation->regby_three = BIN_TO_UUID($deviation->regby_three);
				$deviations[] = $deviation;
			}
		}

		return $deviations;
	}

	// @STEP2: Save in user_messages
	public function eventanalysis($departments_id)
	{
		if( empty($departments_id) ) { return []; }

		$bindings    = [];
		$departments = [];
		$deviations  = [];
		foreach($departments_id as $dep):
			$departments[] = UUID_TO_BIN($dep);
		endforeach;

		$query ="SELECT
					a.a_id, a.answer as title, d.reg_date_one as reg_date_one
				FROM
					deviation AS d
				LEFT JOIN
					deviation_department AS de
						ON de.a_id=d.a_id
				LEFT JOIN
					deviation_answers AS a
						ON a.a_id=d.a_id
				WHERE
					d.eventanalysis IS NOT NULL AND
					d.regby_three IS NOT NULL AND
					de.de_id IN ? AND
					a.df_id = ? AND
					d.company_id = ?
				GROUP BY
					d.a_id";

		$bindings[] = $departments;
		$bindings[] = UUID_TO_BIN('9edbc3cc-2293-4bcf-9004-8e1fdd0e5252');
		$bindings[] = UUID_TO_BIN($this->auth_company_id);

		$q = $this->db->query($query, $bindings);

		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $deviation)
			{
				$deviation->a_id = BIN_TO_UUID($deviation->a_id);
				$deviations[] = $deviation;
			}
		}

		return $deviations;
	}

	public function eventanalysis_by_id($deviation_id)
	{
		if( empty($deviation_id) ) { return []; }

		$bindings      = [];
		$deviations_id = [];
		$deviations    = [];
		foreach($deviation_id as $dev):
			$deviations_id[] = UUID_TO_BIN($dev);
		endforeach;

		$query ="SELECT
					a.a_id, a.answer as title, d.reg_date_one as reg_date_one
				FROM
					deviation AS d
				LEFT JOIN
					deviation_answers AS a
						ON a.a_id=d.a_id
				WHERE
					a.a_id IN ? AND
					a.df_id = ? AND
					d.company_id = ?
				GROUP BY
					d.a_id";

		$bindings[] = $deviations_id;
		$bindings[] = UUID_TO_BIN('9edbc3cc-2293-4bcf-9004-8e1fdd0e5252');
		$bindings[] = UUID_TO_BIN($this->auth_company_id);

		$q = $this->db->query($query, $bindings);

		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $deviation)
			{
				$deviation->a_id = BIN_TO_UUID($deviation->a_id);
				$deviations[] = $deviation;
			}
		}

		return $deviations;
	}

	public function education( $position = [], $education = [] )
	{
		$user_id_bin = UUID_TO_BIN($this->auth_user_id);

		$report = [
			'todo' => [],
			'done' => []
		];
		$bindings = [];
		if( empty($position) )
			$position = [];
		if( empty($education) )
			$education = [];

		$bindings[] = $user_id_bin;

		// $query ="SELECT
					// e.document_id, e.version, MAX(d.done) AS done, MAX(d.version) AS version_done
				// FROM
					// document_education AS e
				// LEFT JOIN
					// document_education_done AS d
						// ON e.document_id = d.document_id AND
						// d.user_id = ? AND
						// e.version >= d.version
				// WHERE ";
		$query ="SELECT
					e.document_id, e.version, MAX(d.done) AS done, MAX(d.version) AS version_done
				FROM
					document_education AS e
				LEFT JOIN
					document_education_done AS d
						ON e.document_id = d.document_id AND
						d.user_id = ?
				WHERE ";
		if( ! empty($position) ) {
			$query .= "group_id IN(SELECT education_id FROM document_education_group WHERE group_id IN ?) OR ";
			$bindings[] = $position;
		}
		if( ! empty($education) ) {
			$query .= "group_id IN ? OR ";
			$bindings[] = $education;
		}
		$query .= "e.user_id = ? ";
		$query .= "GROUP BY e.document_id";

		$bindings[] = $user_id_bin;

		$q = $this->db->query($query, $bindings);

		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $e)
			{
				$e->document_id = BIN_TO_UUID($e->document_id);

				if( $e->version_done !== $e->version )
					$report['todo'][$e->document_id] = $e;
				if( ! empty($e->done) && $e->version_done === $e->version )
					$report['done'][$e->document_id] = $e;
			}
		}

		return $report;
	}

	public function education_done()
	{
		$report = [
			'todo'        => [],
			'done'        => [],
			'education'   => [],
			'document_id' => [],
			'documents'   => []
		];
		$bindings = [];
		$bindings[] = UUID_TO_BIN($this->auth_company_id);
		$bindings[] = UUID_TO_BIN($this->auth_company_id);
		$bindings[] = UUID_TO_BIN($this->auth_company_id);

		$query =
		"SELECT * FROM (
			SELECT de.document_id, de.version, MAX(ded.done) AS done, MAX(ded.version) AS version_done, ug.user_id
			FROM `groups` AS g
			INNER JOIN `document_education` AS de ON de.group_id = g.group_id
			INNER JOIN `document_education_group` AS deg ON deg.education_id=de.group_id
			INNER JOIN `user_group` AS ug ON ug.group_id=deg.group_id
			INNER JOIN `users` ON users.user_id = ug.user_id
			LEFT JOIN `document_education_done` as ded ON ded.user_id = ug.user_id AND de.document_id = ded.document_id
			WHERE g.company_id = ?
			and users.hidden = '0'
			GROUP BY ug.user_id, de.document_id
			ORDER BY ded.version DESC
		) as education
		UNION ALL (
			SELECT de.document_id, de.version, MAX(ded.done) AS done, MAX(ded.version) AS version_done, ug.user_id
			FROM `groups` AS g
			INNER JOIN `document_education` AS de ON de.group_id = g.group_id
			INNER JOIN `user_group` AS ug ON ug.group_id=de.group_id
			INNER JOIN `users` ON users.user_id = ug.user_id
			LEFT JOIN `document_education_done` as ded ON ded.user_id = ug.user_id AND de.document_id = ded.document_id
			WHERE g.company_id = ?
			and users.hidden = '0'
			GROUP BY ug.user_id, de.document_id
			ORDER BY ded.version DESC
		)
		UNION ALL (
			SELECT de.document_id, de.version, MAX(ded.done) AS done, MAX(ded.version) AS version_done, u.user_id
			FROM `users` AS u
			INNER JOIN `document_education` AS de ON de.user_id = u.user_id
			LEFT JOIN `document_education_done` as ded ON ded.user_id = de.user_id AND de.document_id = ded.document_id
			WHERE u.company_id = ?
			and u.hidden = '0'
			GROUP BY u.user_id, de.document_id
			ORDER BY ded.version DESC
		)
		";

		$q = $this->db->query($query, $bindings);

		$documents_id = [];
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $e)
			{
				if( $e->document_id === NULL )
				{
					continue;
				}
				// var_dump($e);
				$e->document_id = BIN_TO_UUID($e->document_id);
				$e->user_id     = BIN_TO_UUID($e->user_id);
				$report['education'][$e->user_id][$e->document_id] = $e;
				$report['document_id'][$e->document_id] = $e->version;
				
				if( $e->version_done !== $e->version )
				{
					$report['todo'][$e->user_id][$e->document_id] = $e;
					$report['documents'][$e->document_id][$e->user_id] = 'todo';
				}
				if( ! empty($e->done) && $e->version_done === $e->version ) 
				{
					$report['done'][$e->user_id][$e->document_id] = $e;
					$report['documents'][$e->document_id][$e->user_id] = 'done';
				}
			}
		}

		return $report;

		/*
		// Education group => groups
		SELECT de.*, ded.done, ded.version AS version_done FROM `groups` AS g
		INNER JOIN `document_education` AS de ON de.group_id = g.group_id
		INNER JOIN `document_education_group` AS deg ON deg.education_id=de.group_id
		INNER JOIN `user_group` AS ug ON ug.group_id=deg.group_id
		LEFT JOIN `document_education_done` as ded ON ded.user_id = ug.user_id
		ORDER BY ded.version DESC

		// Education group => user
		SELECT de.*, ded.done, ded.version AS version_done FROM `groups` AS g
		INNER JOIN `document_education` AS de ON de.group_id = g.group_id
		INNER JOIN `user_group` AS ug ON ug.group_id=de.group_id
		LEFT JOIN `document_education_done` as ded ON ded.user_id = ug.user_id
		ORDER BY ded.version DESC

		// Education user
		SELECT de.*, ded.done, ded.version AS version_done FROM `users` AS u
		INNER JOIN `document_education` AS de ON de.user_id = u.user_id
		LEFT JOIN `document_education_done` as ded ON ded.user_id = de.user_id
		ORDER BY ded.version DESC
		*/
	}

	public function checklist($position)
	{
		$checklists  = [
			'messages' => [],
			'dates' => []
		];
		$user_id     = UUID_TO_BIN($this->auth_user_id);
		$todays_date = date('Y-m-d');
		$query = "
				SELECT *
				FROM  `" . $this->db_table('survey_dates') . "`
				WHERE
				(
					`page_id` IN (
						SELECT `page_id`
						FROM   `" . $this->db_table('survey_group') . "`
						WHERE  `group_id` IN ?
						GROUP  BY `page_id`
					) OR
					`page_id` IN (
						SELECT `page_id`
						FROM   `" . $this->db_table('survey_user') . "`
						WHERE  `user_id` = ?
						GROUP  BY `page_id`
					)
				)
				AND `date` <= ?
				AND `done` = '0'
				ORDER BY
					`date` ASC
				";

		$q = $this->db->query($query, [$position,$user_id,$todays_date]);

		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $checklist)
			{
				$checklists['messages'][] = [
					'user_id'    => $user_id,
					'type'       => 'checklist',
					'type_id'    => $checklist->page_id,
					'action'     => 'create',
					'severity'   => 'warning',
					'created_at' => $todays_date,
					'comment'    => $checklist->date
				];

				$checklists['dates'][] = $checklist;
			}
		}

		return $checklists;
	}

	public function all_user_messages()
	{
		$messages = [
			'autosave' => [
				'warning' => [],
				'critical' => [],
			],
			'documents' => [
				'warning' => [],
				'critical' => [],
				'success' => [],
			],
			'eventanalysis' => [
				'warning' => [],
				'critical' => [],
			],
			'eventanalysis_actionlist' => [
				'warning' => [],
				'critical' => [],
			],
			'checklist' => [
				'warning' => [],
				'critical' => [],
			],
		];

		$q = $this->db
				->where('user_id', UUID_TO_BIN($this->auth_user_id))
				->get($this->db_table('user_messages'));
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $m)
			{
				$m->user_id = BIN_TO_UUID($m->user_id);
				$m->type_id = BIN_TO_UUID($m->type_id);

				if( $m->type === 'checklist')
					$messages[$m->type][$m->severity][$m->type_id . '_' . $m->comment] = $m;
				else
					$messages[$m->type][$m->severity][$m->type_id] = $m;
			}
		}

		return $messages;
	}
}
