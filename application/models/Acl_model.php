<?php
class Acl_model extends MY_Model {
	
	/**
	 * return array($group_id => array($action_id ...))
	**/
	public function _get_acl( $fields = array() )
	{
		$acl = array();
		foreach($fields as $field)
		{
			$post = $this->input->post($field);
			if( is_array($post) )
			{
				foreach($post as $p)
				{
					list($group_id, $action_id) = array_pad(explode('_', $p, 2), 2, FALSE);
					if($action_id === FALSE) { return FALSE; }
					$acl[$group_id][] = $action_id;
				}
			}
		}
		return $acl;
	}
	
	/*
	 *
	**/
	public function _acl_action_code_required( $acl, $acl_actions, $action_code)
	{
		if( empty($acl) OR empty($acl_actions) OR empty($action_code) ) { return NULL; }
		foreach($acl as $group_id =>  $actions)
		{
			foreach($actions as $row_id => $action)
			{
				if( !in_array($action, $acl_actions ) )
				{
					return FALSE;
				}
			}
			if( !in_array($acl_actions[$action_code], $actions) )
			{
				$acl[$group_id][] = $acl_actions[$action_code];
			}
		}
		return $acl;
	}
	
	public function _get_data_user_group( $user_id, $acl )
	{
		if( empty($user_id) OR empty($acl) ) { return NULL; }
		$data = array();
		
		foreach($acl as $group_id => $actions)
		{
			foreach($actions as $action_id)
			{
				$data[] = array(
					'user_id'   => UUID_TO_BIN($user_id),
					'group_id'  => UUID_TO_BIN($group_id),
					'action_id' => UUID_TO_BIN($action_id),
				);
			}
		}
		
		return $data;
	}
	
	public function _get_data_user_object( $user_id, $acl )
	{
		if( empty($user_id) OR empty($acl) ) { return NULL; }
		$data = array();
		
		foreach($acl as $object_id => $actions)
		{
			foreach($actions as $action_id)
			{
				$data[] = array(
					'user_id'   => UUID_TO_BIN($user_id),
					'object_id' => UUID_TO_BIN($object_id),
					'action_id' => UUID_TO_BIN($action_id),
				);
			}
		}
		
		return $data;
	}
	
	public function _get_data_group_object( $group_id, $acl )
	{
		if( empty($group_id) OR empty($acl) ) { return NULL; }
		$data = array();
		
		foreach($acl as $object_id => $actions)
		{
			foreach($actions as $action_id)
			{
				$data[] = array(
					'group_id'  => UUID_TO_BIN($group_id),
					'object_id' => UUID_TO_BIN($object_id),
					'action_id' => UUID_TO_BIN($action_id),
				);
			}
		}
		
		return $data;
	}

	public function _get_data_object( $acl_menu, $object_id, $users )
	{
		if( empty($object_id) OR empty($acl_menu) OR empty($users) ) { return NULL; }
		$data = array();
		
		foreach($acl_menu as $action_code => $action_id)
		{
			var_dump($action_code,$action_id);
			foreach($users as $user_id)
			{
				$data[] = array(
					'user_id'   => UUID_TO_BIN($user_id),
					'object_id' => UUID_TO_BIN($object_id),
					'action_id' => UUID_TO_BIN($action_id),
				);
			}
		}
		
		return $data;
	}
	
	// @STEP2: Not in use
	public function get_all( $category_code = NULL, $users_id = array(), $groups_id = array(), $objects_id = array(), $side = 'after', $escape = NULL )
	{
		$acl            = array();
		$users_id_bin   = array();
		$groups_id_bin  = array();
		$objects_id_bin = array();
		
		if( !empty($category_code) )
		{
			$this->db->like(
						$this->db_table('acl_categories_table') . '.category_code',
						$category_code,
						$side,
						$escape
					);
		}
		
		if( !empty($users_id) )
		{
			foreach($users_id AS $user_id)
			{
				$users_id_bin[] = UUID_TO_BIN($user_id);
			}
			$this->db->where_in('user_id',$users_id_bin);
		}
		
		if( !empty($groups_id) )
		{
			foreach($groups_id AS $group_id)
			{
				$groups_id_bin[] = UUID_TO_BIN($group_id);
			}
			$this->db->or_where_in('group_id',$groups_id_bin);
		}
		
		if( !empty($objects_id) )
		{
			foreach($objects_id AS $object_id)
			{
				$objects_id_bin[] = UUID_TO_BIN($object_id);
			}
			$this->db->or_where_in('object_id',$objects_id_bin);
		}
		$q = $this->db
				->join(
					$this->db_table('acl_actions_table'),
					$this->db_table('acl_actions_table') . '.action_id=' . $this->db_table('acl_users_and_groups_table') . '.action_id'
				)
				->join(
					$this->db_table('acl_categories_table'),
					$this->db_table('acl_categories_table') . '.category_id=' . $this->db_table('acl_actions_table') . '.category_id'
				)
				->get($this->db_table('acl_users_and_groups_table'));
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $row)
			{
				$row->user_id     = BIN_TO_UUID($row->user_id);
				$row->group_id    = BIN_TO_UUID($row->group_id);
				$row->action_id   = BIN_TO_UUID($row->action_id);
				$row->category_id = BIN_TO_UUID($row->category_id);
				$acl[] = $row;
			}
		}
		
		return $acl;
	}

	// @STEP2: Check for multiple category_code
	public function get_like( $category_code, $side = 'after', $escape = NULL )
	{
		$acl = array();

		$q = $this->db
				->like(
					$this->db_table('acl_categories_table') . '.category_code',
					$category_code,
					$side,
					$escape
				)
				->join(
					$this->db_table('acl_actions_table'),
					$this->db_table('acl_actions_table') . '.category_id=' . $this->db_table('acl_categories_table') . '.category_id'
				)
				->order_by('action_order', 'ASC')
				// ->order_by('action_code', 'ASC')
				->get($this->db_table('acl_categories_table'));
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $action)
			{
				$action->action_id   = BIN_TO_UUID($action->action_id);
				$acl[$action->category_code][$action->action_code] = $action->action_id;
			}
		}
		
		if( count($acl) === 1 )
		{
			return array_shift($acl);
		}
		
		return $acl;
	}
	
	public function get_group_checked_like( $acl, $users )
	{
		$acl_checked = array();
		
		$actions = array();
		foreach($acl as $action_id)
		{
			$actions[] = UUID_TO_BIN($action_id);
		}
		
		if( is_array($users) )
		{
			$bin_users = [];
			foreach($users as $user)
			{
				$bin_users[] = UUID_TO_BIN($user);
			}
			$this->db->where_in('user_id',$bin_users);
		}
		else
		{
			$this->db->where('user_id',UUID_TO_BIN($users));
		}
		
		$q = $this->db
				->select('group_id, action_id, user_id')
				->where('object_id',NULL)
				->where_in('action_id',$actions)
				->get($this->db_table('acl_users_and_groups_table'));
		
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $action)
			{
				$action->group_id  = BIN_TO_UUID($action->group_id);
				$action->action_id = BIN_TO_UUID($action->action_id);
				$action->user_id   = BIN_TO_UUID($action->user_id);
				if( is_array($users) )
					$acl_checked[$action->user_id][] = $action->group_id .'_'. $action->action_id;
				else
					$acl_checked[] = $action->group_id .'_'. $action->action_id;
			}
		}
		
		return $acl_checked;
	}
	
	public function get_object_checked_like( $acl, $users = NULL, $object = NULL )
	{
		$acl_checked = array();
		
		$actions = array();
		foreach($acl as $action_id)
		{
			$actions[] = UUID_TO_BIN($action_id);
		}
		
		if( is_array($users) )
		{
			$bin_users = [];
			foreach($users as $user)
			{
				$bin_users[] = UUID_TO_BIN($user);
			}
			$this->db->where_in('user_id',$bin_users);
		}
		elseif( ! empty($users) )
		{
			$this->db->where('user_id',UUID_TO_BIN($users));
		}

		if( ! empty($object) )
		{
			$this->db->where('object_id',UUID_TO_BIN($object));
		}
		
		$q = $this->db
				->select('object_id, action_id, user_id')
				->where('group_id',NULL)
				->where_in('action_id',$actions)
				->get($this->db_table('acl_users_and_groups_table'));
		
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $action)
			{
				$action->object_id  = BIN_TO_UUID($action->object_id);
				$action->action_id = BIN_TO_UUID($action->action_id);
				$action->user_id   = BIN_TO_UUID($action->user_id);
				if( is_array($users) OR $users === NULL )
					$acl_checked[$action->user_id][] = $action->object_id .'_'. $action->action_id;
				else
					$acl_checked[] = $action->object_id .'_'. $action->action_id;
			}
		}
		
		return $acl_checked;
	}
	
	public function save_user_group( $acl, $user_id, $data = array() )
	{
		if( $this->remove_user_group($acl, $user_id) === FALSE) { return FALSE; }
		if( ! empty($data) )
			return $this->db->insert_batch($this->db_table('acl_users_and_groups_table'),$data);
		
		return FALSE;
	}
	
	public function remove_user_group( $acl, $user_id )
	{
		$actions = array();
		foreach($acl as $action_id)
		{
			$actions[] = UUID_TO_BIN($action_id);
		}

		$q = $this->db
				->where_in('action_id',$actions)
				->where('user_id',UUID_TO_BIN($user_id))
				->where('object_id', NULL)
				->delete($this->db_table('acl_users_and_groups_table'));
		return $q;
	}
	
	public function save_user_object( $acl, $user_id, $data = array() )
	{
		if( $this->remove_user_object($acl, $user_id) === FALSE) { return FALSE; }
		if( ! empty($data) )
			return $this->db->insert_batch($this->db_table('acl_users_and_groups_table'),$data);
		
		return TRUE;
	}
	
	public function remove_user_object( $acl, $user_id )
	{
		$actions = array();
		foreach($acl as $action_id)
		{
			$actions[] = UUID_TO_BIN($action_id);
		}

		$q = $this->db
				->where_in('action_id',$actions)
				->where('user_id',UUID_TO_BIN($user_id))
				->where('group_id', NULL)
				->delete($this->db_table('acl_users_and_groups_table'));
		return $q;
	}

	public function save_object( $acl, $object_id, $data = array() )
	{
		if( $this->remove_object($acl, $object_id) === FALSE) { return FALSE; }
		if( ! empty($data) )
			return $this->db->insert_batch($this->db_table('acl_users_and_groups_table'),$data);
		
		return TRUE;
	}
	
	public function remove_object( $acl, $object_id )
	{
		$actions = array();
		foreach($acl as $action_id)
		{
			$actions[] = UUID_TO_BIN($action_id);
		}

		$q = $this->db
				->where_in('action_id',$actions)
				->where('object_id',UUID_TO_BIN($object_id))
				->where('group_id', NULL)
				->delete($this->db_table('acl_users_and_groups_table'));
		return $q;
	}
}
