<?php
class Speciality_model extends MY_Model {
	
	public function get_all_dropdown( $limit = NULL, $offset = NULL )
	{
		$specialities = array(
			'specialities' => [],
		);
		
		$q = $this->db
				->order_by('name', 'ASC')
				->get($this->db_table('specialities'),$limit,$offset);
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $speciality)
			{
				$speciality->speciality_id = BIN_TO_UUID($speciality->speciality_id);
				$specialities['specialities'][$speciality->speciality_id] = $speciality->name;
			}
		}
		
		return $specialities;
	}
}
