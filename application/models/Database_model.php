<?php
class Database_model extends MY_Model {
	
	public function _get_databases_name()
	{
		return trim($this->input->post('databases_name') ?: '');
	}
	
	public function _get_databases_default()
	{
		return trim($this->input->post('databases_default') ?: '');
	}

	private function _get_data()
	{
		$data = array(
			'name'    => $this->_get_databases_name(),
			'default' => $this->_get_databases_default(),
		);
		return $data;
	}
	
	public function create( $database_id )
	{
		$data = $this->_get_data();
		$data['database_id']   = UUID_TO_BIN($database_id);
		
		if( $this->db->insert($this->db_table('databases'),$data) )
		{
			return $this->update_default($data['database_id']);
		}
		
		return FALSE;
	}
	
	public function get( $database_id )
	{
		$database    = NULL;
		$database_id = UUID_TO_BIN($database_id);
		
		$q = $this->db
				->where('database_id',$database_id)
				->limit(1)
				->get($this->db_table('databases'));
		if( $q->num_rows() === 1)
		{
			$database = $q->row();
			$database->database_id = BIN_TO_UUID($database->database_id);
		}
		
		return $database;
	}
	
	public function get_all( $limit = NULL, $offset = NULL )
	{
		$databases     = array();
		
		$q = $this->db
				->order_by('default', 'DESC')
				->order_by('name', 'ASC')
				->get($this->db_table('databases'),$limit,$offset);
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $database)
			{
				$database->database_id = BIN_TO_UUID($database->database_id);
				$databases[] = $database;
			}
		}
		
		return $databases;
	}
	
	public function get_all_dropdown( $limit = NULL, $offset = NULL )
	{
		$databases = array(
			'database' => array(),
			'default'    => NULL
		);
		
		$q = $this->db
				->order_by('default', 'DESC')
				->order_by('name', 'ASC')
				->get($this->db_table('databases'),$limit,$offset);
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $database)
			{
				$database->database_id = BIN_TO_UUID($database->database_id);
				$databases['databases'][$database->database_id] = $database->name;
				if( $database->default )
				{
					$databases['default'] = $database->database_id;
				}
			}
		}
		
		return $databases;
	}
	
	public function update( $database )
	{
		$database->database_id = UUID_TO_BIN($database->database_id);
		
		$data = $this->_get_data();
		
		$q = $this->db
				->where('database_id',$database->database_id)
				->update($this->db_table('databases'),$data);
				
		if( $q )
		{
			return $this->update_default($database->database_id);
		}
		
		return FALSE;
	}
	
	private function update_default( $database_id )
	{
		if( $this->_get_databases_default() == 1 )
		{
			return $this->db
						->set('default',0)
						->where('database_id !=',$database_id)
						->update($this->db_table('databases'));
		}
		
		return TRUE;
	}
	
	public function is_unique( $database )
	{
		$q = $this->db
				->select('database_id')
				->where('name',$this->_get_databases_name())
				->limit(2)
				->get($this->db_table('databases'));

		if( $q->num_rows() >= 2 )
		{
			return FALSE;
		}
		
		if( $row = $q->row() )
		{
			if( $database === NULL || $row->database_id !== UUID_TO_BIN($database->database_id) )
			{
				return FALSE;				
			}
		}
		
		return TRUE;
	}
	
}
