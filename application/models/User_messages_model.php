<?php
class User_messages_model extends MY_Model {
	
	public function create( $user_id, $type, $type_id, $action, $severity, $comment = NULL )
	{
		$data['user_id']    = UUID_TO_BIN($user_id);
		$data['type']       = $type;
		$data['type_id']    = UUID_TO_BIN($type_id);
		$data['action']     = $action;
		$data['severity']  = $severity;
		$data['created_at'] = date('Y-m-d H:i:s');
		$data['comment']    = $comment;
		return $this->db->insert($this->db_table('user_messages'),$data);
	}

	public function create_batch( $data )
	{
		return $this->db->insert_batch($this->db_table('user_messages'),$data);
	}
	
	public function get_all( $users = [], $type = NULL, $action = NULL )
	{
		if( empty($users) ) { return NULL; }
		
		$messages = [];
		$users_id = [];
		foreach($users as $user)
		{
			$users_id[] = UUID_TO_BIN($user);
		}
		
		if( $type )
			$this->db->where('type', $type);
		
		if( $action )
			$this->db->where('action', $action);
		
		$q = $this->db
				->where_in('user_id', $users_id)
				->get($this->db_table('user_messages'));
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $m)
			{
				$m->user_id = BIN_TO_UUID($m->user_id);
				$m->type_id = BIN_TO_UUID($m->type_id);
				
				if( $m->type === 'checklist')
					$messages['messages'][$m->type][$m->user_id][$m->severity][$m->type_id . '_' . $m->comment] = $m;
				else
					$messages['messages'][$m->type][$m->user_id][$m->severity][$m->type_id] = $m;
				
				$messages['id'][$m->type][$m->type_id] = $m->type_id;
			}
		}
		
		return $messages;
	}
	
	public function remove( $type, $user_id, $type_id )
	{
		return $this->db->delete($this->db_table('user_messages'),
			array(
				'type' => $type,
				'user_id' => UUID_TO_BIN($user_id),
				'type_id' => UUID_TO_BIN($type_id),
			)
		);
	}

	public function remove_comment( $type, $user_id, $type_id, $comment )
	{
		return $this->db->delete($this->db_table('user_messages'),
			array(
				'type' => $type,
				'user_id' => UUID_TO_BIN($user_id),
				'type_id' => UUID_TO_BIN($type_id),
				'comment' => $comment,
			)
		);
	}

	public function remove_all_by_id( $type, $id, $action )
	{
		return $this->db->delete($this->db_table('user_messages'),
			array(
				'type'    => $type,
				'type_id' => UUID_TO_BIN($id),
				'action'  => $action,
			)
		);
	}
	
	public function remove_all_actions( $type, $action, $user_id )
	{
		return $this->db->delete($this->db_table('user_messages'),
			array(
				'type'    => $type,
				'action'  => $action,
				'user_id' => UUID_TO_BIN($user_id),
			)
		);
	}
	
	public function check_empty( $type, $table, $column )
	{
		$empty = [];

		$not = $this->db
					->select($column)
					->get_compiled_select($this->db_table($table));
		
		$q = $this->db
				->select('type_id,action')
				->where_not_in('type_id', $not, FALSE)
				->where('type', $type)
				->get($this->db_table('user_messages'));

		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $m)
			{
				$empty[BIN_TO_UUID($m->type_id)] = $m->action;
			}
		}

		return $empty;
	}
}
