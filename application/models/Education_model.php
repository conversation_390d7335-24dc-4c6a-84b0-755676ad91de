<?php
class Education_model extends MY_Model {
	
	public function _get_tree_documents()
	{
		return $this->input->post('tree_documents');
	}
	
	public function _get_education_position()
	{
		return $this->input->post('education_position');
	}
	
	public function get_document( $document_id, $version = FALSE )
	{
		$q = $this->db
				->where('document_id',UUID_TO_BIN($document_id))
				->limit(1)
				->get($this->db_table('document_education'));
		
		if( $q->num_rows() === 1)
		{
			if( $version )
				return $q->row()->version;
			else
				return TRUE;
		}
	}
	
	public function update_document( $document_id, $version )
	{
		$this->db->where('document_id', UUID_TO_BIN($document_id))->update($this->db_table('document_education'),[
			'version' => $version
		]);
	}
	
	public function get_relationships( $column, $id )
	{
		$groups   = array();
		$ids      = array();
		$is_array = TRUE;
		
		if( ! is_array($id) )
		{
			$id = (array) $id;
			$is_array = FALSE;
		}
		
		foreach($id as $row)
		{
			$ids[] = UUID_TO_BIN($row);
		}
		
		$q = $this->db
				->where_in($column,$ids)
				->get($this->db_table('document_education'));
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $group)
			{
				if( ! $is_array )
					$groups[] = BIN_TO_UUID($group->document_id);
				else
					$groups[BIN_TO_UUID($group->$column)][] = BIN_TO_UUID($group->document_id);
			}
		}
		
		return $groups;
	}
	
	public function save( $column, $id, $delete_groups = array() )
	{
		if( $this->remove($column,$id,$delete_groups) === FALSE) { return FALSE; }
		$tree_documents = $this->_get_tree_documents();
		if( empty($tree_documents) ) { return TRUE; }
		
		$data = array();
		foreach($tree_documents as $document_version)
		{
			list($document_id,$version) = explode('_',$document_version);
			$data[] = array(
				$column       => UUID_TO_BIN($id),
				'document_id' => UUID_TO_BIN($document_id),
				'version'     => $version,
			);
		}

		if( ! empty($data) )
			return $this->db->insert_batch($this->db_table('document_education'),$data);
		
		return FALSE;
	}
	
	public function remove( $column, $id, $delete_groups )
	{
		$delete = array();
		if( !empty($delete_groups) )
		{
			foreach($delete_groups as $group_id)
			{
				$delete[] = UUID_TO_BIN($group_id);
			}
			$this->db->where_in('documment_id', $delete);
		}
		return $this->db->delete($this->db_table('document_education'),array($column => UUID_TO_BIN($id)));
	}
	
	public function read( $document_id, $version )
	{
		$data = [
			'user_id'     => UUID_TO_BIN($this->auth_user_id),
			'document_id' => UUID_TO_BIN($document_id),
			'version'     => $version,
			'done'        => date('Y-m-d H:i:s')
		];
		
		return $this->db->insert($this->db_table('document_education_done'), $data);
	}
	
	public function delete( $group_id )
	{
		$group_id = UUID_TO_BIN($group_id);
		
		$this->db->delete($this->db_table('document_education_group'), [
			'education_id' => $group_id
		]);
		$this->db->delete($this->db_table('groups'), [
			'group_id' => $group_id
		]);
		$this->db->delete($this->db_table('document_education'), [
			'group_id' => $group_id
		]);
		$this->db->delete($this->db_table('user_group'), [
			'group_id' => $group_id
		]);
		
		return TRUE;
	}

	public function unmark($document_id) {
		$document_id = UUID_TO_BIN($document_id);
		$this->db->delete($this->db_table('document_education'), [
			'document_id' => $document_id
		]);

		$this->db->delete($this->db_table('document_education_done'), [
			'document_id' => $document_id
		]);
	}

}
