<?php
class Company_model extends MY_Model
{
	
	public function _get_company_name()
	{
		return trim($this->input->post('company_name') ?: '');
	}
	
	public function _get_company_alias()
	{
		return trim($this->input->post('company_alias') ?: '');
	}
	
	public function _get_company_address()
	{
		return trim($this->input->post('company_address') ?: '');
	}
	
	public function _get_company_address2()
	{
		return trim($this->input->post('company_address2') ?: '');
	}
	
	public function _get_company_zip()
	{
		return trim($this->input->post('company_zip') ?: '');
	}
	
	public function _get_company_city()
	{
		return trim($this->input->post('company_city') ?: '');
	}
	
	public function _get_company_phone()
	{
		return trim($this->input->post('company_phone') ?: '');
	}
	
	public function _get_company_email()
	{
		return trim($this->input->post('company_email') ?: '');
	}
	
	public function _get_company_fax()
	{
		return trim($this->input->post('company_fax') ?: '');
	}
	
	public function _get_company_active()
	{
		return trim($this->input->post('company_active') ?: '');
	}
	
	public function _get_company_speciality()
	{
		return trim($this->input->post('company_speciality') ?: '');
	}
	
	public function _get_company_membership()
	{
		return trim($this->input->post('company_membership') ?: '');
	}
	
	public function _get_company_database()
	{
		return trim($this->input->post('company_database') ?: '');
	}
	
	public function _get_data()
	{
		$data = array(
			'name'          => $this->_get_company_name(),
			'alias'         => $this->_get_company_alias(),
			'address'       => $this->_get_company_address(),
			'address2'      => $this->_get_company_address2(),
			'zip'           => $this->_get_company_zip(),
			'city'          => $this->_get_company_city(),
			'phone'         => $this->_get_company_phone(),
			'email'         => $this->_get_company_email(),
			'fax'           => $this->_get_company_fax(),
			'speciality_id' => $this->_get_company_speciality(),
			'membership_id' => $this->_get_company_membership(),
			'database_id'   => $this->_get_company_database()
		);
		
		return $data;
	}
	
	public function create( $company_id )
	{
		$company_id = UUID_TO_BIN($company_id);
		$data                  = $this->_get_data();
		$data['company_id']    = $company_id;
		$data['membership_id'] = UUID_TO_BIN($data['membership_id']);
		$data['database_id']   = UUID_TO_BIN($data['database_id']);
		// return $this->db->insert($this->db_table('company'),$data);
		
		$this->db->insert($this->db_table('company'),$data);
		
		// Groups
		$position = UUID_TO_BIN(UUIDv4());
		$department = UUID_TO_BIN(UUIDv4());
		
		$groups[] = [
			'company_id' => $company_id,
			'group_id'   => $position,
			'type'       => 'position',
			'name'       => 'Befattning'
		];
		
		$groups[] = [
			'company_id' => $company_id,
			'group_id'   => $department,
			'type'       => 'department',
			'name'       => 'Avdelning'
		];

		$menu_id = UUID_TO_BIN(UUIDv4());
		
		$menus[] = [
			'company_id' => $company_id,
			'menu_id' => $menu_id,
			'parent_id' => NULL,
			'program_id' => NULL,
			// 'owner' => $user_id,
			'owner' => NULL,
			'name' => 'Dokument',
			'description' => '',
			'type' => 1,
			'sticky' => 0,
			'href' => NULL
		];
		
		$child_menu_id = UUID_TO_BIN(UUIDv4());
		
		$menus[] = [
			'company_id' => $company_id,
			'menu_id' => $child_menu_id,
			'parent_id' => $menu_id,
			'program_id' => NULL,
			// 'owner' => $user_id,
			'owner' => NULL,
			'name' => 'Dokument',
			'description' => '',
			'type' => 0,
			'sticky' => 0,
			'href' => NULL
		];
		
		$menu_group[] = [
			'menu_id'  => $menu_id,
			'group_id' => $position
		];
		
		$menu_group[] = [
			'menu_id'  => $child_menu_id,
			'group_id' => $position
		];
		
		$menu_group[] = [
			'menu_id'  => $menu_id,
			'group_id' => $department
		];
		
		$menu_group[] = [
			'menu_id'  => $child_menu_id,
			'group_id' => $department
		];
		
		$folders[] = [
			'folder_id' => UUID_TO_BIN(UUIDv4()),
			'menu_id'   => $child_menu_id,
			'name'      => 'Affärsdokument'
		];
		
		$folders[] = [
			'folder_id' => UUID_TO_BIN(UUIDv4()),
			'menu_id'   => $child_menu_id,
			'name'      => 'Ekonomi/Administration'
		];
		
		$folders[] = [
			'folder_id' => UUID_TO_BIN(UUIDv4()),
			'menu_id'   => $child_menu_id,
			'name'      => 'IT'
		];
		
		$folders[] = [
			'folder_id' => UUID_TO_BIN(UUIDv4()),
			'menu_id'   => $child_menu_id,
			'name'      => 'Korrespondens'
		];
		
		$folders[] = [
			'folder_id' => UUID_TO_BIN(UUIDv4()),
			'menu_id'   => $child_menu_id,
			'name'      => 'Lagar/regler'
		];
		
		$folders[] = [
			'folder_id' => UUID_TO_BIN(UUIDv4()),
			'menu_id'   => $child_menu_id,
			'name'      => 'Marknad/försäljning'
		];
		
		$folders[] = [
			'folder_id' => UUID_TO_BIN(UUIDv4()),
			'menu_id'   => $child_menu_id,
			'name'      => 'Miljö'
		];
		
		$folders[] = [
			'folder_id' => UUID_TO_BIN(UUIDv4()),
			'menu_id'   => $child_menu_id,
			'name'      => 'Organisation'
		];
		
		$folders[] = [
			'folder_id' => UUID_TO_BIN(UUIDv4()),
			'menu_id'   => $child_menu_id,
			'name'      => 'Patient'
		];
		
		$folders[] = [
			'folder_id' => UUID_TO_BIN(UUIDv4()),
			'menu_id'   => $child_menu_id,
			'name'      => 'Personal'
		];
		
		$folders[] = [
			'folder_id' => UUID_TO_BIN(UUIDv4()),
			'menu_id'   => $child_menu_id,
			'name'      => 'Rutiner/policys'
		];
		
		$folders[] = [
			'folder_id' => UUID_TO_BIN(UUIDv4()),
			'menu_id'   => $child_menu_id,
			'name'      => 'Teknik'
		];
		
		$folders[] = [
			'folder_id' => UUID_TO_BIN(UUIDv4()),
			'menu_id'   => $child_menu_id,
			'name'      => 'Övrigt'
		];
		
		$this->db->insert_batch('groups', $groups);
		$this->db->insert_batch('menus', $menus);
		$this->db->insert_batch('menu_group', $menu_group);
		$this->db->insert_batch('folders', $folders);
		
		return TRUE;
	}
	
	public function get( $company_id )
	{
		$company    = NULL;
		$company_id = UUID_TO_BIN($company_id);
		
		$q = $this->db
				->where('company_id',$company_id)
				->limit(1)
				->get($this->db_table('company'));
		if( $q->num_rows() === 1)
		{
			$company                = $q->row();
			$company->company_id    = BIN_TO_UUID($company->company_id);
			$company->membership_id = BIN_TO_UUID($company->membership_id);
			$company->speciality_id = BIN_TO_UUID($company->speciality_id);
			$company->database_id   = BIN_TO_UUID($company->database_id);
		}
		
		return $company;
	}
	
	public function get_all_companies()
	{
		return $this->db->order_by('name','ASC')->get($this->db_table('company'))->result();
	}
	
	public function get_all_active_companies( $filter = NULL, $filter_order = FALSE )
	{
		$companies = array();
		
		$this->db->where('active',1);
		if( $filter_order ) {
			$this->db->order_by('membership_id','ASC');
		}
		$this->db->order_by('name','ASC');
		if( $filter ) {
			$this->db->where('membership_id', UUID_TO_BIN($filter));
		}
		
		$q = $this->db->get($this->db_table('company'));
		
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $company)
			{
				$company->company_id    = BIN_TO_UUID($company->company_id);
				$company->membership_id = BIN_TO_UUID($company->membership_id);
				$company->database_id   = BIN_TO_UUID($company->database_id);
				$companies[]            = $company;
			}
		}
		
		return $companies;
	}
	
	public function update( $company )
	{
		$company->company_id = UUID_TO_BIN($company->company_id);
		
		$data                  = $this->_get_data();
		$date['type']          = 0; // Web and 2000 convertion to FLEX
		$data['active']        = $this->_get_company_active() === '' ? $company->active : $this->_get_company_active();
		$data['speciality_id'] = $this->_get_company_speciality() === '' ? UUID_TO_BIN($company->speciality_id) : UUID_TO_BIN($data['speciality_id']);
		$data['membership_id'] = $this->_get_company_membership() === '' ? UUID_TO_BIN($company->membership_id) : UUID_TO_BIN($data['membership_id']);
		$data['database_id']   = $this->_get_company_database()   === '' ? UUID_TO_BIN($company->database_id)   : UUID_TO_BIN($data['database_id']);
		
		$q = $this->db
				->where('company_id',$company->company_id)
				->update($this->db_table('company'),$data);
		
		return $q;
	}
	
	public function is_unique( $company )
	{
		$q = $this->db
				->select('company_id')
				->where('name',$this->_get_company_name())
				->limit(2)
				->get($this->db_table('company'));

		if( $q->num_rows() >= 2 )
		{
			return FALSE;
		}
		
		if( $row = $q->row() )
		{
			if( $company === NULL || $row->company_id !== UUID_TO_BIN($company->company_id) )
			{
				return FALSE;				
			}
		}
		
		return TRUE;
	}
}