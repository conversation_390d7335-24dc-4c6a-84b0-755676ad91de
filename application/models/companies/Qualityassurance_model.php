<?php
class Qualityassurance_model extends MY_Model
{
	public function getAllByFiscalYear($fiscal = NULL)
	{
		if(is_null($fiscal))
			$fiscal = intval(date('Y')) - 1;
		
		$users = [];
		
		$sql = "
		SELECT
			u.`user_id`,
			u.`name`,
			u.`last_visit`,
			c.`membership_id`,
			qa.`done` as qa_done
		FROM 
			`users` AS u
		INNER JOIN
			`companies` AS c 
				ON u.`company_id`=c.`company_id`
				AND c.`active` = 1 
		LEFT JOIN
			`form_survey_025b5eca49_done` AS qa
				ON u.`user_id` = qa.`user_id`
				AND qa.`fiscal` = ?
		ORDER BY
			qa.`done` DESC,
			u.`last_visit` DESC,
			u.`name` ASC";

		$q = $this->db->query($sql, [$fiscal]);

		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $user)
			{
				$user->user_id       = BIN_TO_UUID($user->user_id);
				$user->membership_id = BIN_TO_UUID($user->membership_id);
				$users[$user->user_id] = $user;
			}
		}
		
		return $users;
	}
}