<?php
class Posts_model extends MY_Model {
	
	private $data = array();
	
	public function _get_name()
	{
		return $this->input->post('posts_name');
	}
	
	public function _get_posts_publish()
	{
		return $this->input->post('posts_publish');
	}
	
	public function _get_posts_unpublish()
	{
		return $this->input->post('posts_unpublish') ? $this->input->post('posts_unpublish') : NULL;
	}
	
	public function _get_posts_category()
	{
		return $this->input->post('posts_category[]');
	}
	
	// @STEP2: htmlpurifier
	public function _get_posts_document()
	{
		return trim($this->input->post('posts_document') ?: '');
	}
	
	// @STEP2: htmlpurifier
	public function _get_posts_comment()
	{
		return trim($this->input->post('posts_comment') ?: '');
	}
	
	public function _get_category_slug()
	{
		return $this->input->post('posts_category_slug');
	}
	
	public function _get_category_parent_id()
	{
		return trim($this->input->post('posts_category_parent') ?: '');
	}
	
	public function _get_category_sticky()
	{
		return trim($this->input->post('posts_category_sticky') ?: '');
	}
	
	public function _get_pages_category()
	{
		return $this->input->post('pages_category[]');
	}
	
	// @STEP2: htmlpurifier
	public function _get_pages_document()
	{
		return trim($this->input->post('pages_document') ?: '');
	}
	
	public function _get_pages_href()
	{
		return trim($this->input->post('pages_href') ?: '');
	}
	
	public function _get_pages_icon()
	{
		return $this->input->post('pages_pageicon');
	}
	
	public function _get_pages_position()
	{
		return $this->input->post('pages_position');
	}
	
	public function _get_pages_layout()
	{
		return $this->input->post('pages_layout');
	}
	
	public function _get_pages_status()
	{
		return $this->input->post('pages_status');
	}
	
	private function _get_categories_data($id)
	{
		$this->data['company_id']  = UUID_TO_BIN($this->auth_company_id);
		$this->data['category_id'] = UUID_TO_BIN($id);
		$this->data['slug']        = $this->_get_category_slug();
		$this->data['sticky']      = $this->_get_category_sticky();
		
		if( $parent_id = $this->_get_category_parent_id() )
			$this->data['parent_id'] = UUID_TO_BIN($parent_id);
		else
			$this->data['parent_id'] = NULL;
	}
	
	public function read_post($id)
	{
		$bindings = [];
		$bindings[] = UUID_TO_BIN($id);
		$bindings[] = UUID_TO_BIN($this->auth_user_id);
		$bindings[] = date('Y-m-d H:i:s');
		$query = 'INSERT IGNORE INTO '. $this->db_table('post_read') . ' (post_id, user_id, done) VALUES (?,?,?)';
		
		$q = $this->db->query($query, $bindings);
	}
	
	public function get_readlog_summary( $id )
	{
		$log = [];
		$q = $this->db
				->select('user_id, MAX(done) as done')
				->where('post_id',UUID_TO_BIN($id))
				->group_by('user_id,post_id')
				->get($this->db_table('post_read'));
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $read)
			{
				$read->user_id = BIN_TO_UUID($read->user_id);
				$log[] = $read;
			}
		}
		
		return $log;
	}
	
	public function get_read_post()
	{
		$q = $this->db
				->select('post_id')
				->where('user_id', UUID_TO_BIN($this->auth_user_id))
				->get($this->db_table('post_read'));
		
		$read = [];
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $r)
			{
				$read[] = BIN_TO_UUID($r->post_id);
			}
		}
		
		return $read;
	}
	
	public function get_post($id, $published = TRUE)
	{
		
		if( $published )
		{
			$today = date('Y-m-d');
			$this->db
				->where('publish <= ', $today)
				->group_start()
				->where('unpublish', NULL)
				->or_where('unpublish >=', $today)
				->group_end();
		}
		
		$q = $this->db
				->where('post_id', UUID_TO_BIN($id))
				->limit(1)
				->get($this->db_table('posts'));
		
		$post = NULL;
		if( $q->num_rows() === 1 )
		{
			$post = $q->row();
			$post->post_id     = BIN_TO_UUID($post->post_id);
			$post->parent_id   = BIN_TO_UUID($post->parent_id);
			$post->created_by  = BIN_TO_UUID($post->created_by);
			$post->edited_by   = BIN_TO_UUID($post->edited_by);
		}
		
		return $post;
	}
	
	public function get_page($id, $strip = TRUE)
	{
		$q = $this->db
				->where('page_id', UUID_TO_BIN($id))
				->limit(1)
				->get($this->db_table('pages'));
		
		$page = NULL;
		if( $q->num_rows() === 1 )
		{
			$page = $q->row();
			$page->company_id  = BIN_TO_UUID($page->company_id);
			$page->page_id     = BIN_TO_UUID($page->page_id);
			$page->parent_id   = BIN_TO_UUID($page->parent_id);
			$page->created_by  = BIN_TO_UUID($page->created_by);
			$page->edited_by   = BIN_TO_UUID($page->edited_by);
			if( $strip )
				$page->name    = preg_replace('@^[0-9 ]+@','',$page->name);
		}
		
		return $page;
	}
	
	public function get_post_and_page_with_same_category($page_id, $post_id)
	{
		$q = $this->db
				->select('post_id')
				->from($this->db_table('post_category'))
				->join($this->db_table('page_category'), $this->db_table('post_category') . '.category_id=' . $this->db_table('page_category') . '.category_id')
				->where('page_id', UUID_TO_BIN($page_id))
				->where('post_id', UUID_TO_BIN($post_id))
				->get();
				
		$posts = [];
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $post)
			{
				$posts[] = BIN_TO_UUID($post->post_id);
			}
		}
		
		return $posts;
	}
	
	public function get_post_categories($id)
	{
		$q = $this->db
				->select('category_id')
				->where('post_id', UUID_TO_BIN($id))
				->get($this->db_table('post_category'));
		
		$categories = [];
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $category)
			{
				$categories[] = BIN_TO_UUID($category->category_id);
			}
		}
		
		return $categories;
	}
	
	public function get_page_categories($id, $name = FALSE)
	{
		$categories = [];
		
		if( $name === FALSE)
		{
			$q = $this->db
					->select('category_id')
					->where('page_id', UUID_TO_BIN($id))
					->get($this->db_table('page_category'));
			
			if( $q->num_rows() !== 0 )
			{
				foreach($q->result() as $category)
				{
					$categories[] = BIN_TO_UUID($category->category_id);
				}
			}
		}
		else
		{
			$q = $this->db
					->select('c.category_id,c.name')
					->where('page_id',UUID_TO_BIN($id))
					->join($this->db_table('categories') . ' AS c', 'p.category_id=c.category_id', 'right')
					->order_by('sticky','ASC')
					->order_by('name','ASC')
					->get($this->db_table('page_category') . ' AS p');
			
			if( $q->num_rows() !== 0 )
			{
				foreach($q->result() as $category)
				{
					$category->category_id = BIN_TO_UUID($category->category_id);
					$categories[$category->category_id] = $category;
				}
			}
		}
		
		return $categories;
	}
	
	public function get_all_posts( $posts = [], $published = TRUE )
	{
		if( empty($posts) )
			return FALSE;
		
		$bin_posts = [];
		foreach($posts as $post)
			$bin_posts[] = UUID_TO_BIN($post);
		
		if( $published )
		{
			$today = date('Y-m-d');
			$this->db
				->where('publish <= ', $today)
				->group_start()
				->where('unpublish', NULL)
				->or_where('unpublish >=', $today)
				->group_end();
		}
			
		$q = $this->db
				->select('post_id,created_by,created_date,edited_by,edited_date,name,publish,unpublish,comments')
				->where_in('post_id', $bin_posts)
				->where('status', 'published')
				->order_by('publish','DESC')
				->order_by('created_date','DESC')
				->get($this->db_table('posts'));
		
		$posts = [];
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $post)
			{
				$post->post_id    = BIN_TO_UUID($post->post_id);
				// $post->parent_id  = BIN_TO_UUID($post->parent_id);
				$post->created_by = BIN_TO_UUID($post->created_by);
				$post->edited_by  = BIN_TO_UUID($post->edited_by);
				$posts[$post->post_id] = $post;
			}
		}
		
		return $posts;
	}
	
	public function get_all_pages()
	{	
		$q = $this->db
				->select('page_id,created_by,created_date,edited_by,edited_date,name,href,position,layout,status')
				->where('company_id', UUID_TO_BIN($this->auth_company_id))
				->where('status !=', 'archived')
				->order_by('name','ASC')
				->get($this->db_table('pages'));
		
		$pages = [];
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $page)
			{
				$page->page_id    = BIN_TO_UUID($page->page_id);
				// $page->parent_id  = BIN_TO_UUID($page->parent_id);
				$page->created_by = BIN_TO_UUID($page->created_by);
				$page->edited_by  = BIN_TO_UUID($page->edited_by);
				$pages[$page->page_id] = $page;
			}
		}
		
		return $pages;
	}
	
	public function get_all_pages_for_menu()
	{
		$q = $this->db
				->select('page_id,name,href,icon,position,layout, content')
				->group_start()
					->where('company_id', UUID_TO_BIN($this->auth_company_id))
					->or_where('status','public')
				->group_end()
				->where('status !=', 'inactive')
				->where('status !=', 'archived')
				->order_by('name','ASC')
				->get($this->db_table('pages'));
		
		$pages = [];
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $page)
			{
				$page->page_id = BIN_TO_UUID($page->page_id);
				$page->name    = preg_replace('@^[0-9 ]+@','',$page->name);
				$page->subtitle  = strip_tags($page->content);
				$pages[$page->position][$page->page_id] = $page;
			}
		}
		
		return $pages;
	}
	
	public function get_posts_categories( $id = NULL, $private = TRUE )
	{
		if( $id !== NULL && ! is_array($id) )
			$this->db->where('c.category_id', UUID_TO_BIN($id));
		if( is_array($id) && ! empty($id) )
		{
			$find = [];
			foreach($id as $category_id)
			{
				$find[] = UUID_TO_BIN($category_id);
			}
			$this->db->where_in('c.category_id', $find);
		}
		
		if( $private )
			$this->db->where('company_id',UUID_TO_BIN($this->auth_company_id));
		
		$q = $this->db
				->select('c.category_id,c.name,p.post_id')
				->join($this->db_table('post_category') . ' AS p', 'c.category_id=p.category_id', 'right')
				->order_by('sticky','ASC')
				->order_by('name','ASC')
				->get($this->db_table('categories') . ' AS c');
				
		$categories = [];
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $category)
			{
				$category->post_id     = BIN_TO_UUID($category->post_id);
				$category->category_id = BIN_TO_UUID($category->category_id);
				$categories[$category->post_id][$category->category_id] = $category->name;
			}
		}
		
		return $categories;
	}
	
	public function get_pages_categories( $id = NULL )
	{
		if( $id !== NULL)
			$this->db->where('c.category_id', UUID_TO_BIN($id));
		
		$q = $this->db
				->select('c.category_id,c.name,p.page_id')
				->where('company_id',UUID_TO_BIN($this->auth_company_id))
				->join($this->db_table('page_category') . ' AS p', 'c.category_id=p.category_id', 'right')
				->order_by('sticky','ASC')
				->order_by('name','ASC')
				->get($this->db_table('categories') . ' AS c');
		
		$categories = [];
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $category)
			{
				$category->page_id     = BIN_TO_UUID($category->page_id);
				$category->category_id = BIN_TO_UUID($category->category_id);
				$categories[$category->page_id][$category->category_id] = $category->name;
			}
		}
		
		return $categories;
	}
	
	public function get_category( $id )
	{
		$q = $this->db
				->where('category_id', UUID_TO_BIN($id))
				->limit(1)
				->get($this->db_table('categories'));
		
		$category = NULL;
		if( $q->num_rows() === 1 )
		{
			$category = $q->row();
			$category->company_id  = BIN_TO_UUID($category->company_id);
			$category->category_id = BIN_TO_UUID($category->category_id);
			$category->parent_id   = BIN_TO_UUID($category->parent_id);
		}
		
		return $category;
	}
	
	public function get_categories( $parent_id )
	{
		if( $parent_id !== NULL )
			$this->db->where('parent_id', UUID_TO_BIN($parent_id));
		else
			$this->db->where('parent_id',NULL);
		
		$q = $this->db
				->select('category_id,name')
				->where('company_id', UUID_TO_BIN($this->auth_company_id))
				->order_by('sticky','ASC')
				->order_by('name','ASC')
				->get($this->db_table('categories'));
		
		$categories = [];
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $category)
			{
				$categories[BIN_TO_UUID($category->category_id)] = $category->name;
			}
		}
		
		return $categories;
	}
	
	public function get_all_categories()
	{
		$q = $this->db
				->select('parent_id,category_id,name')
				->where('company_id', UUID_TO_BIN($this->auth_company_id))
				->order_by('sticky','ASC')
				->order_by('name','ASC')
				->get($this->db_table('categories'));
		
		$categories = [];
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $category)
			{
				$category->category_id = BIN_TO_UUID($category->category_id);
				$category->parent_id   = BIN_TO_UUID($category->parent_id);
				
				if( $category->parent_id !== NULL )
				{
					$categories[$category->parent_id][$category->category_id] = $category->name;
					$categories['map'][$category->category_id] = $category->parent_id;
				}
				else
				{
					$categories[0][$category->category_id] = $category->name;
				}
				
				$categories['all'][$category->category_id] = $category->name;
			}
		}
		
		return $categories;
	}
	
	public function get_category_parents()
	{
		$q = $this->db
				->select('category_id,name')
				->where('company_id', UUID_TO_BIN($this->auth_company_id))
				->where('parent_id',NULL)
				->order_by('sticky','ASC')
				->order_by('name','ASC')
				->get($this->db_table('categories'));
		
		$parents = [NULL => lang('no')];
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $category)
			{
				$parents[BIN_TO_UUID($category->category_id)] = $category->name;
			}
		}
		
		return $parents;
	}
	
	public function get_attachments( $id, $name = 'post' )
	{
		$attachments = NULL;
		$id = UUID_TO_BIN($id);
		
		$id_name = $name . '_id';
		$table_name = $name . '_attachment';
		
		$q = $this->db
				->select('attachment_id,uploaded_on,file_name,file_ext')
				->where($id_name,$id)
				->order_by('uploaded_on','ASC')
				->get($this->db_table($table_name));
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $attachment)
			{
				$attachment->attachment_id = BIN_TO_UUID($attachment->attachment_id);
				$attachments[] = $attachment;
			}
		}
		
		return $attachments;
	}
	
	public function get_attachment( $attachment_id, $name = 'post' )
	{
		$attachment = array();
		$attachment_id = UUID_TO_BIN($attachment_id);
		
		$id_name = $name . '_id';
		$table_name = $name . '_attachment';
		
		$q = $this->db
				->select($id_name . ',attachment_id,uploaded_on,file_name,file_ext')
				->where('attachment_id',$attachment_id)
				->limit(1)
				->get($this->db_table($table_name));
				
		if( $q->num_rows() === 1)
		{
			$attachment = $q->row();
			$attachment->{$id_name}    = BIN_TO_UUID($attachment->{$id_name});
			$attachment->attachment_id = BIN_TO_UUID($attachment->attachment_id);
		}
		
		return $attachment;
	}
	
	public function get_comments( $id )
	{
		$comments = NULL;
		$id = UUID_TO_BIN($id);
		
		$q = $this->db
				->select('comment_id,created_by,created_date,comment')
				->where('post_id',$id)
				->order_by('created_date','DESC')
				->get($this->db_table('post_comment'));
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $comment)
			{
				$comment->comment_id = BIN_TO_UUID($comment->comment_id);
				$comment->created_by = BIN_TO_UUID($comment->created_by);
				$comments[] = $comment;
			}
		}
		
		return $comments;
	}
	
	public function get_comment( $id )
	{
		// SELECT post_comment.*, posts.name, pages.page_id, pages.company_id FROM post_comment
		// INNER JOIN posts ON post_comment.post_id=posts.post_id
		// INNER JOIN post_category ON posts.post_id=post_category.post_id
		// INNER JOIN page_category ON post_category.category_id=page_category.category_id
		// INNER JOIN pages ON page_category.page_id=pages.page_id
		
		$comment = NULL;
		$id         = UUID_TO_BIN($id);
		$company_id = UUID_TO_BIN($this->auth_company_id);
		
		$q = $this->db
				->select('post_comment.*, pages.page_id, pages.company_id, posts.name')
				->from($this->db_table('post_comment'))
				->join($this->db_table('posts') , 'post_comment.post_id = posts.post_id', 'left')
				->join($this->db_table('post_category') , 'post_comment.post_id = post_category.post_id', 'left')
				->join($this->db_table('page_category') , 'post_category.category_id = page_category.category_id', 'left')
				->join($this->db_table('pages') , 'page_category.page_id = pages.page_id', 'left')
				->where('comment_id',$id)
				->where('company_id', $company_id)
				->where('position', 3)
				->group_by('comment_id')
				->order_by('created_date','DESC')
				->get();
				
		if( $q->num_rows() === 1 )
		{
			$comment = $q->row();
			$comment->comment_id = BIN_TO_UUID($comment->comment_id);
			$comment->post_id    = BIN_TO_UUID($comment->post_id);
			$comment->created_by = BIN_TO_UUID($comment->created_by);
			$comment->page_id    = BIN_TO_UUID($comment->page_id);
			$comment->company_id = BIN_TO_UUID($comment->company_id);
		}
		
		return $comment;
	}
	
	public function create( $id, $type, $callback, $object = NULL )
	{
		$this->data['name'] = $this->_get_name();
		// Load specific functions
		return $this->{$callback}($id,$type,$object);
	}
	
	// @STEP2: Only keep 3 archived documents
	private function create_posts( $id, $type, $object )
	{
		// var_dump($object);exit;
		$current_time = date('Y-m-d H:i:s');
		// Modify new post
		$object->post_id           = UUID_TO_BIN($object->post_id);
		$object->created_by        = UUID_TO_BIN($object->created_by);
		// $object->created_date   = $object->created_date;
		$object->edited_by         = UUID_TO_BIN($this->auth_user_id);
		$object->edited_date       = $current_time;
		$object->name		       = $this->_get_name();
		$object->publish		   = $this->_get_posts_publish();
		$object->unpublish		   = $this->_get_posts_unpublish();
		$object->content		   = $this->_get_posts_document();
		$object->status            = 'published';
		
		// Save categories
		$this->post_category($object->post_id);
		
		// Save old post as archived
		if( isset($object->parent_id) )
		{
			$object->parent_id = UUID_TO_BIN($object->parent_id);
			$q = $this->db->where( 'post_id', $object->parent_id )->get( $this->db_table('posts') );
			
			if( $q->num_rows() === 1 )
			{
				$d = $q->row();
				$d->post_id      = UUID_TO_BIN(UUIDv4());
				$d->parent_id    = $object->parent_id;
				$d->created_date = $current_time;
				$d->status       = 'archived';
				
				$this->db->insert($this->db_table('posts'),$d);
			}
			
			$object->parent_id = NULL;
			return $this->db->where('post_id', $object->post_id)->update($this->db_table('posts'),$object);
		}
		// Save new post
		else
		{
			return $this->db->insert($this->db_table('posts'),$object);
		}
		
		return FALSE;
	}
	
	private function create_categories( $id, $type )
	{
		$this->_get_categories_data($id);
		return $this->db->insert($this->db_table($type),$this->data);
	}
	
	private function post_category( $id )
	{
		$categories = $this->_get_posts_category();
		if( ! empty($categories) )
		{
			$this->db->delete($this->db_table('post_category'),array('post_id' => $id));
			
			$data = [];
			foreach($categories AS $category)
			{
				$data[] = [
					'post_id'     => $id,
					'category_id' => UUID_TO_BIN($category)
				];
			}
			
			return $this->db->insert_batch($this->db_table('post_category'),$data);
		}
	}
	
	private function page_category( $id )
	{
		$categories = $this->_get_pages_category();
		if( ! empty($categories) )
		{
			$this->db->delete($this->db_table('page_category'),array('page_id' => $id));
			
			$data = [];
			foreach($categories AS $category)
			{
				$data[] = [
					'page_id'     => $id,
					'category_id' => UUID_TO_BIN($category)
				];
			}
			
			return $this->db->insert_batch($this->db_table('page_category'),$data);
		}
	}

	public function update_edit_date( $post_id )
	{
		return $this->db->update($this->db_table('posts'),['edited_date' => date('Y-m-d H:i:s')], ['post_id' => UUID_TO_BIN($post_id)]);
	}
	
	public function update( $id, $type, $callback, $object = NULL )
	{
		// var_dump($object);exit;
		$this->data['name'] = $this->_get_name();
		// Load specific functions
		return $this->{$callback}($id,$type,$object);
	}
	
	private function update_posts( $id, $type, $object )
	{
		// var_dump($object);exit;
		return $this->create_posts($id,$type,$object);
	}
	
	private function update_categories( $id, $type, $object )
	{
		$this->_get_categories_data($id);
		unset($this->data['category_id']);
		
		return $this->db
				->where('category_id', UUID_TO_BIN($id))
				->update($this->db_table($type),$this->data);
	}
	
	// @STEP2: Only keep 3 archived documents
	public function save_page( $id, $object )
	{
		$current_time = date('Y-m-d H:i:s');
		// Modify new post
		$object->company_id        = UUID_TO_BIN($this->auth_company_id);
		$object->page_id           = UUID_TO_BIN($object->page_id);
		$object->created_by        = UUID_TO_BIN($object->created_by);
		// $object->created_date   = $object->created_date;
		$object->edited_by         = UUID_TO_BIN($this->auth_user_id);
		$object->edited_date       = $current_time;
		$object->name		       = $this->_get_name();
		$object->content		   = $this->_get_pages_document();
		$object->href              = $this->_get_pages_href();
		$object->icon              = $this->_get_pages_icon();
		$object->position          = $this->_get_pages_position();
		$object->layout            = $this->_get_pages_layout();
		$object->status            = $this->_get_pages_status();
		
		// Save categories
		$this->page_category($object->page_id);
		
		// Save old post as archived
		if( isset($object->parent_id) )
		{
			$object->parent_id = UUID_TO_BIN($object->parent_id);
			$q = $this->db->where( 'page_id', $object->parent_id )->get( $this->db_table('pages') );
			
			if( $q->num_rows() === 1 )
			{
				$d = $q->row();
				$d->page_id      = UUID_TO_BIN(UUIDv4());
				$d->parent_id    = $object->parent_id;
				$d->created_date = $current_time;
				$d->status       = 'archived';
				
				$this->db->insert($this->db_table('pages'),$d);
			}
			
			$object->parent_id = NULL;
			return $this->db->where('page_id', $object->page_id)->update($this->db_table('pages'),$object);
		}
		// Save new post
		else
		{
			return $this->db->insert($this->db_table('pages'),$object);
		}
		
		return FALSE;
	}
	
	public function comment( $object, $new = FALSE )
	{
		$object->comment_id = UUID_TO_BIN($object->comment_id);
		$object->post_id    = UUID_TO_BIN($object->post_id);
		$object->created_by = UUID_TO_BIN($object->created_by);
		$object->comment    = $this->_get_posts_comment();
		
		if( $new )
		{
			$this->db
				->where('post_id', $object->post_id)
				->set('comments', 'comments + 1', FALSE)
				->update($this->db_table('posts'));
				
			return $this->db->insert($this->db_table('post_comment'),$object);
		}
		else
		{
			return $this->db->where('comment_id', $object->comment_id)->update($this->db_table('post_comment'),$object);
		}
	}
	
	public function comment_delete( $object )
	{
		$this->db
			->where('post_id', UUID_TO_BIN($object->post_id))
			->set('comments', 'comments - 1', FALSE)
			->update($this->db_table('posts'));
		
		return $this->db->delete($this->db_table('post_comment'), [
			'comment_id' => UUID_TO_BIN($object->comment_id)
		]);
	}
	
	public function is_unique_category_slug( $category )
	{
		// var_dump($category);exit;
		$q = $this->db
				->select('category_id')
				->where('slug', $this->_get_category_slug())
				->where('company_id', UUID_TO_BIN($this->auth_company_id))
				->limit(2)
				->get($this->db_table('categories'));
		
		// How did this happen?
		if( $q->num_rows() >= 2 )
		{
			return FALSE;
		}
		
		// Edit
		if( $category !== NULL )
		{
			if( $row = $q->row() )
			{
				if( $row->category_id !== UUID_TO_BIN($category->category_id) )
				{
					return FALSE;				
				}
			}
		}
		
		// Create
		if( $category === NULL )
		{
			if( $row = $q->row() )
			{
				return FALSE;
			}
		}
		
		// No match. It's unique.
		return TRUE;
	}
	
	public function save_attachments( $attachments, $name = 'post' )
	{
		return $this->db->insert($this->db_table($name . '_attachment'),$attachments);
	}
	
	public function delete_attachments( $attachment_id, $name = 'post' )
	{
		return $this->db->delete($this->db_table($name . '_attachment'),array('attachment_id' => UUID_TO_BIN($attachment_id)));
	}
}