<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Auth_model extends MY_Model
{
	/**
	 * Check the user table to see if a user exists by email address.
	 *
	 * While this query is rather limited, you could easily join with
	 * other custom tables, and return specific user profile data.
	 * 
	 * @param   string  the email address of a user
	 * @return  mixed   either query data as object or FALSE
	 */
	public function get_auth_data( $user_string )
	{
		// Selected user table data
		$selected_columns = [
			'username',
			'name',
			'email',
			'auth_level',
			'passwd',
			'user_id',
			'refresh_token',
			'refresh_token_expiry',
			'totp_secret',
			// 'company_id',
			'banned'
		];

		// User table query
		$query = $this->db->select( $selected_columns )
			->from( $this->db_table('user_table') )
			->where( 'LOWER( email ) =', strtolower( $user_string ) )
			->limit(1)
			->get();

		if( $query->num_rows() == 1 )
		{
			$row = $query->row_array();

			// $acl = array();
			// BIN TO UUID
			$row['user_id'] = BIN_TO_UUID($row['user_id']);
			
			// ACL is added 
			$acl = $this->add_acl_to_auth_data( $row['user_id'] );
			// $row['company_id'] = BIN_TO_UUID($row['company_id']);
			return (object) array_merge( $row, $acl );
		}

		return FALSE;
	}
	
	// --------------------------------------------------------------

	/**
	 * Check the user table to see if a user exists by username.
	 *
	 * While this query is rather limited, you could easily join with
	 * other custom tables, and return specific user profile data.
	 * 
	 * @param   string  the username of a user
	 * @return  mixed   either query data as object or FALSE
	 */
	public function get_auth_data_ldap( $user_string )
	{
		// Selected user table data
		$selected_columns = [
			'username',
			'email',
			'auth_level',
			'passwd',
			'user_id',
			// 'company_id',
			'banned'
		];

		// User table query
		$query = $this->db->select( $selected_columns )
			->from( $this->db_table('user_table') )
			->where( 'LOWER( username ) =', strtolower( $user_string ) )
			->limit(1)
			->get();

		if( $query->num_rows() == 1 )
		{
			$row = $query->row_array();

			// $acl = array();
			// BIN TO UUID
			$row['user_id'] = BIN_TO_UUID($row['user_id']);
			
			// ACL is added 
			$acl = $this->add_acl_to_auth_data( $row['user_id'] );
			// $row['company_id'] = BIN_TO_UUID($row['company_id']);
			return (object) array_merge( $row, $acl );
		}

		return FALSE;
	}
	
	// --------------------------------------------------------------
	
	/**
	 * Check the user table to see if a user exists by email address.
	 *
	 * While this query is rather limited, you could easily join with
	 * other custom tables, and return specific user profile data.
	 * 
	 * @param   string  customer id of a user
	 * @return  mixed   either query data as object or FALSE
	 */
	public function get_auth_data_flex( $user_string )
	{
		// Selected user table data
		$selected_columns = [
			'username',
			'email',
			'auth_level',
			'passwd',
			'user_id',
			// 'company_id',
			'banned'
		];

		// User table query
		$query = $this->db->select( $selected_columns )
			->from( $this->db_table('user_table') )
			->where( 'cid =', $user_string )
			->limit(1)
			->get();

		if( $query->num_rows() == 1 )
		{
			$row = $query->row_array();

			// $acl = array();
			// BIN TO UUID
			$row['user_id'] = BIN_TO_UUID($row['user_id']);
			
			// ACL is added
			$acl = $this->add_acl_to_auth_data( $row['user_id'] );
			// $row['company_id'] = BIN_TO_UUID($row['company_id']);
			return (object) array_merge( $row, $acl );
		}

		return FALSE;
	}
	
	// --------------------------------------------------------------

	/**
	 * During a login attempt or when checking login status, 
	 * ACL permissions may be added to authentication variables, 
	 * but only if the "add_acl_query_to_auth_functions" option is set to 
	 * TRUE in config/authentication.php
	 *
	 * @param  int  the logged in user's user ID
	 */
	public function add_acl_to_auth_data( $user_id )
	{
		$acl = [];
		
		// Add ACL query only if turned on in authentication config
		if( config_item('add_acl_query_to_auth_functions') )
		{
			$acl = $this->acl_query( $user_id, TRUE );
		}
		
		// var_dump($this->groups);

		// return ['acl' => $acl, 'groups' => $this->groups, 'groups_bin' => $this->groups_bin, 'groups_types' => $this->groups_types, 'groups_types_bin' => $this->groups_types_bin];
		return ['acl' => $acl, 'groups' => $this->groups];
	}
	
	// --------------------------------------------------------------
	
	public function get_user_data( $user_id )
	{
		
	}
	
	// --------------------------------------------------------------

	/**
	 * Update the user's user table record when they login
	 * 
	 * @param  array  the user's user table data
	 * @param  string  the login time in MySQL format
	 * @param  array  the session ID 
	 */
	public function login_update( $user_id, $login_time, $session_id )
	{
		if( config_item('disallow_multiple_logins') === TRUE )
		{
			$this->db->where( 'user_id', UUID_TO_BIN($user_id) )
				->delete( $this->db_table('auth_sessions_table') );
		}

		$data = ['last_login' => $login_time];

		$this->db->where( 'user_id' , UUID_TO_BIN($user_id) )
			->update( $this->db_table('user_table') , $data );

		$data = [
			'id'         => $session_id,
			'user_id'    => UUID_TO_BIN($user_id),
			'login_time' => $login_time,
			'ip_address' => $this->input->ip_address(),
			'user_agent' => $this->_user_agent()
		];

		$this->db->insert( $this->db_table('auth_sessions_table') , $data );
	}
	
	// --------------------------------------------------------------

	/**
	 * Return the user agent info for login update
	 */
	protected function _user_agent()
	{
		$this->load->library('user_agent');

		if( $this->agent->is_browser() ){
			$agent = $this->agent->browser() . ' ' . $this->agent->version();
		}else if( $this->agent->is_robot() ){
			$agent = $this->agent->robot();
		}else if( $this->agent->is_mobile() ){
			$agent = $this->agent->mobile();
		}else{
			$agent = 'Unidentified User Agent';
		}

		$platform = $this->agent->platform();

		return $platform 
			? $agent . ' on ' . $platform 
			: $agent; 
	}
	
	// -----------------------------------------------------------------------

	/**
	 * Check user table and confirm there is a record where:
	 *
	 * 1) The user ID matches
	 * 2) The login time matches
	 * 
	 * If there is a matching record, return a specified subset of the record.
	 *
	 * @param   int     the user ID
	 * @return  string  the login time in MySQL format
	 */
	public function check_login_status( $user_id, $login_time )
	{
		// Selected user table data
		$selected_columns = [
			'u.username',
			'u.name',
			'u.god',
			'u.kiv',
			'u.flex',
			'u.email',
			'u.auth_level',
			'u.user_id',
			'u.company_id',
			'u.last_visit',
			'u.banned',
			'u.totp_secret'
		];

		$this->db->select( $selected_columns )
			->from( $this->db_table('user_table') . ' u' )
			->join( $this->db_table('auth_sessions_table') . ' s', 'u.user_id = s.user_id' )
			->where( 's.user_id', UUID_TO_BIN($user_id) )
			->where( 's.login_time', $login_time );

		// If the session ID was NOT regenerated, the session IDs should match
		if( is_null( $this->session->regenerated_session_id ) )
		{
			$this->db->where( 's.id', $this->session->session_id );
		}

		// If it was regenerated, we can only compare the old session ID for this request
		else
		{
			$this->db->where( 's.id', $this->session->pre_regenerated_session_id );
		}

		$this->db->limit(1);
		$query = $this->db->get();

		if( $query->num_rows() == 1 )
		{
			// Update a users last visit
			$this->db->set( 'last_visit', date('Y-m-d H:i:s') )
				->set( 'modified_at', 'modified_at', FALSE )
				->where( 'user_id', UUID_TO_BIN($user_id) )
				->update( $this->db_table('user_table') );
			
			$row = $query->row_array();
			$this->load->helper('cookie');
			$last_visit = get_cookie('last_visit');
			if (date($row['last_visit']) < date('Y-m-d') || $last_visit == NULL) {
				set_cookie(['name'   =>'last_visit', 'value'  => $row['last_visit'],'expire' => '51840000',                                                                                   
													'secure' => TRUE]);
			}
			// var_dump($row);exit;

			// return (object) array_merge( $row, $acl );
			// BIN TO UUID
			$row['user_id'] = BIN_TO_UUID($row['user_id']);
			$row['company_id'] = BIN_TO_UUID($row['company_id']);
			
			// ACL is added
			$acl = $this->add_acl_to_auth_data( $row['user_id'] );
			
			return (object) array_merge( $row, $acl );
		}

		return FALSE;
	}
	
	// -----------------------------------------------------------------------

	/**
	 * Update a user's user record session ID if it was regenerated
	 */
	public function update_user_session_id( $user_id )
	{
		if( ! is_null( $this->session->regenerated_session_id ) )
		{
			$this->db->where( 'user_id', UUID_TO_BIN($user_id) )
				->where( 'id', $this->session->pre_regenerated_session_id )
				->update( 
					$this->db_table('auth_sessions_table'),
					['id' => $this->session->regenerated_session_id]
			);
		}
	}
	
	// -----------------------------------------------------------------------

	/**
	 * Remove the auth session record when somebody logs out
	 * 
	 * @param  int  the user's ID 
	 * @param  string  the session ID
	 */
	public function logout( $user_id, $session_id )
	{
		$this->db->where( 'user_id' , UUID_TO_BIN($user_id) )
			->where( 'id', $session_id )
			->delete( $this->db_table('auth_sessions_table') );
	}
	
	// --------------------------------------------------------------

	/**
	 * Garbage collection routine for old or orphaned auth sessions.
	 * The auth sessions records are normally deleted if the user
	 * logs out, but if they simply close the browser, the record
	 * needs to be removed though garbage collection. This is subject 
	 * to settings you have for sessions in config/config.
	 */
	public function auth_sessions_gc()
	{
		// GC for database based sessions
		if( config_item('sess_driver') == 'database' )
		{
			// Immediately delete orphaned auth sessions
			$this->db->query('
				DELETE a
				FROM `' . $this->db_table('auth_sessions_table') . '` a
				LEFT JOIN `' . $this->db_table('sessions_table') . '` b
				ON  b.id = a.id
				WHERE b.id IS NULL
			');
		}

		// GC for sessions not expiring on browser close
		if( config_item('sess_expiration') != 0 )
		{
			$this->db->query('
				DELETE FROM `' . $this->db_table('auth_sessions_table') . '` 
				WHERE modified_at < CURDATE() - INTERVAL ' . config_item('sess_expiration') . ' SECOND
			');
		}
	}
	
	// -----------------------------------------------------------------------
	
}
