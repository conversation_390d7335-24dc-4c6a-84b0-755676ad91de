<?php
class Menu_model extends MY_Model {
	
	private $data = array();
	
	public function _get_menus_program_id()
	{
		return trim($this->input->post('menus_program_id') ?: '');
	}
	
	public function _get_menus_name()
	{
		return trim($this->input->post('menus_name') ?: '');
	}
	
	public function _get_menus_description()
	{
		return trim($this->input->post('menus_description') ?: '');
	}
	
	public function _get_menus_sticky()
	{
		return trim($this->input->post('menus_sticky') ?: '');
	}
	
	public function _get_menus_position()
	{
		return $this->input->post('menus_position');
	}
	
	public function _get_menus_department()
	{
		return $this->input->post('menus_department');
	}
	
	public function _get_rights_menu()
	{
		return $this->input->post('rights_menu');
	}
	
	public function _get_menus_href()
	{
		return prep_url(trim($this->input->post('menus_href') ?: ''));
	}
	
	public function _get_menus_owner()
	{
		return $this->input->post('menus_owner');
	}
	
	public function _get_menus_move()
	{
		return $this->input->post('move_menu');
	}

	private function _get_data()
	{
		$this->data['name']        = $this->_get_menus_name();
		$this->data['description'] = $this->_get_menus_description();
		$this->data['sticky']      = $this->_get_menus_sticky();
	}
	
	public function create( $menu_id, $parent_id, $type, $callback )
	{
		$this->_get_data();
		$this->data['company_id']    = UUID_TO_BIN($this->auth_company_id);
		$this->data['menu_id']       = UUID_TO_BIN($menu_id);
		$this->data['type']          = Menus::$types[$type];
		if( $parent_id )
			$this->data['parent_id'] = UUID_TO_BIN($parent_id);
		// Load specific functions
		$this->{$callback}();
		return $this->db->insert($this->db_table('menus'),$this->data);
	}
	
	public function create_menu() {}
	
	public function create_folder() {}
	
	public function create_link()
	{
		$this->data['href'] = $this->_get_menus_href();
	}
	
	public function create_app()
	{
		$this->data['program_id'] = $this->_get_menus_program_id();
	}
	
	public function assign_roles($menus, $user_id)
	{
		$data = [];
		foreach($menus as $menu_id => $menu)
		{
			$data[] = [
				'owner'   => UUID_TO_BIN($user_id),
				'menu_id' => UUID_TO_BIN($menu_id)
			];
		}
		return $this->db->update_batch($this->db_table('menus'), $data, 'menu_id');
	}
	
	public function update( $menu_id, $callback )
	{
		$this->_get_data();
		// Load specific functions
		$this->{$callback}();
		$q = $this->db
				->where('menu_id',UUID_TO_BIN($menu_id))
				->update($this->db_table('menus'),$this->data);
		return $q;
	}

	public function update_batch( $data, $id)
	{
		return $this->db->update_batch($this->db_table('menus'), $data, $id);
	}
	
	public function update_menu() {}
	
	public function update_folder() {}
	
	public function update_link()
	{
		$this->data['href'] = $this->_get_menus_href();
	}
	
	public function update_app()
	{
		$this->data['program_id'] = $this->_get_menus_program_id();
	}
	
	public function get( $menu_id )
	{
		$menu    = NULL;
		$menu_id = UUID_TO_BIN($menu_id);
		
		$q = $this->db
				->where('menu_id',$menu_id)
				->limit(1)
				->get($this->db_table('menus'));
		if( $q->num_rows() === 1)
		{
			$menu = $q->row();
			$menu->menu_id    = BIN_TO_UUID($menu->menu_id);
			$menu->parent_id  = BIN_TO_UUID($menu->parent_id);
			$menu->company_id = BIN_TO_UUID($menu->company_id);
			$menu->owner      = BIN_TO_UUID($menu->owner);
		}
		return $menu;
	}
	
	public function get_all($company_id = NULL)
	{
		$menus = array();
		$id    = $company_id !== NULL ? $company_id : $this->auth_company_id;
		$id    = UUID_TO_BIN($id);
		
		$q = $this->db
				->select('menu_id,parent_id,name,type,owner')
				->where('company_id',$id)
				->order_by('sticky', 'ASC')
				->order_by('name', 'ASC')
				->get($this->db_table('menus'));
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $menu)
			{
				$menu->menu_id   = BIN_TO_UUID($menu->menu_id);
				$menu->parent_id = BIN_TO_UUID($menu->parent_id);
				$menu->owner     = BIN_TO_UUID($menu->owner);
				// $menu->name      = preg_replace('/^[0-9\.\s]+/u', '',$menu->name);
				if( $company_id !== NULL )
				{
					$menus[$menu->menu_id] = $menu;
				}
				else
				{
					if($menu->parent_id)
						$menus[$menu->parent_id][$menu->menu_id] = $menu;
					else
						$menus[0][$menu->menu_id] = $menu;
					
					$menus['id'][] = $menu->menu_id;
					
					if($menu->parent_id)
						$menus['parent'][$menu->menu_id] = $menu->parent_id;
				}
			}
		}
		
		return $menus;
	}
	
	public function get_all_by_parent_id( $parent_id = NULL )
	{
		$menus      = array();
		$company_id = UUID_TO_BIN($this->auth_company_id);
		if( $parent_id )
			$parent_id = UUID_TO_BIN($parent_id);
		
		$q = $this->db
				->select('menu_id,parent_id,name,type')
				->where('company_id',$company_id)
				->where('parent_id',$parent_id)
				->order_by('sticky', 'ASC')
				->order_by('name', 'ASC')
				->get($this->db_table('menus'));
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $menu)
			{
				$menu->menu_id   = BIN_TO_UUID($menu->menu_id);
				$menu->parent_id = BIN_TO_UUID($menu->parent_id);
				// $menu->name      = preg_replace('/^[0-9\.\s]+/u', '',$menu->name);
				$menus[$menu->menu_id] = $menu;
			}
		}
		
		return $menus;
	}
	
	public function get_by_groups_bin( $position, $department, $object )
	{
		$menus = array();
		if( ! empty($object) )
		{
			$objects = [];
			$keys = array_keys($object);
			foreach($keys as $key)
			{
				$objects[] = UUID_TO_BIN($key);
			}
		}
		$company_id = UUID_TO_BIN($this->auth_company_id);
		$query = "
				SELECT * 
				FROM  `" . $this->db_table('menus') . "`  
				WHERE 
				(
					`menu_id` IN (
						SELECT `menu_id` 
						FROM   `" . $this->db_table('menu_group') . "` 
						WHERE  `group_id` IN ?
						GROUP  BY `menu_id`
					) AND 
						`menu_id` IN (
						SELECT `menu_id` 
						FROM   `" . $this->db_table('menu_group') . "` 
						WHERE  `group_id` IN ?
						GROUP  BY `menu_id`
					)
				)";
		if( ! empty($object) )
		{
			$query .= " OR menu_id IN ?";
		}
		$query .= "
				AND `company_id` = ?
				GROUP BY 
						`menu_id`
				ORDER BY 
						`sticky` ASC,
						`name` ASC
				";
				
		if( ! empty($object) )
		{
			$q = $this->db->query($query, [$position,$department,$objects,$company_id]);
		}
		else
		{
			$q = $this->db->query($query, [$position,$department,$company_id]);
		}
		
		if( $q->num_rows() !== 0 )
		{
			$this->load->model('folder_model');
			foreach($q->result() as $menu)
			{
				// die();
				// $menu = $q->row();
				$menu->company_id = BIN_TO_UUID($menu->company_id);
				$menu->menu_id    = BIN_TO_UUID($menu->menu_id);
				$menu->parent_id  = BIN_TO_UUID($menu->parent_id);
				$menu->owner      = BIN_TO_UUID($menu->owner);
				
				if($menu->parent_id) {
					$menu->folders = $this->folder_model->get_all( $menu->menu_id );
					$menus['structure'][$menu->parent_id][$menu->menu_id] = $menu;
				} else
					$menus['structure'][0][$menu->menu_id] = $menu;
				
				$menus['all'][$menu->menu_id] = $menu;
				// var_dump($menu);
			}
		}
		
		return $menus;
	}
	
	public function get_first_child( $parent_id = NULL )
	{
		$menu      = NULL;
		$parent_id = UUID_TO_BIN($parent_id);
		
		$q = $this->db
				->where('parent_id',$parent_id)
				->order_by('sticky', 'ASC')
				->order_by('name', 'ASC')
				->limit(1)
				->get($this->db_table('menus'));
		if( $q->num_rows() === 1)
		{
			$menu = $q->row();
			$menu->menu_id    = BIN_TO_UUID($menu->menu_id);
			$menu->parent_id  = BIN_TO_UUID($menu->parent_id);
			$menu->company_id = BIN_TO_UUID($menu->company_id);
		}
		return $menu;
	}

	public function get_owner( $user_id = NULL, $company_id = NULL)
	{
		if( empty($user_id) ) { return NULL; }

		$menus   = array();
		$id      = $company_id !== NULL ? $company_id : $this->auth_company_id;
		$id      = UUID_TO_BIN($id);
		$user_id = UUID_TO_BIN($user_id);
		
		$q = $this->db
				->select('menu_id')
				->where('company_id',$id)
				->where('owner', $user_id)
				->get($this->db_table('menus'));
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $menu)
			{
				$menus[] = BIN_TO_UUID($menu->menu_id);
			}
		}
		
		return $menus;
	}
	
	public function update_position( $menu )
	{
		$menu->name       = $this->_get_menus_name();
		$menu->menu_id    = UUID_TO_BIN($menu->menu_id);
		$menu->company_id = UUID_TO_BIN($menu->company_id);
		
		$q = $this->db
				->where('menu_id',$menu->menu_id)
				->update($this->db_table('menus'),$menu);
		return $q;
	}
	
	public function owner( $menu_id )
	{
		$owner = $this->_get_menus_owner();

		if( empty($owner) )
		{
			$data = ['owner' => NULL ];
		}
		else
		{
			$data = ['owner' => UUID_TO_BIN($owner)];
		}
		
		$q = $this->db
				->where('menu_id',UUID_TO_BIN($menu_id))
				->update($this->db_table('menus'),$data);
		return $q;
	}
	
	public function delete( $menu_id )
	{
		if( $this->db->delete($this->db_table('menus'),array('menu_id' => UUID_TO_BIN($menu_id))) )
		{
			return $this->db->delete($this->db_table('menu_group'),array('menu_id' => UUID_TO_BIN($menu_id)));
		}
		
		return FALSE;
		//menus
		//menu_group
	}
	
	public function move( $menu_id )
	{
		$new_position = $this->_get_menus_move();
		
		if($new_position === '00000000-0000-0000-0000-000000000000')
			$data = ['parent_id' => NULL];
		else
			$data = ['parent_id' => UUID_TO_BIN($new_position)];

		$q = $this->db
				->where('menu_id',UUID_TO_BIN($menu_id))
				->update($this->db_table('menus'),$data);
				
		if( $q )
			return $new_position;
		else
			return $q;
	}
	
}
