<?php

class Task_model extends MY_Model {

	public function _get_tasks_name()
	{
		return trim($this->input->post('tasks_name') ?: '');
	}

	public function _get_tasks_description()
	{
		return trim($this->input->post('tasks_description') ?: '');
	}

	public function _get_tasks_created()
	{
		return trim($this->input->post('tasks_created') ?: '');
	}

	public function _get_tasks_accepted_by()
	{
		return trim($this->input->post('tasks_accepted_by') ?: '');
	}

	public function _get_tasks_type()
	{
		return trim($this->input->post('tasks_type') ?: '');
	}

	// public function _get_tasks_duration()
	// {
	// 	return trim($this->input->post('tasks_duration') ?: '');
	// }

	public function _get_tasks_last_revised()
	{
		return trim($this->input->post('tasks_last_revised') ?: '');
	}

	public function _get_tasks_finish_date()
	{
		return trim($this->input->post('tasks_finish_date') ?: '');
	}

	public function _get_tasks_hours_needed()
	{
		return trim($this->input->post('tasks_hours_needed') ?: '');
	}

	public function _get_tasks_goal_type()
	{
		return trim($this->input->post('tasks_goal_type') ?: '');
	}

	public function _get_tasks_reminder()
	{
		return trim($this->input->post('tasks_reminder') ?: '');
	}

  public function _get_tasks_metric_type()
	{
		return trim($this->input->post('tasks_metric_type') ?: '');
	}

  public function _get_tasks_target()
	{
		return trim($this->input->post('tasks_target') ?: '');
	}

	public function _get_tasks_targets()
	{
		return trim($this->input->post('tasks_targets') ?: '');
	}

  public function _get_tasks_recurrence()
	{
		return trim($this->input->post('tasks_recurrence') ?: '');
	}

  public function _get_tasks_state()
	{
		return trim($this->input->post('tasks_state') ?: '');
	}

	public function _get_tasks_owner()
	{
		return trim($this->input->post('tasks_owner') ?: '');
	}

	public function _get_tasks_resources()
	{
		return $this->input->post('tasks_editors[]');
	}

	public function _get_tasks_groups()
	{
		return $this->input->post('tasks_groups[]');
	}

	public function get_task_type_data()
	{
		if ($this->db->table_exists($this->db_table('task_type')) ) {
			$q = 	$this->db->select('*')->from( $this->db_table('task_type'))->get();
			$res = [];
			foreach($q->result() as $e)
			{
				$res[$e->type_id] = $e->type;
			}
			return $res;
		} else {
			return [];
		}
	}

	public function get_task_metric_type_data()
	{
		$q = $this->db->select('*')->from( $this->db_table('task_metric_type'))->get();
		$res = [];
		foreach($q->result() as $e)
		{
			$res[$e->metric_type_id] = $e->metric_type;
		}
		return $res;
	}

	public function add_task_metric_type($metric_type)
	{
		$this->db->insert( $this->db_table('task_metric_type'), ['metric_type' => $metric_type]);
		return $this->db->insert_id();
	}

	public function add_task_type($type)
	{
		$this->db->insert( $this->db_table('task_type'), ['type' => $type]);
		return $this->db->insert_id();
	}

	public function edit_task_type($type_id, $type)
	{
		$this->db->where('type_id', $type_id)->update( $this->db_table('task_type'), ['type' => $type]);
	}

	public function edit_task_metric_type($metric_type_id, $metric_type)
	{
		$this->db->where('metric_type_id', $metric_type_id)->update( $this->db_table('task_metric_type'), ['metric_type' => $metric_type]);
	}

	public function delete_task_metric_type($metric_type_id)
	{
		$this->db->delete( $this->db_table('task_metric_type'), ['metric_type_id' => $metric_type_id]);
	}

	public function delete_task_type($type_id)
	{
		$this->db->delete( $this->db_table('task_type'), ['type_id' => $type_id]);
	}

	public function get( $task_id, $status = array('published','draft', 'unpublished') )
	{
		$task    = NULL;
		$task_id = UUID_TO_BIN($task_id);

		$q = $this->db
				->where('task_id',$task_id)
				->where_in('status',$status)
				->limit(1)
				->get($this->db_table('tasks'));
		if( $q->num_rows() === 1)
		{
			$task = $q->row();
			$task->task_id           = BIN_TO_UUID($task->task_id);
			$task->parent_id         = BIN_TO_UUID($task->parent_id);
      $task->company_id        = BIN_TO_UUID($task->company_id);
			$task->created_by        = BIN_TO_UUID($task->created_by);
			$task->edited_by         = BIN_TO_UUID($task->edited_by);
			$task->accepted_by       = BIN_TO_UUID($task->accepted_by);
			$task->owner             = BIN_TO_UUID($task->owner);
		}
		return $task;
	}

	public function get_all( $task_id, $status = array('published') )
	{
		// var_dump($task_id);exit;
		$tasks    = [];
		$tasks_id = [];
		if( is_array($task_id) )
		{
			foreach($task_id as $id)
			{
				$tasks_id[] = UUID_TO_BIN($id);
			}
		}
		else
		{
			$tasks_id[] = UUID_TO_BIN($task_id);
		}

		$q = $this->db
				->where_in('task_id',$tasks_id)
				->where_in('status',$status)
				->order_by('name','ASC')
				->get($this->db_table('tasks'));

		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $task)
			{
        $task->task_id           = BIN_TO_UUID($task->task_id);
        $task->parent_id         = BIN_TO_UUID($task->parent_id);
        $task->company_id        = BIN_TO_UUID($task->company_id);
        $task->created_by        = BIN_TO_UUID($task->created_by);
        $task->edited_by         = BIN_TO_UUID($task->edited_by);
        $task->accepted_by       = BIN_TO_UUID($task->accepted_by);
        $task->owner             = BIN_TO_UUID($task->owner);
				$tasks[$task->task_id] = $task;
			}
		}

		return $tasks;
	}

	public function get_task_exists( $task_id, $version = FALSE )
	{
		$task_id = UUID_TO_BIN($task_id);
		$company_id  = UUID_TO_BIN($this->auth_company_id);

		$q = $this->db
				->select('task_id, last_revised')
				->where('task_id', $task_id)
				->where('company_id', $company_id)
				->limit(1)
				->get('tasks');
		if( $q->num_rows() === 1)
		{
			if( $version )
				return $q->row()->task_last_revised;
			else
				return TRUE;
		}

		return FALSE;
	}

	public function get_all_by_owner( $owner = NULL, $status = array('published','draft') )
	{
		$tasks = [];
		$is_array  = FALSE;

		if( is_array($owner) )
		{
			$is_array = TRUE;
			$owners = [];
			foreach($owner as $user_id)
			{
				$owners[] = UUID_TO_BIN($user_id);
			}
			$this->db->where_in('tasks.owner',$owners);
		}
		else
		{
			$owner = ! empty($owner) ? $owner : $this->auth_user_id;
			$owner = '0x'.str_replace('-','',$owner);
		}

		$q = $this->db->select('tasks.*, COALESCE(sum(tp1.progress),0) + COALESCE(avg(tp2.progress), 0) as progress')
				->from( $this->db_table('tasks') )
				->join( $this->db_table('task_resource') , 'tasks.task_id = task_resource.task_id', 'left')
				->join( $this->db_table('task_progress'). ' AS tp1' , '(tasks.task_id = tp1.task_id and tasks.goal_type = 0)', 'left')
				->join( $this->db_table('task_progress'). ' AS tp2' , '(tasks.task_id = tp2.task_id and tp2.created_date > DATE_SUB(CURRENT_DATE(), INTERVAL DAYOFMONTH(CURRENT_DATE())-1 DAY))', 'left')
				;

		if ( !is_array($owner))
		{
			$q = $q->where("tasks.parent_id is null and (tasks.owner = " . $owner . " or 
				task_resource.user_id = " . $owner . " or 
				tasks.task_id IN (
					select task_id from `task_group` left join user_group on user_group.group_id = task_group.group_id
					where user_id = " . $owner . ")
				)", NULL, FALSE);
		}
		$q = $q->where_in('status',$status)
				->group_by('task_id, task_resource.user_id')
				->order_by('name','ASC')
				->get();

		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $task)
			{
        $task->task_id           = BIN_TO_UUID($task->task_id);
        $task->parent_id         = BIN_TO_UUID($task->parent_id);
        $task->company_id        = BIN_TO_UUID($task->company_id);
        $task->created_by        = BIN_TO_UUID($task->created_by);
        $task->edited_by         = BIN_TO_UUID($task->edited_by);
        $task->accepted_by       = BIN_TO_UUID($task->accepted_by);
        $task->owner             = BIN_TO_UUID($task->owner);
				if ($task->goal_type == 0) {
					$targets = json_decode($task->targets, true);
					$task->target = empty($targets) ? 1 : (end($targets)[1] ? (int) end($targets)[1] : 1); // Now it taked the last
					// if sum use below
					// $task->target = array_sum(array_map(function($item) {
					// 	return $item[1];
					// }, $targets));
				} elseif ($task->target == 0) {
					$targets = json_decode($task->targets, true);
					$task->target = empty($targets) ? 1 : (end($targets)[1] ? (int) end($targets)[1] : 1);
				}
				if( $is_array )
				{
					$tasks[$task->owner][$task->status][$task->task_id] = $task;
				}
				else
				{
					$tasks[$task->status][$task->task_id] = $task;
				}
			}
		}

		return $tasks;
	}

	public function get_all_by_parent_id( $parent_id, $status = array('archived'), $return = NULL )
	{
		$tasks    = [];
		$tasks_id = [];
		if( is_array($parent_id) )
		{
			foreach($parent_id as $id)
			{
				$tasks_id[] = UUID_TO_BIN($id);
			}
		}
		else
		{
			$tasks_id[] = UUID_TO_BIN($parent_id);
		}

		$q = $this->db
				->where_in('parent_id',$tasks_id)
				->where_in('status',$status)
				->order_by('edited_date','DESC')
				->get($this->db_table('tasks'));

		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $task)
			{
        $task->task_id           = BIN_TO_UUID($task->task_id);
        $task->parent_id         = BIN_TO_UUID($task->parent_id);
        $task->company_id        = BIN_TO_UUID($task->company_id);
        $task->created_by        = BIN_TO_UUID($task->created_by);
        $task->edited_by         = BIN_TO_UUID($task->edited_by);
        $task->accepted_by       = BIN_TO_UUID($task->accepted_by);
        $task->owner             = BIN_TO_UUID($task->owner);
				if( ! empty($return) )
				{
					if( count($status) === 1)
						$tasks[$task->{$return}] = $task;
					else
						$tasks[$task->status][$task->{$return}] = $task;
				}
				else
				{
					if( count($status) === 1)
						$tasks[$task->task_id] = $task;
					else
						$tasks[$task->status][$task->task_id] = $task;
				}
			}
		}

		return $tasks;
	}

	public function get_draft( $parent_id )
	{
		$task = NULL;
		$parent_id = UUID_TO_BIN($parent_id);

		$q = $this->db
				->where_in('parent_id', $parent_id)
				->where_in('status', ['draft'])
				->order_by('edited_date','DESC')
				->get($this->db_table('tasks'));

		if( $q->num_rows() !== 0 )
		{
			$task = $q->result()[0];
			$task->task_id = BIN_TO_UUID($task->task_id);
		}
		return $task;
	}

	public function save( $task, $status = 'draft', $rights = FALSE )
	{
		// Store a local value, as it gets overwritten
		$task_status = $task->status;
		// var_dump($task);
		$current_time = date('Y-m-d H:i:s');
		// Modify new task
		$task->task_id       = UUID_TO_BIN($task->task_id);
    $task->company_id        = UUID_TO_BIN($this->auth_company_id);
		$task->created_by        = UUID_TO_BIN($task->created_by);
		$task->edited_by         = UUID_TO_BIN($this->auth_user_id);
		$task->edited_date       = $current_time;
		$task->name		         = $this->_get_tasks_name();
		$task->description       = $this->_get_tasks_description();
		$task->created		     = $this->_get_tasks_created();
		// $task->accepted_by	     = UUID_TO_BIN($this->_get_tasks_accepted_by());
		$task->task_type     = $this->_get_tasks_type();
    // $task->duration      = $this->_get_tasks_duration();
    $task->finish_date      = $this->_get_tasks_finish_date();
		$task->hours_needed      = $this->_get_tasks_hours_needed();
		$task->goal_type      = $this->_get_tasks_goal_type();
    $task->reminder         = $this->_get_tasks_reminder();
    $task->metric_type      = $this->_get_tasks_metric_type();
    $task->target      = $this->_get_tasks_target();
		// if (empty($task->target))
		// {
		// 	$task->target = 0;
		// }
		$task->targets      = $this->_get_tasks_targets();
    $task->recurrence      = $this->_get_tasks_recurrence();
    $task->state      = $this->_get_tasks_state();
		$task->last_revised      = $this->_get_tasks_last_revised();

		// Systemadministratör, owner, and menu owner
		if( $rights )
			$task->owner         = $this->_get_tasks_owner();
		if( empty($task->owner) )
			$task->owner         = $this->auth_user_id;
		// Conver to binary
		$task->owner			 = UUID_TO_BIN($task->owner);
		// published | draft | archived | unpublished
		$task->status            = $status;
		// Save old task as archived
		if( isset($task->parent_id) && ! in_array($status, ['draft']) )
		{
			// $task->parent_id = UUID_TO_BIN($task->parent_id);
			$q = $this->db->where( 'task_id', $task->task_id )->get( $this->db_table('tasks') );

			if( $q->num_rows() === 1 )
			{
				$d = $q->row();
				$d->task_id  = UUID_TO_BIN(UUIDv4());
				$d->parent_id    = $task->task_id;
				$d->created_date = $current_time;
				$d->status       = 'archived';

				$this->db->insert($this->db_table('tasks'),$d);
			}

			$task->parent_id = NULL;
			return $this->db->where('task_id', $task->task_id)->update($this->db_table('tasks'),$task);
		}
		// Save draft
		else if( isset($task->parent_id) )
		{
			$tmp_task_id       = UUID_TO_BIN($task->parent_id);
			$tmp_parent_id         = $task->task_id;
			$task->parent_id   = $tmp_parent_id;
			$task->task_id = $tmp_task_id;

			// Remove draft
			$this->db
				->where('parent_id', $task->parent_id)
				->where_in('status', ['draft'])
				->delete($this->db_table('tasks'));

			return $this->db->insert($this->db_table('tasks'),$task);
		}
		// Save new task
		else
		{
			return $this->db->insert($this->db_table('tasks'),$task);
		}

		return FALSE;
	}

	public function create_draft($task)
	{
		$current_time = date('Y-m-d H:i:s');
		// Modify new task
		$task->created_by        = UUID_TO_BIN($task->created_by);
		// $task->created_date   = $task->created_date;
		$task->edited_by         = UUID_TO_BIN($this->auth_user_id);
		$task->company_id        = UUID_TO_BIN($task->company_id);
		$task->owner			 			 = UUID_TO_BIN($task->owner);
		$task->edited_date       = $current_time;
		$task->status            = 'draft';

		// Save draft
		$task->parent_id   = UUID_TO_BIN($task->task_id);
		$task->task_id = UUID_TO_BIN(UUIDv4());

		// Remove draft
		$this->db
			->where('parent_id', $task->parent_id)
			->where_in('status', ['draft'])
			->delete($this->db_table('tasks'));

		$this->db->insert($this->db_table('tasks'),$task);

	}

	public function publish( $task )
	{
		// var_dump($task);
		$current_time = date('Y-m-d H:i:s');
		// UUUID TO BIN
		$task->task_id       = UUID_TO_BIN($task->task_id);
    $task->company_id        = UUID_TO_BIN($task->company_id);
		$task->created_by        = UUID_TO_BIN($task->created_by);
		$task->edited_by         = UUID_TO_BIN($task->edited_by);
		$task->owner			 = UUID_TO_BIN($task->owner);
		// published | draft | archived | unpublished
		$task->status            = 'published';

		// var_dump($task);exit;

		// Save old task as archived
		if( isset($task->parent_id) )
		{
			$parent_id = $task->parent_id;
			$task_id = $task->parent_id = UUID_TO_BIN($task->parent_id);
			$q = $this->db->where('task_id', $task->parent_id )->where_in('status', ['published','archived'])->get( $this->db_table('tasks') );

			if( $q->num_rows() === 1 )
			{
				$newUUid = UUIDv4();
				$d = $q->row();
				$d->task_id  = UUID_TO_BIN($newUUid);
				$d->parent_id    = $task->parent_id;
				$d->created_date = $current_time;
				$d->status       = 'archived';

				$this->db->insert($this->db_table('tasks'),$d);
			}

			// Remove draft
			$this->db
				->where('parent_id', $task->parent_id)
				->where_in('status', ['draft'])
				->delete($this->db_table('tasks'));

			// Update original from draft
			$task->task_id = $task_id;
			$task->parent_id = NULL;
			return $this->db->where('task_id', $task_id)->update($this->db_table('tasks'),$task);
		}
		else
		{
			$this->db->where('task_id', $task->task_id)->update($this->db_table('tasks'),$task);
		}

		return FALSE;
	}

	public function accepted( $task_id )
	{
		return $this->db->where('task_id', $task_id)->update($this->db_table('tasks'), [
			'status' => 'draft'
		]);
	}

	public function get_task_progress( $progress_id )
	{
		$progress    = NULL;
		$progress_id = UUID_TO_BIN($progress_id);

		$q = $this->db
				->where('progress_id',$progress_id)
				->limit(1)
				->get($this->db_table('task_progress'));
		if( $q->num_rows() === 1)
		{
			$progress = $q->row();
			$progress->task_id        = BIN_TO_UUID($progress->task_id);
			$progress->progress_id    = BIN_TO_UUID($progress->progress_id);
      $progress->user_id        = BIN_TO_UUID($progress->user_id);
		}
		return $progress;
	}

	public function add_progress( $progress )
	{
		$progress->progress_id = UUID_TO_BIN($progress->progress_id);
		$progress->task_id = UUID_TO_BIN($progress->task_id);
		$progress->user_id = UUID_TO_BIN($progress->user_id);
		$this->db->insert($this->db_table('task_progress'), $progress);
		$this->db->update($this->db_table('tasks'),['state' => 1], ['task_id' => $progress->task_id]);
	}

	public function edit_progress( $progress )
	{
		$progress->progress_id = UUID_TO_BIN($progress->progress_id);
		$progress->task_id = UUID_TO_BIN($progress->task_id);
		$progress->user_id = UUID_TO_BIN($progress->user_id);
		$this->db->update($this->db_table('task_progress'), $progress, ['progress_id' => $progress->progress_id]);
	}

	public function get_progress_data( $task_id )
	{
		$task_id = UUID_TO_BIN($task_id);
		$q = $this->db->select('*')->from( $this->db_table('task_progress'))
			->where('task_id', $task_id)->order_by('date(created_date)', 'asc')->get();
		$res = [];
		foreach($q->result() as $progress)
		{
			$progress->user_id       = BIN_TO_UUID($progress->user_id);
			$progress->progress_id   = BIN_TO_UUID($progress->progress_id);
			$res[] = $progress;
		}
		return $res;
	}

	public function get_progress( $task_id )
	{
		$task_id = UUID_TO_BIN($task_id);
		$q = $this->db->select('sum(progress) as progress, date(created_date) as created_date')->from( $this->db_table('task_progress'))
			->where('task_id', $task_id)->group_by('date(created_date)')->order_by('date(created_date)', 'asc')->get();
		$res = [];
		$total = 0;
		foreach($q->result() as $e)
		{
			$total += $e->progress;
			$res[] = [
				"progress" => $total, 
				"date" => $e->created_date
			];
		}
		return $res;
	}

	public function get_monitoring_progress( $task_id )
	{
		$task_id = UUID_TO_BIN($task_id);
		$q = $this->db->select("progress as progress, created_date")->from( $this->db_table('task_progress'))
			->where('task_id', $task_id)->order_by("created_date")->get();
		$res = [];
		foreach($q->result() as $e)
		{
			$res[] = [
				"progress" => $e->progress, 
				"date" => $e->created_date
			];
		}
		return $res;
	}

	public function get_progress_by_user( $task_id, $monitoring = false )
	{
		$task_id = UUID_TO_BIN($task_id);
		$q = $this->db->select(($monitoring == true ? 'avg' : 'sum') .'(progress) as progress, user_id')->from( $this->db_table('task_progress'));
		if ($monitoring == true)
			$q = $q->where("created_date > DATE_SUB(CURRENT_DATE(), INTERVAL DAYOFMONTH(CURRENT_DATE())-1 DAY)");
		$q = $q->where('task_id', $task_id)->group_by('user_id')->get();
		$res = [];
		foreach($q->result() as $e)
		{
			$res[] = [
				"progress" => $e->progress, 
				"user" => BIN_TO_UUID($e->user_id),
			];
		}
		return $res;
	}

	// @TODO: God mode, delete tasks
	// public function remove( $table, $column, $id )
	// {
		// return $this->db->delete($this->db_table($table),array($column => UUID_TO_BIN($id)));
	// }

	public function archive($task_id)
	{
		return $this->db->update($this->db_table('tasks'),['status' => 'unpublished'], ['task_id' => UUID_TO_BIN($task_id)]);
	}

	public function update_edit_date( $task_id )
	{
		return $this->db->update($this->db_table('tasks'),['edited_date' => date('Y-m-d H:i:s')], ['task_id' => UUID_TO_BIN($task_id)]);
	}

	public function tasks_resources( $id, $users )
	{
		if( $this->tasks_resources_remove($id) === FALSE) { return FALSE; }
		if( empty($users) ) { return TRUE; }
		$data = array();
		foreach($users as $user_id)
		{
			$data[] = array(
				'task_id' => UUID_TO_BIN($id),
				'user_id'     => UUID_TO_BIN($user_id)
			);
		}

		if( ! empty($data) )
			return $this->db->insert_batch($this->db_table('task_resource'),$data);

		return FALSE;
	}

	private function tasks_resources_remove( $id )
	{
		return $this->db->delete($this->db_table('task_resource'),array('task_id' => UUID_TO_BIN($id)));
	}

	public function tasks_groups( $id, $groups )
	{
		if( $this->tasks_groups_remove($id) === FALSE) { return FALSE; }
		if( empty($groups) ) { return TRUE; }
		$data = array();
		foreach($groups as $group_id)
		{
			$data[] = array(
				'task_id' => UUID_TO_BIN($id),
				'group_id' => UUID_TO_BIN($group_id)
			);
		}

		if( ! empty($data) )
			return $this->db->insert_batch($this->db_table('task_group'),$data);

		return FALSE;
	}

	private function tasks_groups_remove( $id )
	{
		return $this->db->delete($this->db_table('task_group'),array('task_id' => UUID_TO_BIN($id)));
	}

	public function change_owner()
	{
		$from = $this->input->post('tasks_owner_from');
		$to = $this->input->post('tasks_owner_to');

		if( empty($from) OR empty($to) )
			return NULL;

		if( ! in_array($from, array_keys($this->users_all)) OR ! in_array($to, array_keys($this->users)) )
			return FALSE;

		return $this->db->where('owner', UUID_TO_BIN($from))->update($this->db_table('tasks'),[
			'owner' => UUID_TO_BIN($to)
		]);
	}

	public function change_owner_individual()
	{
    $task_id = $this->input->post('task_id');
		$to = $this->input->post('tasks_owner_to');

		if( empty($task_id) OR empty($to) )
			return NULL;

		if( ! in_array($to, array_keys($this->users)) )
			return FALSE;

		return $this->db->where('task_id', UUID_TO_BIN($task_id))->update($this->db_table('tasks'),[
			'owner' => UUID_TO_BIN($to)
		]);
	}
}
