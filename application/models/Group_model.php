<?php
class Group_model extends MY_Model {
	
	public function _get_groups_name()
	{
		return trim($this->input->post('groups_name') ?: '');
	}
	
	public function _get_groups_type()
	{
		return trim($this->input->post('groups_type') ?: '');
	}
	
	public function _get_groups_type_security()
	{
		return $this->input->post('groups_security[]');
	}

	private function _get_data()
	{
		$data = array(
			'name'		=> $this->_get_groups_name(),
			'type'		=> $this->_get_groups_type(),
		);
		return $data;
	}
	
	public function create( $group_id )
	{
		$data = $this->_get_data();
		$data['company_id'] = UUID_TO_BIN($this->auth_company_id);
		$data['group_id']   = UUID_TO_BIN($group_id);
		return $this->db->insert($this->db_table('groups'),$data);
	}
	
	public function get( $group_id, $company_id = NULL )
	{
		$group    = NULL;
		$group_id = UUID_TO_BIN($group_id);
		$company_id = $company_id !== NULL ? $company_id : $this->auth_company_id;
		$company_id = UUID_TO_BIN($company_id);
		
		$q = $this->db
				->where('group_id',$group_id)
				->where('company_id',$company_id)
				->limit(1)
				->get($this->db_table('groups'));
		if( $q->num_rows() === 1)
		{
			$group = $q->row();
			$group->group_id   = BIN_TO_UUID($group->group_id);
			$group->company_id = BIN_TO_UUID($group->company_id);
		}
		return $group;
	}
	
	public function get_all( $groups_id = array(), $type = NULL, $default = array(), $reverse = FALSE, $company_id = NULL )
	{
		$company_id = $company_id !== NULL ? $company_id : $this->auth_company_id;
		$company_id = UUID_TO_BIN($company_id);
		
		$groups = array(
			'department' => [],
			'position'   => [],
			'security'   => [],
			'education'  => []
		);
		if( ! empty($default) )
		{
			$groups = $default;
		}
		$groups_id_bin = array();
		
		if( !empty($groups_id) )
		{
			foreach($groups_id AS $group_id)
			{
				$groups_id_bin[] = UUID_TO_BIN($group_id);
			}
			$this->db->where_in('group_id',$groups_id_bin);
		}
		
		if( $type !== NULL )
		{
			$this->db->where('type',$type);
		}
		
		$q = $this->db
				->select('group_id,type,name')
				->where('company_id',$company_id)
				->order_by('type', 'ASC')
				->order_by('name', 'ASC')
				->get($this->db_table('groups'));
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $group)
			{
				$group->group_id = BIN_TO_UUID($group->group_id);
				if( $reverse )
					$groups[$group->type][$group->name] = $group->group_id;
				else
					$groups[$group->type][$group->group_id] = $group->name;
			}
		}
		
		return $groups;
	}
	
	// SELECT * FROM `groups` INNER JOIN `user_group` ON `groups`.`group_id`=`user_group`.`group_id`

	
	public function get_relationships( $table, $column, $id )
	{
		$groups = array();
		$ids   = array();
		foreach($id as $row)
		{
			$ids[] = UUID_TO_BIN($row);
		}
		
		$q = $this->db
				->where_in($column,$ids)
				->get($this->db_table($table));
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $group)
			{
				$groups[BIN_TO_UUID($group->$column)][] = BIN_TO_UUID($group->group_id);
			}
		}
		
		return $groups;
	}
	
	public function get_all_by_relationship( $table, $column, $id, $data = TRUE, $type = FALSE, $return = NULL )
	{
		$id         = (array)  $id;
		$groups_id  = $this->_get_relationship($table, $column, $id, $type, $return);
		if( empty($groups_id) ) { return array(); }
		// if( !$data ) { return $groups_id['uuid']; }
		if( !$data ) { return $groups_id; }
		
		return $this->get_all( $groups_id );
		
		// $groups     = array();
		// $company_id = UUID_TO_BIN($this->auth_company_id);
		
		// $q = $this->db
				// ->select('group_id,type,name')
				// ->where('company_id',$company_id)
				// ->where_in('group_id',$groups_id['bin'])
				// ->order_by('type', 'ASC')
				// ->order_by('name', 'ASC')
				// ->get($this->db_table('groups'));
				
		// if( $q->num_rows() !== 0 )
		// {
			// foreach($q->result() as $group)
			// {
				// $group->group_id = BIN_TO_UUID($group->group_id);
				// $groups[$group->type][$group->group_id] = $group->name;
			// }
		// }
		
		// return $groups;
	}
	
	private function _get_relationship( $table, $column, $id, $type, $return )
	{
		$groups = array();
		$ids   = array();
		foreach($id as $row)
		{
			$ids[] = UUID_TO_BIN($row);
		}
		
		if( $return )
		{
			$this->db->select('a.' . $return);
		}
		elseif( $type )
		{
			$this->db
				->select('a.group_id,b.type')
				->join($this->db_table('groups') . ' b', 'b.group_id = a.group_id');
		}
		else
		{
			$this->db->select('a.group_id');
		}
		
		$q = $this->db
				->where_in($column,$ids)
				->get($this->db_table($table) . ' a');
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $group)
			{
				if( $return )
				{
					$groups[] = BIN_TO_UUID($group->{$return});
				}
				elseif( $type )
				{
					$groups[$group->type][] = BIN_TO_UUID($group->group_id);
				}
				else
				{
					// $groups['uuid'][] = BIN_TO_UUID($group->group_id);
					// $groups['bin'][]  = $group->group_id;
					$groups[] = BIN_TO_UUID($group->group_id);
				}
			}
		}
		
		return $groups;
	}
	
	public function update( $group )
	{
		$group->name = $this->_get_groups_name();
		
		$q = $this->db
				->where('group_id',$group->group_id)
				->update($this->db_table('groups'),$group);
		return $q;
	}
	
	public function save( $table, $column, $id, $groups, $delete_groups = array() )
	{
		if( $this->remove($table,$column,$id,$delete_groups) === FALSE) { return FALSE; }
		if( empty($groups) ) { return TRUE; }
		$data = array();
		foreach($groups as $group_id)
		{
			$data[] = array(
				$column    => UUID_TO_BIN($id),
				'group_id' => UUID_TO_BIN($group_id)
			);
		}

		if( ! empty($data) )
			return $this->db->insert_batch($this->db_table($table),$data);
		
		return FALSE;
	}
	
	public function remove( $table, $column, $id, $delete_groups )
	{
		$delete = array();
		if( !empty($delete_groups) )
		{
			foreach($delete_groups as $group_id)
			{
				$delete[] = UUID_TO_BIN($group_id);
			}
			$this->db->where_in('group_id', $delete);
		}
		return $this->db->delete($this->db_table($table),array($column => UUID_TO_BIN($id)));
	}
	
	public function save_reverse( $table, $column, $id, $items, $delete_items = array() )
	{
		if( $this->remove_reverse($table,$column,$id,$delete_items) === FALSE) { return FALSE; }
		if( empty($items) ) { return TRUE; }
		$data = array();
		foreach($items as $item)
		{
			$data[] = array(
				$column    => UUID_TO_BIN($item),
				'group_id' => UUID_TO_BIN($id)
			);
		}

		if( ! empty($data) )
			return $this->db->insert_batch($this->db_table($table),$data);
		
		return FALSE;
	}
	
	public function remove_reverse( $table, $column, $id, $delete_items )
	{
		$delete = array();
		if( !empty($delete_items) )
		{
			foreach($delete_items as $item_id)
			{
				$delete[] = UUID_TO_BIN($item_id);
			}
			$this->db->where_in($column, $delete);
		}
		return $this->db->delete($this->db_table($table),array('group_id' => UUID_TO_BIN($id)));
	}

	public function delete( $group_id = NULL )
	{
		if( empty($group_id) ) { return NULL; }

		$group_id = UUID_TO_BIN($group_id);

		if( ! $this->db->delete($this->db_table('acl_users_and_groups_table'),array('group_id' => $group_id)) )
			return FALSE;
		if( ! $this->db->delete($this->db_table('category_group'),array('group_id' => $group_id)) )
			return FALSE;
		if( ! $this->db->delete($this->db_table('document_education_group'),array('group_id' => $group_id)) )
			return FALSE;
		if( ! $this->db->delete($this->db_table('document_group'),array('group_id' => $group_id)) )
			return FALSE;
		if( ! $this->db->delete($this->db_table('folder_group'),array('group_id' => $group_id)) )
			return FALSE;
		if( ! $this->db->delete($this->db_table('groups'),array('group_id' => $group_id)) )
			return FALSE;
		if( ! $this->db->delete($this->db_table('menu_group'),array('group_id' => $group_id)) )
			return FALSE;
		if( ! $this->db->delete($this->db_table('survey_group'),array('group_id' => $group_id)) )
			return FALSE;
		if( ! $this->db->delete($this->db_table('user_group'),array('group_id' => $group_id)) )
			return FALSE;

		return TRUE;
	}

	public function in_use($table = NULL, $column = 'group_id')
	{
		if( empty($table) ) { return NULL; }

		$groups = [];

		$q = $this->db
				->select($column)
				->group_by($column)
				->get($this->db_table($table));
				
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $group)
			{
				$groups[] = BIN_TO_UUID($group->{$column});

			}
		}
		
		return $groups;
	}
	
	public function is_unique()
	{
		$company_id = UUID_TO_BIN($this->auth_company_id);
		
		$q = $this->db
				->select('group_id')
				->where('company_id',$company_id)
				->where('name',$this->_get_groups_name())
				->where('type',$this->_get_groups_type())
				->limit(1)
				->get($this->db_table('groups'));

		if( $q->row() )
		{
			return FALSE;
		}
		return TRUE;
	}
	
	public function is_unique_update( $group )
	{
		$group->group_id   = UUID_TO_BIN($group->group_id);
		$group->company_id = UUID_TO_BIN($group->company_id);
		
		$q = $this->db
				->select('group_id')
				->where('company_id',$group->company_id)
				->where('name',$this->_get_groups_name())
				->where('type',$group->type)
				->limit(2)
				->get($this->db_table('groups'));

		if( $q->num_rows() >= 2 )
		{
			return FALSE;
		}
		
		if( $row = $q->row() )
		{
			if( $row->group_id !== $group->group_id )
			{
				return FALSE;				
			}
		}
		
		return TRUE;
	}
}
