<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Resources extends Auth_Controller
{
	public function __construct()
	{
		parent::__construct();

		if( !$this->is_logged_in() )
			show_404();
	}

	public function uploads()
	{
		if(($count = $this->uri->total_segments()) < 3)
			show_404();

		if(($segments = $this->_segments()) && strlen($segments) < 1)
			show_404();

		if(!in_array($segments, $this->_files(), TRUE))
			show_404();

		$path = CI_UPLOAD_PATH . 'elfinder' . DIRECTORY_SEPARATOR;
		$file = $path . $segments;

		if(file_exists($file))
		{
			if(!function_exists('mime_content_type'))
				show_404();

			$mimeType = mime_content_type($file);

			if($this->_is_image($mimeType))
			{
				if(($image = file_get_contents($file)) === FALSE)
					show_404();

				$this->output
					->set_status_header(200)
					->set_content_type($mimeType)
					->set_output($image)
					->_display();
				exit;
			}
			elseif($mimeType === 'application/pdf')
			{
				if(($pdf = file_get_contents($file)) === FALSE)
					show_404();

				$this->load->helper('download');
				force_download(basename($segments), $pdf, TRUE);
			}
		}

		show_404();
	}

	private function _segments()
	{
		$segments = $this->uri->segment_array();

		// resources
		unset($segments[1]);
		// uploads
		unset($segments[2]);

		$out = [];
		foreach($segments as $segment)
		{
			$out[] = rawurldecode($segment);
		}

		return implode(DIRECTORY_SEPARATOR, $out);
	}

	private function _files()
	{
		$path = CI_UPLOAD_PATH . 'elfinder' . DIRECTORY_SEPARATOR;
		$cache = $path . '.cache';

		if(!file_exists($cache))
		{
			$directory = new \RecursiveDirectoryIterator(
				$path,
				RecursiveDirectoryIterator::SKIP_DOTS
			);

			$filter = new \RecursiveCallbackFilterIterator($directory, function ($current, $key, $iterator)
			{
				if($current->isFile() && strpos($current->getFilename(), '.') === 0)
					return FALSE;

				return TRUE;
			});

			$iterator = new \RecursiveIteratorIterator($filter);

			$files = [];
			foreach ($iterator as $i) {
				$files[] = str_replace($path, '', $i->getPathname());
			}

			file_put_contents($cache, json_encode($files));
		}
		else
		{
			if(($files = file_get_contents($cache)) === FALSE)
				return [];

			$files = json_decode($files);

			if(json_last_error() !== JSON_ERROR_NONE)
				return [];
		}

		return $files;
	}

	private function _is_image($file_type)
	{
		$img_mimes = ['image/gif', 'image/jpeg', 'image/png'];

		return in_array($file_type, $img_mimes, TRUE);
	}
}