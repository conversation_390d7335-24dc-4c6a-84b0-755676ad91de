<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class User extends User_Controller
{
	public function __construct()
	{
		parent::__construct();
		$this->data['body_class'] .= 'skin-white fixed';
		$this->load->model('user_model');
		$this->data['sidebar']['active'] = FALSE;
	}

	public function documents()
	{
		$this->data['sidebar']['active'] = TRUE;
		$this->data['sidebar']['module'] = 'document';
		$this->load->model('document_model');
		$this->load->helper('form');
		$this->data['documents_category'] = $this->document_model->get_document_category();
		$this->data['documents'] = $this->document_model->get_all_by_owner();
		$this->load->view('general/user/documents', $this->data);
	}

	public function _validate_code() {
		$CI =& get_instance();
		if (!$this->validate_otp_secret($this->input->post('verification_code'), $this->input->post('secret'))) {
			$CI->form_validation->set_message('validate_auth', lang('error_otp'));
			return FALSE;
		}
		return TRUE;
	}

	public function enable_2fa() {
		$this->data['user'] = $this->user_model->get($this->auth_user_id);
		if( empty($this->data['user']) ) { show_404(); }

		if (!empty($this->data['user']->totp_secret)) {
			redirect('user/profile');
		}

		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$config = array(
			array(
				'field' => 'verification_code',
				'label' => lang('verification_code'),
				'rules' => array(
					'required',
					'min_length[6]',
					array('validate_auth', array( $this, '_validate_code' ) )
				),
			),
		);

		$this->form_validation->set_rules($config);
		if( $this->form_validation->run() === TRUE ) 
		{
			$this->user_model->update_2fa( $this->data['user'], $this->input->post('secret'));
			$this->session->unset_userdata('totp_required');
			redirect('user/profile');
			exit;
		}
		elseif ($this->input->post('secret')) {
			$this->data['secret'] = $this->input->post('secret');
			$otp = $this->generate_otp_from_secret($this->data['user']->name, $this->data['secret']);
		} else {
			$otp = $this->generate_otp($this->data['user']->name);
			$this->data['secret'] = $otp->getSecret();
		}

		$this->data['qr_code'] = $otp->getQrCodeUri(
			'https://api.qrserver.com/v1/create-qr-code/?data=[DATA]&size=300x300&ecc=M',
			'[DATA]'
		);
		$this->load->view('general/user/enable_2fa',$this->data);
	}

    public function profile( )
	{
		$this->data['user'] = $this->user_model->get($this->auth_user_id);
		if( empty($this->data['user']) ) { show_404(); }

		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$validation_rules = array_merge($this->_get_rules(), $this->_get_rules_profile());

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			if ( $this->user_model->update( $this->data['user'] ) === TRUE )
			{

			}

			redirect(); exit;
		}
		else
		{
			$this->load->view('general/user/profile',$this->data);
		}
	}

	public function staff()
	{
		$this->load->view('general/user/staff',$this->data);
	}

    public function upgrade()
	{
		$this->data['body_class'] = 'skin-blue layout-top-nav fixed';
		$this->data['user'] = $this->user_model->get($this->auth_user_id);
		if( empty($this->data['user']) ) { show_404(); }

		$this->load->model('companies/company_model');
		$this->data['company'] = $this->company_model->get($this->auth_company_id);
		if( empty($this->data['company']) ) { show_404(); }

		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$validation_rules = $this->_get_rules_upgrade();

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			if ( $this->user_model->update( $this->data['user'] ) === TRUE )
			{

			}

			if ( $this->company_model->update( $this->data['company'] ) === TRUE )
			{

			}

			redirect(); exit;
		}
		else
		{
			$this->load->view('general/user/upgrade',$this->data);
		}
	}

	public function _is_unique_update()
	{
		return $this->user_model->is_unique_update( $this->data['user'] );
	}

	public function _change_email()
	{
		$password = $this->input->post('users_password_current');
		$email    = $this->input->post('users_email');

		if( $this->data['user']->email !== $email && empty($password) )
		{
			$this->form_validation->set_message('change_email', lang('error_password_current'));
			return FALSE;
		}

		return TRUE;
	}

	public function _current_password()
	{
		$password = $this->input->post('users_password_current');

		if( ! empty($password) )
		{
			// @STEP2: Ban user if FALSE too many times
			if( $this->check_password($this->data['user']->passwd, $password) === FALSE)
			{
				$this->form_validation->set_message('current_password', lang('error_password_no_match'));
				return FALSE;
			}
		}

		return TRUE;
	}

	public function _change_password()
	{
		$password         = $this->input->post('users_password_current');
		$password_new     = $this->input->post('users_password_new');
		$password_confirm = $this->input->post('users_password_confirm');

		if( ! empty($password_new) && ! empty($password_confirm) && $password_new !== $password_confirm )
		{
			$this->form_validation->set_message('change_password', lang('error_password_matches'));
			return FALSE;
		}

		if( empty($password) && (! empty($password_new) OR ! empty($password_confirm)) )
		{
			$this->form_validation->set_message('change_password', lang('error_password_current'));
			return FALSE;
		}

		return TRUE;
	}

	public function _change_password_upgrade()
	{
		$password_new     = $this->input->post('users_password_new');
		$password_confirm = $this->input->post('users_password_confirm');

		if( ! empty($password_new) && ! empty($password_confirm) && $password_new !== $password_confirm )
		{
			$this->form_validation->set_message('change_password', lang('error_password_matches'));
			return FALSE;
		}

		if( empty($password_new) OR empty($password_confirm) )
		{
			$this->form_validation->set_message('change_password', lang('error_password_required'));
			return FALSE;
		}

		return TRUE;
	}

	private function _get_rules()
	{
		return array(
			array(
				'field' => 'users_name',
				'label' => lang('users_name'),
				'rules' => array(
					'trim',
					'required',
					'min_length[3]',
					'max_length[100]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']'
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'users_position',
				'label' => lang('users_position'),
				'rules' => array(
					'trim',
					'max_length[100]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']'
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'users_HSAID',
				'label' => lang('users_HSAID'),
				'rules' => array(
					'trim',
					'max_length[100]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']'
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'users_phone_private',
				'label' => lang('users_phone_private'),
				'rules' => array(
					'trim',
					'max_length[100]',
					'regex_match['.$this->config->item('error_phone_number').']'
				),
				'errors' => array(
					'regex_match' => lang('error_phone_number')
				)
			),
			array(
				'field' => 'users_mobile_private',
				'label' => lang('users_mobile_private'),
				'rules' => array(
					'trim',
					'max_length[100]',
					'regex_match['.$this->config->item('error_phone_number').']'
				),
				'errors' => array(
					'regex_match' => lang('error_phone_number')
				)
			),
			array(
				'field' => 'users_address',
				'label' => lang('users_address'),
				'rules' => array(
					'trim',
					'max_length[100]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']'
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'users_zip',
				'label' => lang('users_zip'),
				'rules' => array(
					'trim',
					'max_length[10]',
					'regex_match['.$this->config->item('error_zip').']'
				),
				'errors' => array(
					'regex_match' => lang('error_zip')
				)
			),
			array(
				'field' => 'users_city',
				'label' => lang('users_city'),
				'rules' => array(
					'trim',
					'max_length[100]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']'
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'users_phone_private',
				'label' => lang('users_phone_private'),
				'rules' => array(
					'trim',
					'max_length[100]',
					'regex_match['.$this->config->item('error_phone_number').']'
				),
				'errors' => array(
					'regex_match' => lang('error_phone_number')
				)
			),
			array(
				'field' => 'users_mobile',
				'label' => lang('users_mobile_private'),
				'rules' => array(
					'trim',
					'max_length[100]',
					'regex_match['.$this->config->item('error_phone_number').']'
				),
				'errors' => array(
					'regex_match' => lang('error_phone_number')
				)
			),
			array(
				'field' => 'users_email_private',
				'label' => lang('users_email_private'),
				'rules' => array(
					'trim',
					'max_length[80]',
					'valid_email'
				)
			),
			array(
				'field' => 'users_password_current',
				'label' => lang('users_password_current'),
				'rules' => array(
					'trim',
					array('current_password', array( $this, '_current_password' ) )
				)
			),
			array(
				'field' => 'users_password_new',
				'label' => lang('users_password_new'),
				'rules' => array(
					'trim',
					'min_length[8]',
				)
			),
			array(
				'field' => 'users_password_confirm',
				'label' => lang('users_password_confirm'),
				'rules' => array(
					'trim',
					array('change_password', array( $this, '_change_password' ) )
				)
			),
		);
	}

	private function _get_rules_profile()
	{
		return array(
			array(
				'field' => 'users_email',
				'label' => lang('users_email'),
				'rules' => array(
					'trim',
					'required',
					'max_length[100]',
					'valid_email',
					array('is_unique', array( $this, '_is_unique_update' ) ),
					array('change_email', array( $this, '_change_email' ) ),
				)
			)
		);
	}

	private function _get_rules_upgrade()
	{
		return array(
			array(
				'field' => 'company_name',
				'label' => lang('company_name'),
				'rules' => array(
					'trim',
					'required',
					'min_length[3]',
					'max_length[80]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']',
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'company_alias',
				'label' => lang('company_alias'),
				'rules' => array(
					'trim',
					'max_length[80]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']'
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'company_address',
				'label' => lang('company_address'),
				'rules' => array(
					'trim',
					'required',
					'max_length[80]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']'
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'company_address2',
				'label' => lang('company_address2'),
				'rules' => array(
					'trim',
					'max_length[80]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']'
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'company_zip',
				'label' => lang('company_zip'),
				'rules' => array(
					'trim',
					'required',
					'max_length[10]',
					'regex_match['.$this->config->item('error_zip').']'
				),
				'errors' => array(
					'regex_match' => lang('error_zip')
				)
			),
			array(
				'field' => 'company_city',
				'label' => lang('company_city'),
				'rules' => array(
					'trim',
					'required',
					'max_length[50]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']'
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'company_phone',
				'label' => lang('company_phone'),
				'rules' => array(
					'trim',
					'max_length[80]',
					'regex_match['.$this->config->item('error_phone_number').']'
				),
				'errors' => array(
					'regex_match' => lang('error_phone_number')
				)
			),
			array(
				'field' => 'company_fax',
				'label' => lang('company_fax'),
				'rules' => array(
					'trim',
					'max_length[80]',
					'regex_match['.$this->config->item('error_phone_number').']'
				),
				'errors' => array(
					'regex_match' => lang('error_phone_number')
				)
			),
			array(
				'field' => 'company_email',
				'label' => lang('company_email'),
				'rules' => array(
					'trim',
					'max_length[80]',
					'valid_email'
				)
			),
			array(
				'field' => 'users_name',
				'label' => lang('users_name'),
				'rules' => array(
					'trim',
					'required',
					'min_length[3]',
					'max_length[100]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']'
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'users_email',
				'label' => lang('users_email'),
				'rules' => array(
					'trim',
					'required',
					'max_length[100]',
					'valid_email',
					array('is_unique', array( $this, '_is_unique_update' ) ),
				)
			),
			array(
				'field' => 'users_password_new',
				'label' => lang('users_password_new'),
				'rules' => array(
					'trim',
					'min_length[8]',
				)
			),
			array(
				'field' => 'users_password_confirm',
				'label' => lang('users_password_confirm'),
				'rules' => array(
					'trim',
					array('change_password', array( $this, '_change_password_upgrade' ) )
				)
			),
		);
	}

	/**
	 * Verify users password against the one stored in the database.
	 * @param   string  The hashed password
	 * @param   string  The raw (supplied) password
	 * @return  bool
	 */
	public function check_password( $hash, $password ) {
		return password_verify(
			base64_encode(
				hash('sha384', $password, true)
			),
			$hash
		);
	}
}
