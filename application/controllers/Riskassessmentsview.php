<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Riskassessmentsview extends MY_Controller
{

  public function __construct()
	{
		parent::__construct();
		$this->load->model(array('deviation_model'));
    $this->load->model(array('group_model'));
		$this->load->model(array('user_model'));
    $db = [
			'hostname' => $this->db->hostname,
			'database' => $this->db->database,
			'username' => $this->db->username,
			'password' => $this->db->password,
		];
		$this->load->library('riskassessmentslib',$db);
    $this->load->helper(array('form','forms'));
		$this->load->helper('old_form');
    $this->auth_company_id  = $this->deviation_model->get_company_id();
		$this->users = $this->user_model->get_all();
		$CI =& get_instance();
		$CI->users_all = $this->users;
  }
  public function view( $ra_id )
	{
		$this->VALID_UUIDv4($ra_id);
		
		$getRisk = $this->riskassessmentslib->getRisk($ra_id);
		if( empty($getRisk) OR $getRisk->company_id !== $this->auth_company_id )
			show_404();
		$getRiskDepartment = $riskDepartmentRights = $this->riskassessmentslib->getRiskDepartment($ra_id);
		$getRisks = $this->riskassessmentslib->getRisks($ra_id);
		
		$getRiskAssessmentsDeviationMap     = $this->riskassessmentslib->getRiskAssessmentsDeviationMap($ra_id);
		$getRiskAssessmentsEventAnalysisMap = $this->riskassessmentslib->getRiskAssessmentsEventAnalysisMap($ra_id);
		$getDeviationNames                  = $this->riskassessmentslib->getDeviationNames($riskDepartmentRights,$getRiskAssessmentsDeviationMap,$this->auth_company_id);
		$getEventAnalysisNames              = $this->riskassessmentslib->getEventAnalysisNames($this->auth_company_id);
		
		$this->data['getRisk']                            = $getRisk;
		$this->data['getRisks']                           = $getRisks;
		$this->data['getRiskAssessmentsDeviationMap']     = $getRiskAssessmentsDeviationMap;
		$this->data['getDeviationNames']                  = $getDeviationNames;
		$this->data['getRiskAssessmentsEventAnalysisMap'] = $getRiskAssessmentsEventAnalysisMap;
		$this->data['getEventAnalysisNames']              = $getEventAnalysisNames;
		
		$this->data['fields'] = array(
			array(
				'id' => 'name',
				'input' => 'input',
				'required' => 1,
				'title' => 'Rubrik',
				'description' => 'Ange kortfattat vad som rapporteras.',
				'value' => $getRisk->name,
				'values' => NULL,
			),
			array(
				'id' => 'description',
				'input' => 'text',
				'required' => 1,
				'title' => 'Ärendebeskrivning',
				'description' => 'Beskriv riskbedömningen.',
				'value' => $getRisk->description,
				'values' => NULL,
			),
			array(
				'id' => 'date',
				'input' => 'date',
				'required' => 0,
				'title' => 'Datum',
				'description' => 'När skapades riskbedömningen?',
				'value' => $getRisk->date?$getRisk->date:date('Y-m-d'),
				'values' => NULL,
			),
		);
		
		$this->data['groups']      = $this->group_model->get_all();
		$this->data['fields'] = array_merge($this->data['fields'], array(
			array(
				'id' => 'us_id',
				'input' => 'users',
				'required' => 0,
				'title' => 'Användare',
				'description' => 'Vem är ansvarig för riskbedömningen?',
				'value' => $getRisk->us_id,
				'values' => $this->users
			),
			array(
				'id' => 'department',
				'input' => 'department',
				'required' => 0,
				'title' => 'Enhet',
				'description' => 'Ange de(n) enhet(er) som riskbedömningen berör.',
				'value' => $getRiskDepartment,
				'values' => $this->data['groups']['department']
			)
		));

		$this->data['riskOccurrenceValues'] = array(
			0 => '-- Frekvens --',
			1 => 'Låg (Har inte hänt i Sverige)',
			2 => 'Måttlig (Har hänt någon gång i Sverige)',
			3 => 'Hög (Händer någon gång per år I verksamheten)',
			4 => 'Mycket hög (Händer flera gånger om året i verksamheten)'
		);
		$this->data['riskSeverityValues'] = array(
			0 => '-- Allvarlighetsgrad --',
			1 => 'Lindrig (övergående obehag)',
			2 => 'Kännbar (kortare sjukfrånvaro, måttliga besvär)',
			3 => 'Allvarlig (längre sjukskrivning, allvarliga besvär)',
			4 => 'Mycket allvarlig (livslång påverkan, dödsfall)'
		);
		
		$this->data['valueToText'] = array(
			0 => array('',''),
			1 => array('',''),
			2 => array('Låg','riskok'),
			3 => array('Låg','riskok'),
			4 => array('Måttlig','riskwarning'),
			5 => array('Måttlig','riskwarning'),
			6 => array('Allvarligt','riskdanger'),
			7 => array('Mycket allvarligt','riskmeltdown'),
			8 => array('Mycket allvarligt','riskmeltdown')
		);
		
		$this->load->view('general/riskassessments/viewpublic',$this->data);
	}
}
