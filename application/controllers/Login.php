<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Login extends MY_Controller
{
	
	public function __construct()
	{
		parent::__construct();
		$this->data['body_class'] .= 'login-page';
	}

	public function index()
	{
		if ( $this->is_logged_in() === TRUE )
		{
			redirect(); exit;
		}

		if( $this->config->item('ldap') === TRUE ) 
		{
			$username = NULL;

			if ( ! empty($_SERVER['REMOTE_USER']) )
			{
				$username = $_SERVER['REMOTE_USER'];
			}
			elseif ( ! empty($_SERVER['LOGON_USER']) )
			{
				$username = $_SERVER['LOGON_USER'];
			}
			elseif ( ! empty($_SERVER['AUTH_USER']) )
			{
				$username = $_SERVER['AUTH_USER'];
			}

			if( $this->authentication->ldap($username) === FALSE )
			{
				$this->data['username'] = $username;
				$this->load->view('login/ldap_error',$this->data);
			}
		}
		else
		{
			$this->setup_login_form(LOGIN_PAGE);
			$CI =& get_instance();
			$CI->session->set_userdata('totp_required', true);
			if( $this->authentication->login() === FALSE )
			{
				$this->load->view('login/login',$this->data);
			}			
		}
	}
	
	public function logout()
	{
		$this->authentication->logout();

		// Set redirect protocol
		$redirect_protocol = USE_SSL ? 'https' : NULL;

		redirect( site_url( LOGIN_PAGE . '?' . AUTH_LOGOUT_PARAM . '=1', $redirect_protocol ) ); exit;
	}
	
	public function remote()
	{
		// header('WWW-Authenticate: Basic', TRUE);
		// var_dump($_SERVER['AUTH_USER']);
		// var_dump($_SERVER['LOGON_USER']);
		// var_dump($_SERVER['REMOTE_USER']);
		exit;
	}
	
	public function flex()
	{
		// if ( $this->is_logged_in() === TRUE )
		// {
			// redirect();
		// }
		// if( $this->authentication->ldap() === TRUE )
		// {
			// redirect( $this->agent->referrer() );
		// }
		
		$this->setup_login_form(LOGIN_PAGE);
		
		if( $this->authentication->login_flex() === FALSE )
		{
			$this->load->view('login/login_flex',$this->data);
		}
	}

	public function _validate_code() {
		$CI =& get_instance();
		if (!$this->validate_otp($this->input->post('verification_code'))) {
			$CI->form_validation->set_message('validate_auth', lang('error_otp'));
			return FALSE;
		}
		return TRUE;
	}

	public function verify() {
		$this->load->model('user_model');
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		
		$config = array(
			array(
				'field' => 'verification_code',
				'label' => lang('verification_code'),
				'rules' => array(
					'required',
					'min_length[6]',
					array('validate_auth', array( $this, '_validate_code' ) )
				),
			),
		);
		
		$this->setup_login_form('verify');
		$this->form_validation->set_rules($config);
		if( $this->form_validation->run() === TRUE )
		{
			$CI =& get_instance();
			$CI->session->unset_userdata('totp_required');
			// Redirect to specified page, or home page if none provided
			$redirect = $this->input->get(AUTH_REDIRECT_PARAM)
			? urldecode( $this->input->get(AUTH_REDIRECT_PARAM) )
			: '';

			// Set the redirect protocol
			$redirect_protocol = USE_SSL ? 'https' : NULL;

			// Load URL helper for the site_url function
			$this->load->helper('url');

			$url = site_url( $redirect, $redirect_protocol );

			header( "Location: " . $url, TRUE, 302 );
		}
		else
		{
			$this->load->view('login/verify',$this->data);
		}
	}
	
	public function recover()
	{
		$this->load->model('user_model');
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		
		$config = array(
			array(
				'field' => 'login_email',
				'label' => lang('login_email'),
				'rules' => array(
					'trim',
					'required',
					'valid_email',
					'min_length[3]'
				),
			),
		);
		
		$this->form_validation->set_rules($config);
		if( $this->form_validation->run() === TRUE )
		{
			if( $user_data = $this->user_model->get_recovery_data( $this->input->post('login_email') ) )
			{
				// Check if user is banned
				if( $user_data->banned == '1' )
				{
					// Log an error if banned
				}
				else
				{
					// Generate recovery code
					$recovery_code = bin2hex(random_bytes(36));
					
					// Update user record with recovery code and time
					$this->user_model->update_user_raw_data(
						$user_data->user_id,
						[
							'passwd_recovery_code' => $this->authentication->hash_password($recovery_code),
							'passwd_recovery_date' => date('Y-m-d H:i:s')
						]
					);
					
					$link_uri = 'login/recovery_verification/' . $user_data->user_id . '/' . $recovery_code;
					
					// Send email
					$this->data['user_data'] = $user_data;
					$this->data['recover_url'] = site_url($link_uri);
					
					$this->load->library('PHPMailerLib');
					$mail = $this->phpmailerlib->load();
					$mail->Subject = 'Återställning av lösenord';
					$mail->Body = $this->load->view('login/recover_email',$this->data,TRUE);
					$mail->addAddress($user_data->email, $user_data->name);
					// $mail->SMTPDebug = 3;
					if ( ! $mail->send() )
					{
						$this->load->view('login/recover_error',$this->data);
					}
					else
					{
						$this->load->view('login/recover_success',$this->data);
					}
				}
			}
			else
			{
				$this->load->view('login/recover_success',$this->data);
			}
		}
		else
		{
			$this->load->view('login/recover',$this->data);
		}
	}
	
    public function upgrade()
	{
		if( $this->is_logged_in() )
		{
		}
		
		$this->data['body_class'] = 'skin-blue layout-top-nav fixed';
		$this->load->model('user_model');
		$this->data['user'] = $this->user_model->get($this->auth_user_id);
		if( empty($this->data['user']) ) { show_404(); }
		
		$this->load->model('companies/company_model');
		$this->data['company'] = $this->company_model->get($this->auth_company_id);
		if( empty($this->data['company']) ) { show_404(); }
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$validation_rules = $this->_get_rules_upgrade();
		
		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			if ( $this->user_model->update( $this->data['user'] ) === TRUE )
			{
				
			}
			
			if ( $this->company_model->update( $this->data['company'] ) === TRUE )
			{
				
			}
			
			redirect(); exit;
		}
		else
		{
			$this->load->view('general/user/upgrade',$this->data);
		}
	}
	
	public function recovery_verification( $user_id = NULL, $recovery_code = NULL )
	{
		$this->VALID_UUIDv4($user_id);
		$this->load->model('user_model');
		
		if(
			/**
			 * Make sure that $recovery code is exactly 72 characters long
			 */
			strlen( $recovery_code ) == 72 &&
			
			/**
			 * Try to get a hashed password recovery 
			 * code and user salt for the user.
			 */
			$recovery_data = $this->user_model->get_recovery_verification_data( $user_id )
		)
		{
			/**
			 * Check that the recovery code from the 
			 * email matches the hashed recovery code.
			 */
			if( $recovery_data->passwd_recovery_code == $this->authentication->check_password( $recovery_data->passwd_recovery_code, $recovery_code ) )
			{
				$this->load->helper('form');
				$this->load->library('form_validation');
				$this->config->load('form_validation');
				
				$config = array(
					array(
						'field' => 'users_password_new',
						'label' => lang('users_password_new'),
						'rules' => array(
							'trim',
							'min_length[8]',
						)
					),
					array(
						'field' => 'users_password_confirm',
						'label' => lang('users_password_confirm'),
						'rules' => array(
							'trim',
							array('change_password', array( $this, '_change_password' ) )
						)
					),
				);
				
				$this->form_validation->set_rules($config);
				if( $this->form_validation->run() === TRUE )
				{
					// Save
					$this->user_model->update_user_raw_data($user_id, [
						'passwd' => $this->authentication->hash_password( $this->input->post('users_password_new') ),
						'passwd_recovery_code' => NULL,
						'passwd_recovery_date' => NULL
					]);
					// TODO: Flash message
					$_SESSION['flashdata'] = lang('login_recover_password_flashdata');
					// $this->session->mark_as_flash('flashdata');
					
					redirect(); exit;
				}
				else
				{
					$this->data['user_data']['name']  = $recovery_data->name;
					$this->data['user_data']['email'] = $recovery_data->email;
					// Load view
					$this->load->view('login/recovery_verification',$this->data);
				}
			}

			// Link is bad so show message
			else
			{
				show_404();
			}
		}
		else
		{
			// @TODO: Expired
			show_404();
		}
	}
	
	public function _change_password()
	{
		$password_new     = $this->input->post('users_password_new');
		$password_confirm = $this->input->post('users_password_confirm');
		
		if( ! empty($password_new) && ! empty($password_confirm) && $password_new !== $password_confirm )
		{
			$this->form_validation->set_message('change_password', lang('error_password_matches'));				
			return FALSE;
		}
		
		if( empty($password_new) OR empty($password_confirm) )
		{
			$this->form_validation->set_message('change_password', lang('error_password_required'));				
			return FALSE;
		}
		
		return TRUE;
	}
	
	private function _get_rules_upgrade()
	{
		return array(
			array(
				'field' => 'company_name',
				'label' => lang('company_name'),
				'rules' => array(
					'trim',
					'required',
					'min_length[3]',
					'max_length[80]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']',
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'company_alias',
				'label' => lang('company_alias'),
				'rules' => array(
					'trim',
					'max_length[80]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']'
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'company_address',
				'label' => lang('company_address'),
				'rules' => array(
					'trim',
					'required',
					'max_length[80]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']'
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'company_address2',
				'label' => lang('company_address2'),
				'rules' => array(
					'trim',
					'max_length[80]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']'
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'company_zip',
				'label' => lang('company_zip'),
				'rules' => array(
					'trim',
					'required',
					'max_length[10]',
					'regex_match['.$this->config->item('error_zip').']'
				),
				'errors' => array(
					'regex_match' => lang('error_zip')
				)
			),
			array(
				'field' => 'company_city',
				'label' => lang('company_city'),
				'rules' => array(
					'trim',
					'required',
					'max_length[50]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']'
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'company_phone',
				'label' => lang('company_phone'),
				'rules' => array(
					'trim',
					'max_length[80]',
					'regex_match['.$this->config->item('error_phone_number').']'
				),
				'errors' => array(
					'regex_match' => lang('error_phone_number')
				)
			),
			array(
				'field' => 'company_fax',
				'label' => lang('company_fax'),
				'rules' => array(
					'trim',
					'max_length[80]',
					'regex_match['.$this->config->item('error_phone_number').']'
				),
				'errors' => array(
					'regex_match' => lang('error_phone_number')
				)
			),
			array(
				'field' => 'company_email',
				'label' => lang('company_email'),
				'rules' => array(
					'trim',
					'max_length[80]',
					'valid_email'
				)
			),
			array(
				'field' => 'users_name',
				'label' => lang('users_name'),
				'rules' => array(
					'trim',
					'required',
					'min_length[3]',
					'max_length[100]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']'
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'users_email',
				'label' => lang('users_email'),
				'rules' => array(
					'trim',
					'required',
					'max_length[100]',
					'valid_email',
					array('is_unique', array( $this, '_is_unique_update' ) ),
				)
			),
			array(
				'field' => 'users_password_new',
				'label' => lang('users_password_new'),
				'rules' => array(
					'trim',
					'min_length[8]',
				)
			),
			array(
				'field' => 'users_password_confirm',
				'label' => lang('users_password_confirm'),
				'rules' => array(
					'trim',
					array('change_password', array( $this, '_change_password_upgrade' ) )
				)
			),
		);
	}
	
	public function _change_password_upgrade()
	{
		$password_new     = $this->input->post('users_password_new');
		$password_confirm = $this->input->post('users_password_confirm');
		
		if( ! empty($password_new) && ! empty($password_confirm) && $password_new !== $password_confirm )
		{
			$this->form_validation->set_message('change_password', lang('error_password_matches'));				
			return FALSE;
		}
		
		if( empty($password_new) OR empty($password_confirm) )
		{
			$this->form_validation->set_message('change_password', lang('error_password_required'));				
			return FALSE;
		}
		
		return TRUE;
	}
	
	public function _is_unique_update()
	{
		return $this->user_model->is_unique_update( $this->data['user'] );
	}
}
