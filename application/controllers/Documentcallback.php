<?php
defined('BASEPATH') OR exit('No direct script access allowed');

require_once( dirname(__FILE__) . '/../libraries/onlyoffice/config.php' );
require_once( dirname(__FILE__) . '/../libraries/onlyoffice/ajax.php' );
require_once( dirname(__FILE__) . '/../libraries/onlyoffice/common.php' );
require_once( dirname(__FILE__) . '/../libraries/onlyoffice/functions.php' );
require_once( dirname(__FILE__) . '/../libraries/onlyoffice/jwtmanager.php' );
require_once( dirname(__FILE__) . '/../libraries/onlyoffice/trackmanager.php' );
require_once( dirname(__FILE__) . '/../libraries/onlyoffice/functions/callback.php' );

class Documentcallback extends MY_Controller
{
	function __construct()
	{
		parent::__construct();
		$this->load->model('document_model');
		$this->load->model('user_model');
	}

	// @TODO: Check read permissions
	public function index( $company_id = NULL, $attachment_file = NULL, $doctype = 'documents' )
	{
		// @ONLYOFFICE
		$attachment_id = pathinfo($attachment_file, PATHINFO_FILENAME);
		$this->VALID_UUIDv4($attachment_id);

		$upload_base_path = CI_UPLOAD_PATH . $doctype;
		$upload_path = $upload_base_path . DIRECTORY_SEPARATOR . $company_id ;
		$filename = $upload_path . DIRECTORY_SEPARATOR . $attachment_file;

		if(isset($_GET["type"]) && !empty($_GET["type"]))
		{
			log_message('debug', 'callback : ' . $company_id . DIRECTORY_SEPARATOR . $attachment_file . ' - ' . $_GET["type"]);

			@header( 'Content-Type: application/json; charset==utf-8');
			@header( 'X-Robots-Tag: noindex' );
			@header( 'X-Content-Type-Options: nosniff' );
			nocache_headers();

			sendlog(print_r($_GET, true), "webedior-ajax.log");

			$type = $_GET["type"];

			switch($type) { //Switch case for value of type
				case "upload":
					die();
				case "download":
					$response_array = download($filename);
					$response_array['status'] = 'success';
					die (json_encode($response_array));
				case "convert":
					$response_array = convert($filename);
					$response_array['status'] = 'success';
					die (json_encode($response_array));
				case "track":
					$response_array = track($filename);
					$response_array['status'] = 'success';
					die (json_encode($response_array));
				case "delete":
					$response_array = delete($filename);
					$response_array['status'] = 'success';
					die (json_encode($response_array));
				case "assets":
					$response_array = assets();
					$response_array['status'] = 'success';
					die (json_encode($response_array));
				case "csv":
					$response_array = csv();
					$response_array['status'] = 'success';
					die (json_encode($response_array));
				case "files":
					die();
				default:
					$response_array['status'] = 'error';
					$response_array['error'] = '404 Method not found';
					die(json_encode($response_array));
			}
		}

	}

	public function download_path($folder_id, $file_name, $doctype = 'documents')
	{
		$upload_base_path =  CI_UPLOAD_PATH . $doctype;
		$upload_path = $upload_base_path . DIRECTORY_SEPARATOR . $folder_id;
		$upload_file = $upload_path . DIRECTORY_SEPARATOR . $file_name;
		if (isset($_GET["version"]) && !empty($_GET["version"])) {
			if (isset($_GET["diff"]) && $_GET["diff"] == 'true') {
				$upload_file = $upload_file . '-hist' . DIRECTORY_SEPARATOR . $_GET["version"] . DIRECTORY_SEPARATOR . "diff.zip";
			} else {
				$filetype = strtolower(pathinfo($upload_file, PATHINFO_EXTENSION));
				$upload_file = $upload_file . '-hist' . DIRECTORY_SEPARATOR . $_GET["version"] . DIRECTORY_SEPARATOR . "prev." . $filetype;;
			}
		}
		if( ! file_exists($upload_file) )
			show_404();

		downloadFile($upload_file);
	}
}