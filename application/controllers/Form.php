<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Form extends Form_Controller
{
	public function id( $form_id = NULL )
	{
		$this->VALID_UUIDv4($form_id);
		$fiscal = date('Y')-1;
		
		$this->data['form'] = $this->form_model->get($form_id);
		if( empty($this->data['form']) ) { show_404(); }
		if( $this->data['form']->company_id !== $this->auth_company_id && intval($this->data['form']->global) !== 1 ) { show_404(); }
		
		$this->data['fiscal'] = FALSE;
		if( in_array($this->data['form']->type, ['qa_flex','qa_kiv']) )
		{
			if( $this->data['form']->type === 'qa_flex') {
				$form_done = $this->form_model->get_form_done_flex($this->data['form']->survey_id, $fiscal);
			}
			else
			{
				$form_done = $this->form_model->get_form_done($this->data['form']->survey_id, $fiscal);
			}
			if( isset($form_done) )
			{
				$this->data['fiscal'] = TRUE;
				$this->data['formdone'] = $form_done->done;
			}
		}
		
		$this->form = $this->form_model->get_menu_structure($form_id);
		if( in_array($this->data['form']->type, ['qa_flex','qa_kiv']))
		{
			if( isset($this->form['structure'][$this->data['form']->form_id]) )
			{
				$lastpage = array_pop($this->form['structure'][$this->data['form']->form_id]);
				$formdone = array_pop($this->form['structure'][$this->data['form']->form_id]);
				
				if( $this->data['fiscal'] )
					array_push($this->form['structure'][$this->data['form']->form_id], $lastpage);
				else
					array_push($this->form['structure'][$this->data['form']->form_id], $formdone);
			}
		}
		
		$this->data['sidebar']['menu']['header']     = $this->data['form']->name;
		$this->data['sidebar']['menu']['structure']  = $this->form['structure'];
		$this->data['sidebar']['menu']['active']     = [$this->data['form']->form_id];
		$this->data['sidebar']['menu']['controller'] = 'form/page';
		$this->data['sidebar']['active']             = TRUE;
		$this->data['sidebar']['module'] 						 = 'form';

		$this->load->view('general/form/id',$this->data);
	}
	
	// Display answers, not form.
	public function view()
	{
		
	}
	
	// @TODO: Fetch fiscal from settings somewhere
	public function page($page_id = NULL)
	{
		// var_dump($page_id);exit;
		$this->VALID_UUIDv4($page_id);
		$active = array();
		$fiscal = date('Y')-1;
		
		if( $page_id )
		{
			$this->data['formdone'] = FALSE;
			$this->data['lastpage'] = FALSE;
			$this->data['fiscal']   = FALSE;
			
			$this->data['page'] = $this->form_model->get_page($page_id);
			$this->data['page_current'] = $this->data['page'];
			if( empty($this->data['page']) ) { show_404(); }
			
			if( ! empty($this->data['page_current']->settings) )
			{
				$settings = json_decode($this->data['page_current']->settings);
				if( isset($settings->formdone) )
					$this->data['formdone'] = TRUE;
				if( isset($settings->lastpage) )
					$this->data['lastpage'] = TRUE;
			}
			
			if( isset($this->data['page']->parent_id) )
			{
				$this->data['sub_page']     = $this->data['page'];
				$this->data['page']         = $this->form_model->get_page($this->data['page']->parent_id);
				if( empty($this->data['page']) ) { show_404(); }
			}
			if( $this->data['sub_page'] ) {
				$active[] = $this->data['sub_page']->page_id;
			}
			$active[] = $this->data['page']->page_id;
			$active[] = $this->data['page']->parent_id;
			
			$this->data['form'] = $this->form_model->get($this->data['page']->form_id);
			if( empty($this->data['form']) ) { show_404(); }
			if( $this->data['form']->company_id !== $this->auth_company_id && intval($this->data['form']->global) !== 1 ) { show_404(); }
			
			if( in_array($this->data['form']->type, ['qa_flex','qa_kiv']) )
			{
				if( $this->data['form']->type === 'qa_flex') {
					$form_done = $this->form_model->get_form_done_flex($this->data['form']->survey_id, $fiscal);
				}
				else
				{
					$form_done = $this->form_model->get_form_done($this->data['form']->survey_id, $fiscal);
				}
				if( isset($form_done) )
				{
					$this->data['fiscal'] = TRUE;
					$this->data['formdone'] = $form_done->done;
				}
			}
			
			$active[] = $this->data['form']->form_id;
		}
		
		if( !isset($page_id) )
			show_404();
		
		$this->questions = $this->form_model->get_question_structure($this->data['page_current']->page_id);
		$this->form      = $this->form_model->get_menu_structure($this->data['form']->form_id);
		if( in_array($this->data['form']->type, ['qa_flex','qa_kiv']))
		{
			if( isset($this->form['structure'][$this->data['form']->form_id]) )
			{
				$lastpage = array_pop($this->form['structure'][$this->data['form']->form_id]);
				$formdone = array_pop($this->form['structure'][$this->data['form']->form_id]);
				
				if( $this->data['fiscal'] )
					array_push($this->form['structure'][$this->data['form']->form_id], $lastpage);
				else
					array_push($this->form['structure'][$this->data['form']->form_id], $formdone);
			}
		}
		
		$this->load->helper(array('form','forms'));
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		
		// @STEP2: Validation
		// $this->form_validation->set_rules( $validation_rules );
		// if( $this->form_validation->run() === TRUE )
		if( $this->input->method(TRUE) === 'POST' )
		{
			// Done
			if( $this->input->post('form_done') )
			{
				if( in_array($this->data['form']->type, ['qa_kiv']))
				{
					// Save form done, give 404 otherwise
					if( $this->form_model->save_form_done_kiv(
							$this->data['form']->survey_id,
							$this->data['form']->form_id,
							$fiscal
						) === TRUE
					)
					{
						// Receipt
						redirect('form/page/' . $lastpage->page_id); exit;
					}
				}
				else
				{
					// Save form done, give 404 otherwise
					if( $this->form_model->save_form_done(
							$this->data['form']->survey_id,
							$this->data['form']->form_id,
							$fiscal
						) === TRUE
					)
					{
						// Receipt
						redirect('form/page/' . $lastpage->page_id); exit;
					}
				}
				
				// @STEP2: Error messages
				// show_404();
				redirect('form/id/' . $this->data['form']->form_id); exit;
			}
			else
			{
				if( $this->data['form']->type === 'qa_flex') {
					// Save form, give 404 otherwise
					if( $this->form_model->save_form_flex(
							$this->data['form']->survey_id,
							$this->data['page_current']->page_id,
							$this->questions['all'],
							$this->questions['field']
						) === FALSE
					)
					{
						show_404();
					}
				}
				else
				{
					// Save form, give 404 otherwise
					if( $this->form_model->save_form(
							$this->data['form']->survey_id,
							$this->data['page_current']->page_id,
							$this->questions['all'],
							$this->questions['field']
						) === FALSE
					)
					{
						show_404();
					}
				}
				
				$navigation = forms_navigation($this->data['page_current']->page_id, $_SESSION['menu_order']);
				if( isset($navigation['previous']) && $navigation['previous'] !== FALSE && $this->input->post('form_previous') )
				{
					// TODO: Flashdata
					redirect('form/page/' . $navigation['previous']); exit;
				}
				// There are no previous pages. How could you submit it...?
				else if( $this->input->post('form_previous') )
				{
					echo 'There are no previous pages. How could you submit it...?';
				}
				
				if( isset($navigation['next']) && $navigation['next'] !== FALSE && $this->input->post('form_next') )
				{
					// TODO: Flashdata
					redirect('form/page/' . $navigation['next']); exit;
				}
				// No more pages. Done!
				else if( $this->input->post('form_next') )
				{
					// TODO: Flashdata && go to list
					redirect('form/id/' . $this->data['form']->form_id); exit;
				}
			}
		}
		else
		{
			if( $this->data['form']->type === 'qa_flex') {
				$this->answers = $this->form_model->get_answers_flex($this->data['form']->survey_id,$this->data['page_current']->page_id);
			}
			else
			{
				// exit;
				$this->answers = $this->form_model->get_answers($this->data['form']->survey_id,$this->data['page_current']->page_id);
			}
			// var_dump($this->form);exit;
			$this->data['questions'] = $this->questions['structure'];
			$this->data['options']   = $this->questions['options'];
			$this->data['selected']  = $this->answers;
			$this->data['sidebar']['menu']['header']     = $this->data['form']->name;
			$this->data['sidebar']['menu']['structure']  = $this->form['structure'];
			$this->data['sidebar']['menu']['active']     = array_filter($active);
			$this->data['sidebar']['menu']['controller'] = 'form/page';
			$this->data['sidebar']['module'] 						 = 'form';
			
			if( ! empty($this->questions['field']['upload']) )
			{
				$this->load->model(['document_model']);
				foreach($this->questions['field']['upload'] as $upload)
				{
					if( $attachments = $this->form_model->get_formUpload($this->data['form']->survey_id,$upload) )
					{
						$this->data['selected'][$upload] = $attachments;
						$this->data['options'][$upload]  = $this->document_model->get_all( $attachments );
					}
					
				}
			}
		
			$this->load->view('general/form/view',$this->data);
		}
	}
	
	public function formUploadFiles( $question_id )
	{
		$this->VALID_UUIDv4($question_id);
		$this->load->model(['document_model']);
		
		$this->data['question'] = $this->form_model->get_question( $question_id );
		
		if( empty($this->data['question']) ) { exit; }
		
		if( ! empty($this->data['question']->parent_id) )
		{
			$this->data['parent'] = $this->form_model->get_question( $this->data['question']->parent_id );
			if( empty($this->data['parent']) ) { exit; }
		}
		
		$page = $this->form_model->get_page($this->data['question']->page_id);
		if( empty($page) ) { exit; }
		
		$form = $this->form_model->get($page->form_id);
		if( empty($form) ) { exit; }
		
		$documents = $this->form_model->get_formUpload($form->survey_id, $question_id);
		if( empty($documents) ) {
			$this->output
					->set_output('')
					->set_status_header(404)
					->_display();
			exit;
		}
		
		$documents = $this->document_model->get_all( $documents );
		
		$this->output
				->set_content_type('application/json', 'utf-8')
				->set_output( json_encode($documents,
				JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES) )
				->set_status_header(200)
				->_display();
		exit;
	}
	
	public function formUpload( $question_id )
	{
		$this->VALID_UUIDv4($question_id);
		$this->load->model(['document_model','folder_model']);
		
		$this->data['question'] = $this->form_model->get_question( $question_id );
		
		if( empty($this->data['question']) ) { exit; }
		
		if( ! empty($this->data['question']->parent_id) )
		{
			$this->data['parent'] = $this->form_model->get_question( $this->data['question']->parent_id );
			if( empty($this->data['parent']) ) { exit; }
		}
		
		$page = $this->form_model->get_page($this->data['question']->page_id);
		if( empty($page) ) { exit; }
		
		$form = $this->form_model->get($page->form_id);
		if( empty($form) ) { exit; }
		
		$this->data['move']['menu'] = $this->menu_model->get_all();
		$this->data['move']['folder'] = $this->folder_model->get_all($this->data['move']['menu']['id'], TRUE);
		$this->data['move']['document'] = $this->document_model->get_all_documents_in_folder(array_keys($this->data['move']['folder']['id']));
		// @STEP2: Real error
		if( empty($this->data['move']['document']) ) { exit; }
		
		$keep = [];
		foreach($this->data['move']['folder']['id'] AS $folder_id => $menu_id)
		{
			if(
				in_array($folder_id, $this->data['move']['document']['folders']) && 
				isset($this->data['move']['menu']['parent'][$menu_id]) )
			{
				$parent_id = $this->data['move']['menu']['parent'][$menu_id];
				if( isset($this->data['move']['menu'][$parent_id]) )
				{
					$keep[] = $menu_id;
					$keep[] = $parent_id;
				}
				
				if(isset($this->data['move']['menu']['parent'][$parent_id]))
				{
					$top_menu = $this->data['move']['menu']['parent'][$parent_id];
					if( isset($this->data['move']['menu'][$top_menu]) )
					{
						$keep[] = $top_menu;
					}
				}
			}
		}
		
		$this->data['move']['menu']['loop'] = $keep;
		
		// Load selected
		$this->data['document']['selected'] = $this->form_model->get_formUpload($form->survey_id, $question_id);
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$validation_rules = $this->_get_rules_formUpload();

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			if( $this->form_model->formUpload($form->survey_id, $question_id) )
			{
				
			}
			
			$this->load->view('general/form/formUpload_saved',$this->data);
		}
		else
		{
			$this->load->view('general/form/formUpload',$this->data);
		}
	}
	
	private function _get_rules_formUpload()
	{
		return array(
			array(
				'field' => 'tree_documents[]',
				'label' => lang('documents_document'),
				'rules' => array(
					'trim',
					'in_list['.implode(',', $this->data['move']['document']['version']).']'
				)
			),
		);
	}

	// Display all available checklists
	public function checklists()
	{
		$this->data['forms'] = $this->form_model->get_all(['checklist']);
		if( ! empty($this->data['forms']) ) {
			$this->data['pages'] = $this->form_model->get_form_pages_by_form(array_keys($this->data['forms']));
		}

		$this->data['sidebar']['active'] = TRUE;
		$this->data['sidebar']['module'] = 'checklist';
		// $this->data['body_class'] .= " sidebar-collapse";
		$this->load->view('general/form/checklist_display',$this->data);
	}
	
	public function checklist( $type = NULL, $page_id = NULL, $checklist_id = NULL, $date = NULL )
	{
		$func = 'checklist_' . $type;
		if( ! method_exists($this,$func) ) { show_404(); }

		$this->VALID_UUIDv4($page_id);
		if( $date !== NULL && ! $this->is_date($date) ) { show_404(); }
		// $this->VALID_UUIDv4($checklist_id,FALSE);

		$this->data['page'] = $this->form_model->get_page($page_id);
		if( empty($this->data['page']) ) { show_404(); }

		$this->data['form'] = $this->form_model->get($this->data['page']->form_id);
		if( empty($this->data['form']) ) { show_404(); }
		if( $this->data['form']->company_id !== $this->auth_company_id && intval($this->data['form']->global) !== 1 ) { show_404(); }

		$this->data['sidebar']['active'] = TRUE;
		$this->data['sidebar']['module'] = 'checklist';
		// $this->data['body_class'] .= " sidebar-collapse";

		$this->{$func}( $page_id, $checklist_id, $date );
	}
	// @TODO: Fetch only your own checklists
	public function checklist_list($page_id = NULL, $checklist_id = NULL, $date = NULL)
	{
		$this->VALID_UUIDv4($checklist_id,FALSE);

		$this->data['surveys'] = $this->form_model->get_all_surveys($page_id);
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		
		$checklist_users = [NULL => '- ' . lang('users_users') . ' -'];
		foreach($this->users as $user_id => $user)
		{
			$checklist_users[$user_id] = $this->users[$user_id]->name;
		}
		$this->data['checklist_users'] = $checklist_users;
		
		$this->load->view('general/form/checklist_list',$this->data);
	}
	// @TODO: Add custom comment that can be used a summury
	public function checklist_create($page_id = NULL, $checklist_id = NULL, $date = NULL)
	{
		if( $checklist_id !== NULL && $checklist_id !== '00000000-0000-0000-0000-000000000000') { show_404(); }
		
		$this->questions = $this->form_model->get_question_structure($this->data['page']->page_id);

		$this->load->helper(array('form','forms'));
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		
		// @STEP2: Validation
		// $this->form_validation->set_rules( $validation_rules );
		// if( $this->form_validation->run() === TRUE )
		if( $this->input->method(TRUE) === 'POST' )
		{
			$new_checklist_id = UUIDv4();
			// Save form, give 404 otherwise
			if( $this->form_model->save_form(
					$this->data['form']->survey_id,
					$new_checklist_id,
					$this->questions['all'],
					$this->questions['field']
				) === FALSE
			)
			{
				show_404();
			}
			
			if( $this->form_model->save_surveys($new_checklist_id, $page_id, $date) )
			{
				
			}
			if( $date !== NULL)
			{
				$this->load->model('user_messages_model');
				$this->user_messages_model->remove_comment( 'checklist', $this->auth_user_id, $page_id, $date);				
			}

			redirect('form/checklist/list/' . $page_id); exit;
		}
		else
		{
			$this->data['questions'] = $this->questions['structure'];
			$this->data['options']   = $this->questions['options'];
			$this->data['date']      = $date;
			$this->data['selected']  = [];
			
			if( ! empty($this->questions['field']['users']) )
			{
				foreach($this->questions['field']['users'] as $users)
				{
					foreach($this->users as $user)
					{
						$dropdown = new stdClass();
						$dropdown->option_id = $user->user_id;
						$dropdown->name      = $user->name;
						$this->data['options'][$users][$user->user_id] = $dropdown;
					}
				}
			}
		
			$this->load->view('general/form/checklist_create',$this->data);
		}
	}
	
	public function checklist_view($page_id = NULL, $checklist_id = NULL, $date = NULL)
	{
		$this->VALID_UUIDv4($checklist_id);
		$this->data['survey'] = $this->form_model->get_survey($checklist_id);
		if( empty($this->data['survey']) ) { show_404(); }
		
		$this->questions = $this->form_model->get_question_structure($this->data['page']->page_id);

		$this->load->helper(array('form','forms'));
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		
		$this->answers = $this->form_model->get_answers($this->data['form']->survey_id,$checklist_id);
		$this->data['questions'] = $this->questions['structure'];
		$this->data['options']   = $this->questions['options'];
		$this->data['selected']  = $this->answers;
		
		if( ! empty($this->questions['field']['users']) )
		{
			foreach($this->questions['field']['users'] as $users)
			{
				foreach($this->users as $user)
				{
					$dropdown = new stdClass();
					$dropdown->option_id = $user->user_id;
					$dropdown->name      = $user->name;
					$this->data['options'][$users][$user->user_id] = $dropdown;
				}
			}
		}
	
		$this->load->view('general/form/checklist_view',$this->data);
	}
	
	public function checklist_update($page_id = NULL, $checklist_id = NULL, $date = NULL)
	{
		$this->VALID_UUIDv4($checklist_id);
		$this->data['survey'] = $this->form_model->get_survey($checklist_id);
		if( empty($this->data['survey']) ) { show_404(); }
		
		$this->questions = $this->form_model->get_question_structure($this->data['page']->page_id);

		$this->load->helper(array('form','forms'));
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		
		// @STEP2: Validation
		// $this->form_validation->set_rules( $validation_rules );
		// if( $this->form_validation->run() === TRUE )
		if( $this->input->method(TRUE) === 'POST' )
		{
			$new_checklist_id = UUIDv4();
			// Save form, give 404 otherwise
			if( $this->form_model->save_form(
					$this->data['form']->survey_id,
					$new_checklist_id,
					$this->questions['all'],
					$this->questions['field']
				) === FALSE
			)
			{
				show_404();
			}
			
			if( $this->form_model->save_surveys($new_checklist_id, $page_id, $this->data['survey']->survey_date, $checklist_id) )
			{
				
			}

			redirect('form/checklist/list/' . $page_id); exit;
		}
		else
		{
			$this->answers = $this->form_model->get_answers($this->data['form']->survey_id,$checklist_id);
			$this->data['questions'] = $this->questions['structure'];
			$this->data['options']   = $this->questions['options'];
			$this->data['selected']  = $this->answers;
			
			if( ! empty($this->questions['field']['users']) )
			{
				foreach($this->questions['field']['users'] as $users)
				{
					foreach($this->users as $user)
					{
						$dropdown = new stdClass();
						$dropdown->option_id = $user->user_id;
						$dropdown->name      = $user->name;
						$this->data['options'][$users][$user->user_id] = $dropdown;
					}
				}
			}
		
			$this->load->view('general/form/checklist_update',$this->data);
		}
	}

	/**
	 * The function is_date() validates the date and returns true or false
	 * @param $str sting expected valid date format
	 * @return bool returns true if the supplied parameter is a valid date 
	 * otherwise false
	 */
	private function is_date( $str ) {
		try
		{
			$dt = new DateTime( trim($str) );
		}
		catch( Exception $e )
		{
			return false;
		}
		$month = $dt->format('m');
		$day   = $dt->format('d');
		$year  = $dt->format('Y');
		if( checkdate($month, $day, $year) )
		{
			return true;
		}
		else
		{
			return false;
		}
	}
	
	// Answers [form_id, page_id, id[question_id,option_id], date, user_id, mixed, answer]
	/*
		forms_survey_ID
		forms_survey_025b5eca49
			P*company_id, ?user_id, P*question_id, *fiscal, ?department, answer
			
	CREATE TABLE `kiv-8-dev`.`form_survey_025b5eca49` ( `company_id` BINARY(16) NOT NULL , `page_id` BINARY(16) NOT NULL , `question_id` BINARY(16) NOT NULL , `fiscal` YEAR NOT NULL , `answer` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL , PRIMARY KEY (`company_id`, `page_id`, `question_id`, `fiscal`)) ENGINE = InnoDB;
	*/ 
	// Check if you have updated the page [page_id, fiscal, date]
	
	// TODO: Dynamic rules
}
