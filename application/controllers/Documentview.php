<?php
defined('BASEPATH') OR exit('No direct script access allowed');

require_once( dirname(__FILE__) . '/../libraries/onlyoffice/config.php' );
require_once( dirname(__FILE__) . '/../libraries/onlyoffice/common.php' );
require_once( dirname(__FILE__) . '/../libraries/onlyoffice/functions.php' );
require_once( dirname(__FILE__) . '/../libraries/onlyoffice/jwtmanager.php' );
require_once( dirname(__FILE__) . '/../libraries/onlyoffice/functions/editor.php' );

class Documentview extends MY_Controller
{

  public function __construct()
	{
		parent::__construct();
		$this->load->model(array('deviation_model'));
		$this->load->model('document_model');
		$this->load->model(array('user_model'));
    $this->load->model(array('folder_model','menu_model','group_model'));
    $this->auth_company_id  = $this->deviation_model->get_company_id();
		$this->users = $this->user_model->get_all();
		$CI =& get_instance();
		$CI->users_all = $this->users;
  }
  public function view( $document_id )
	{
		$this->VALID_UUIDv4($document_id);

		$this->_get_document_type_data();
		$this->data['documents_category'] = $this->document_model->get_document_category();
		$this->data['document'] = $this->document_model->get($document_id);
		if( empty($this->data['document']) ) { show_404(); }
		
		if( ! empty($this->data['document']->content) )
			$_SESSION['document_'.$document_id] = TRUE;

		$this->data['attachments'] = $this->document_model->get_attachments($document_id);
		$this->data['main_attachment'] = $attachment = null;
		$upload_base_path = CI_UPLOAD_PATH . 'documents';
		$upload_path = $upload_base_path . DIRECTORY_SEPARATOR . $this->auth_company_id;
		$this->data['auth_company_id'] = $this->auth_company_id;
		if (CI_ONLY_OFFICE)
		{
			$attachment = $this->document_model->get_main_attachment($document_id);
			$this->data['main_attachment'] = ($attachment && is_file($upload_path . DIRECTORY_SEPARATOR . $attachment->file_name)) ? $attachment->file_name : null;
		}
		$this->load->helper('form');
		$this->data['folder'] = $this->folder_model->get($this->data['document']->folder_id);
		$this->data['menu'] = $this->menu_model->get($this->data['folder']->menu_id);

		$this->data['groups'] = $this->group_model->get_all_by_relationship( 'folder_group', 'folder_id', $this->data['document']->folder_id, FALSE );
		if( empty($this->data['groups']) )
		{
			$this->data['groups'] = $this->group_model->get_all_by_relationship( 'menu_group', 'menu_id', $this->data['menu']->menu_id, FALSE );
		}

		$this->load->view('general/documents/viewpublic',$this->data);
	}

	public function inline( $document_id = NULL )
	{
		$this->VALID_UUIDv4($document_id);

		$this->data['document'] = $this->document_model->get_inline($document_id);
		if( empty($this->data['document']) ) { show_404(); }
		if( !isset($_SESSION['document_'.$document_id]) OR $_SESSION['document_'.$document_id] !== TRUE ) { show_404(); }
		unset($_SESSION['document_'.$document_id]);

		$this->load->view('general/documents/inline',$this->data);
	}

  public function doceditor( $attachment_file = NULL, $document_id = NULL )
	{
		
		log_message('debug', "doc editor " . $attachment_file);
		$upload_base_path = CI_UPLOAD_PATH . ($document_id == 'Post' ? 'posts' : 'documents');
		$dir_exists = TRUE;
		$upload_path = $upload_base_path . DIRECTORY_SEPARATOR . $this->auth_company_id ;
		$filename = $upload_path . DIRECTORY_SEPARATOR . $attachment_file;
		$fileuri = serverPath() . "/documentcallback/download_path/" . $this->auth_company_id . "/" . $attachment_file;
		$fileuriUser = $filename;
		$attachment_document_id = pathinfo($attachment_file, PATHINFO_FILENAME);
		$docKey = $attachment_file;
		$tite = "";
		if ($document_id == 'Post') {
			$fileuri = $fileuri . '/posts';
			$this->load->model('posts_model');
			$post = $this->posts_model->get_post($attachment_document_id);
			$title = '';
			if (!empty($post)) {
				$docKey = $docKey . filemtime($filename);
				$title = $post->name;
			}
		} else {
			$this->load->model('document_model');
			$attachment_document = $this->document_model->get($attachment_document_id);
			if (empty($attachment_document)){
				$docKey = $docKey . filemtime($filename);
				$attachment_document = $this->document_model->get_attachment( $attachment_document_id );
				$title = empty($attachment_document) ? "" : $attachment_document->file_name;
			} else  {
				$edit_date = str_replace(' ', '', $attachment_document->edited_date);
				$edit_date = str_replace(':', '', $edit_date);
				$edit_date = str_replace('-', '', $edit_date);
				$docKey = $docKey . $edit_date;
				$document = $this->document_model->get($document_id);
				$title = empty($document) ? "" : $document->name;
			}
		}
		$filetype = strtolower(pathinfo($filename, PATHINFO_EXTENSION));

		$uid = 'anonymous';
		$uname = '';

		$ugroup = "group-3";
		$reviewGroups = ["group-2"];

		$review = false;
		$editorsMode = false;
		$canEdit = false;
		$submitForm = false;
		$mode = "view";
		$type = "desktop";// empty($_GET["type"]) ? "desktop" : $_GET["type"];
		if ($mode == 'view' || $type == 'embedded') {
			$docKey = $attachment_file . filemtime($filename);
		}
		$current_url = getScheme() . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
		$config = [
			"type" => $type,
			"documentType" => getDocumentType($filename),
			"document" => [
				"title" => $title,
				"url" => $fileuri,
				"fileType" => $filetype,
				"key" => $docKey,
				"info" => [
					"owner" => "Me",
					"uploaded" => date('d.m.y'),
					"favorite" => isset($_GET["user"]) ? $_GET["user"] == 1 : null
				],
				"permissions" => [
					"print" => false,
					"copy" => false,
					"comment" => $editorsMode != "view" && $editorsMode != "fillForms" && $editorsMode != "embedded" && $editorsMode != "blockcontent",
					"download" => false,
					"edit" => $canEdit && ($editorsMode == "edit" || $editorsMode == "view" || $editorsMode == "filter" || $editorsMode == "blockcontent"),
					"fillForms" => $editorsMode != "view" && $editorsMode != "comment" && $editorsMode != "embedded" && $editorsMode != "blockcontent",
					"modifyFilter" => $editorsMode != "filter",
					"modifyContentControl" => $editorsMode != "blockcontent",
					"review" => $editorsMode == "edit" || $editorsMode == "review",
					"reviewGroups" => $reviewGroups
				]
			],
			"editorConfig" => [
				"actionLink" => empty($_GET["actionLink"]) ? null : json_decode($_GET["actionLink"]),
				"mode" => $mode,
				"lang" => empty($_COOKIE["ulang"]) ? "sv" : $_COOKIE["ulang"],
				"location" => "se",
				"region" => "sv-SE",
				"callbackUrl" => getCallbackUrl($this->auth_company_id, $attachment_file, $document_id == 'Post' ? '/posts' : ''),
				"user" => [
					"id" => $uid . rand(),
					"name" => $uname,
					"group" => $ugroup
				],
				"embedded" => [
					"saveUrl" => $fileuri,
					"embedUrl" => false,
					"shareUrl" => false,
					"fullscreenUrl" => $current_url,
					"toolbarDocked" => "top",
				],
				"customization" => [
					// only available for developer edition of onlyoffice
					"customer" => [
						"address" => "Catalinatorget 8, 183 68, Täby",
						"info" => "Legitimerade kliniker ur alla yrkesgrupper använder idag Kvalitet i vården (Orna) och Kvalprak FLEX",
						"logo" => "https://kvalprak.se/wp-content/uploads/2018/05/logotype-kvalprak.png",
						"logoDark" => "https://kvalprak.se/wp-content/uploads/2018/05/logotype-kvalprak.png",
						"mail" => "<EMAIL>",
						"name" => "Kvalprak",
						"www" => "https://kvalprak.se"
					],
					"logo" => [
						"image" => serverPath(false) . "/resources/img/orna-logo.png",
						"imageDark" => serverPath(false) . "/resources/img/orna-logo.png",
						"imageEmbedded" => serverPath(false) . "/resources/img/orna-logo.png",
						"url" => $fileuri
					],
					"uiTheme" => "theme-classic-light",
					"hideRightMenu" => true,
					"hideNotes" => true,
					"toolbarHideFileName" => true,
					"feedback" => false,
					"autosave" => true,
					"forcesave" => false,
					"review" => [
						"trackChanges" => false,
						// "reviewDisplay" => $review ? 'markup' : 'final'
						// "hoverMode" => $review,
					],
					"submitForm" => $submitForm,
					"goback" => [
						"url" => $current_url,
						"text" => "Fullskärm"
					]
				]
			]
		];
		$dataInsertImage = [
			"fileType" => "png",
			"url" => serverPath(true) . "/resources/css/images/logo.png"
		];

		$dataCompareFile = [
			"fileType" => "docx",
			"url" => serverPath(true) . "/documentcallback\/" . $this->auth_company_id .  '\/' . $attachment_file . ($document_id == 'Post' ? '/posts' : '')
				. "?type=assets&name=sample.docx"
		];

		$dataMailMergeRecipients = [
			"fileType" =>"csv",
			"url" => serverPath(true) . "/documentcallback\/" . $this->auth_company_id .  '\/' . $attachment_file . ($document_id == 'Post' ? '/posts' : '') 
				. "?type=csv"
		];
		if (isJwtEnabled()) {
			$config["token"] = jwtEncode($config);
			$dataInsertImage["token"] = jwtEncode($dataInsertImage);
			$dataCompareFile["token"] = jwtEncode($dataCompareFile);
			$dataMailMergeRecipients["token"] = jwtEncode($dataMailMergeRecipients);
		}

		$out = getHistory($filename, $filetype, $docKey, $fileuri);
		$this->data['history'] = $out[0];
		$this->data['historyData'] = $out[1];
		$this->data['document_type'] = getDocumentType($filename);
		$this->data['dataInsertImage'] = $dataInsertImage;
		$this->data['dataCompareFile'] = $dataCompareFile;
		$this->data['dataMailMergeRecipients'] = $dataMailMergeRecipients;
		$this->data['filename'] = $filename;
		$this->data['config'] = $config;
		$this->load->view('general/documents/onlyoffice_view',$this->data);
	}

	public function download( $attachment_id )
	{
		$this->VALID_UUIDv4($attachment_id);
		$attachment = $this->document_model->get_attachment( $attachment_id );
		if( empty($attachment) ) { show_404(); }

		$document = $this->document_model->get($attachment->document_id);
		if( empty($document) ) { show_404(); }

		$upload_base_path =  CI_UPLOAD_PATH . 'documents';
		$upload_path = $upload_base_path . DIRECTORY_SEPARATOR . $this->auth_company_id;
		$upload_file = $upload_path . DIRECTORY_SEPARATOR . $attachment->attachment_id . $attachment->file_ext;
		log_message('debug', "path " . $upload_file);
		if( ! file_exists($upload_file) )
			show_404();

		if($attachment->file_ext === '.pdf')
		{
			$this->load->helper('pdf');
			pdf($attachment, $upload_file);
		}
		else
		{
			$this->load->helper('download');
			force_download($attachment->file_name, file_get_contents($upload_file), TRUE);
		}
	}
}
