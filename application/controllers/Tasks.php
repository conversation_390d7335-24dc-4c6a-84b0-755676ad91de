<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Tasks extends Menu_Controller
{
	function __construct()
	{
		parent::__construct();
		$this->load->model(['task_model']);
		$this->data['sidebar']['module'] = 'task';
		// $this->data['body_class'] .= " sidebar-collapse";
		$this->data['users']['available'] = array();
		$users = $this->data['users']['available'] = $this->user_model->get_all();
		foreach($users as $user)
		{
			$this->data['users']['available'][$user->user_id] = $user->name;
		}
	}

	private function _get_task_goal_type_data()
	{
		$this->data['tasks_goal_type'] = array(
			0  => lang('tasks_goal_type_achievement'),
			1  => lang('tasks_goal_type_monitoring'),
		);
	}	

	private function _get_task_type_data()
	{
		$this->data['tasks_type'] = $this->task_model->get_task_type_data();
	}

	private function _get_task_metric_type_data()
	{
		$this->data['tasks_metric_type'] = $this->task_model->get_task_metric_type_data();
	}

	private function _get_task_state_data()
	{
		$this->data['tasks_state'] = array(
			0  => lang('tasks_state_not_started'),
			1  => lang('tasks_state_active'),
			2  => lang('tasks_state_paused'),
			3  => lang('tasks_state_finished')
		);
	}

	private function _get_task_reminder_data()
	{
		$this->data['reminder'] = array(
			0 => lang('no'),
			1 => lang('tasks_recurrence_monthly'),
			2 => lang('tasks_recurrence_quarterly'),
			3 => lang('tasks_recurrence_yearly'),
		);
	}
	
	private function _get_task_recurrence_data()
	{
		$this->data['recurrence'] = array(
			0 => lang('no'),
			1 => lang('tasks_recurrence_monthly'),
			2 => lang('tasks_recurrence_quarterly'),
			3 => lang('tasks_recurrence_yearly'),
		);
	}
	// @TODO: Check read permissions
	public function index( $task_id = NULL )
	{
		$this->VALID_UUIDv4($task_id);
		$this->_get_task_type_data();
		$this->_get_task_goal_type_data();
		$this->_get_task_metric_type_data();
		$this->_get_task_state_data();
		$this->_get_task_reminder_data();
		$this->_get_task_recurrence_data();

		$this->data['task'] = $this->task_model->get($task_id);
		if( empty($this->data['task']) ) { show_404(); }
		$this->data['all_task_progress'] = $this->task_model->get_progress_data($task_id);

		$this->data['task_editors'] = $this->group_model->get_all_by_relationship( 'task_resource', 'task_id', $task_id, FALSE, FALSE, 'user_id');
		$this->data['task_id'] = $task_id;

		$this->load->library('user_agent');

		if( $this->data['task']->parent_id !== NULL)
		{
			$this->data['task_parent'] = $this->task_model->get($this->data['task']->parent_id, ['published','unpublished']);
		}

		$progress = $this->data['task']->goal_type == 0 ? $this->task_model->get_progress($task_id) : $this->task_model->get_monitoring_progress($task_id);
		$progress_chart_data = array_map(function($item) { return [$item['date'], round($item['progress'], 2)]; }, $progress);
		$chart = array(
			'tooltip' => array( 'show' => true, 'trigger' => 'axis'), //, 'position' => 'line'),
			'title' => array( 'text' => lang('tasks_progress_chart'), 'left' => 'center'),
			'color' => ['#D8CBFD', '#8dc1a9'],
			'xAxis' => array(
				'type' => 'time',
				'axisPointer' => array('label' => array(
					'formatter' => "function format(params) {
						return echarts.format.formatTime('yyyy-MM-dd"
						. "', params.value);
					}",
				)),
				'boundaryGap' => FALSE,
			),
			'yAxis' => array(
				'type' => 'value',
				'name' => $this->data['tasks_metric_type'][$this->data['task']->metric_type],
				'nameLocation' => 'center',
				'nameGap' => 30,
			),
		);
		$values = array_map(function($item) { return $item['progress']; }, $progress);

		$chart['series'] = [ array(
				'name' => 'Progress',
				'type' => 'line',
				'data' => $progress_chart_data,
		)];
		$targets = $this->data['task']->goal_type == 0 ? json_decode($this->data['task']->targets, true) : [];
		function cmp($a, $b)
		{
				if ($a[0] == $b[0]) {
						return 0;
				}
				return ($a[0] < $b[0]) ? -1 : 1;
		}
		usort($targets, "cmp");
		$values = array_merge(array_map(function($item) { return $item[1]; }, $targets), $values);
		$chart_data = array(
			'name' => 'Target',
			'type' => 'line',
			'data' => $targets,
		);
		if ( $this->data['task']->target != 0 && $this->data['task']->goal_type != 0) {
			if (count($values) == 0 || $this->data['task']->target > max($values)) {
				$chart['yAxis']['max'] = $this->data['task']->target + 1;
			}
			$chart_data['markLine'] = [ "data" => [[
				'name' => 'Overall Target',
				'yAxis' => $this->data['task']->target,
				'label' => array (
					'position' => "end",
					'formatter' => lang('tasks_target') . ": {c}"
				),
			]]];
		}
		$chart['series'][] = $chart_data;
		$chart['series'][] = array(
			'name' => 'Base',
			'type' => 'line',
			'data' => $targets,
			'lineStyle' => array('opacity' => 0),
			'stack' => 'confidence-band',
			'symbol' => 'none',
			'tooltip' => array('show' => FALSE)
		);
		$chart['series'][] = array(
			'name' => 'U',
			'type' => 'line',
			'data' => array_map(function($e) { if (is_numeric($e[1])) {
						$e[1] *= 0.1; return $e;
					}
					return 0;
				}, $targets),
			'lineStyle' => array('opacity' => 0),
			'areaStyle' => array('color' => '#cce3c1'),
			'stack' => 'confidence-band',
			'symbol' => 'none',
			'tooltip' => array('show' => FALSE)
		);
		$chart['series'][] = array(
			'name' => 'L',
			'type' => 'line',
			'data' => array_map(function($e) { if (is_numeric($e[1])) {
						$e[1] *= 0.9; return $e;
					}
					return 0;
				}, $targets),
			'lineStyle' => array('opacity' => 0),
			'stack' => 'confidence-band-2',
			'symbol' => 'none',
			'tooltip' => array('show' => FALSE)
		);
		$chart['series'][] = array(
			'name' => 'Base2',
			'type' => 'line',
			'data' => array_map(function($e) { if (is_numeric($e[1])) {
						$e[1] *= 0.1; return $e;
					}
					return 0;
				}, $targets),
			'lineStyle' => array('opacity' => 0),
			'areaStyle' => array('color' => '#fcb1b1'),
			'stack' => 'confidence-band-2',
			'symbol' => 'none',
			'tooltip' => array('show' => FALSE)
		);
		$this->data['chart'] = $chart;
		$progress = $this->task_model->get_progress_by_user($task_id, $this->data['task']->goal_type == 1);
		$chart_user = array(
			'title' => array('text' => $this->data['task']->goal_type == 0 ? lang('tasks_progress_by_user_chart') :
			 		lang('tasks_progress_by_user_chart_monitoring') , 'left' => 'center'),
			'tooltip' => array('show' => true),
			'xAxis' => array(
				'type' => 'category',
				'data' => array_map(function($item) { return username($item['user']); }, $progress)
			),
			'yAxis' => array(
				'type' => 'value',
				'name' => $this->data['tasks_metric_type'][$this->data['task']->metric_type],
				'nameLocation' => 'center',
				'nameGap' => 30
			),
			'color' => ['#D8CBFD', "#e098c7", "#7cb4cc", '#E0B298', '#9b8bba', "#8dc1a9", '#DFE098', '#E09898'],
			'series' => [array(
				'name' => $this->data['tasks_metric_type'][$this->data['task']->metric_type],
				'type' => 'bar',
				// 'barWidth' => '88%',
				'data' => array_map(function($item) { return round($item['progress'], 2); }, $progress)
			)],
		);
		$this->data['chart_user'] = $chart_user;
		$this->load->helper('form');
		if($this->data['task']->status === 'unpublished')
		{
			$this->load->view('general/tasks/unpublished',$this->data);
		}
		else if( in_array($this->data['task']->status, ['draft']) )
		{
			$this->load->view('general/tasks/draft',$this->data);
		}
		else
		{
			$this->load->view('general/tasks/view',$this->data);
		}
	}

	public function draft( $task_id = NULL )
	{
		$this->VALID_UUIDv4($task_id);
		$this->data['task'] = $this->task_model->get($task_id);
		if( empty($this->data['task']) ) { show_404(); }

		$this->load->model('user_messages_model');
		$this->user_messages_model->remove( 'tasks', $this->auth_user_id, $task_id );

		if( $this->input->post('denied') )
		{
			$this->user_messages_model->create(
				$this->data['task']->edited_by,
				'tasks',
				$task_id,
				'denied',
				'critical',
				$this->input->post('comment')
			);
		}
		else
		{
			$parent_id = $this->data['task']->parent_id;
			$this->user_messages_model->create(
				$this->data['task']->edited_by,
				'tasks',
				$task_id,
				'accepted',
				'success',
				$this->input->post('comment')
			);
			$this->task_model->publish( $this->data['task'] );

			if( ! empty($parent_id) )
			{
				redirect('tasks/' . $parent_id); exit;
			}
			redirect('tasks/' . $task_id); exit;
		}

		redirect('tasks_list'); exit;
	}

	private function no_targets(array $array) {
    foreach ($array as $value) {
        if($value[1] == "") {
            return true;
        }
    }
    return false;
}

	private function _validate_targets()
	{
		$this->data['custom_validation_error'] = '';
		$targets = $this->input->post('tasks_targets');
		$goal_type = $this->input->post('tasks_goal_type');
		if ($goal_type == 0) {
			$targets_json = json_decode($targets);
			if (empty($targets_json) || $this->no_targets($targets_json)) {
				$this->data['custom_validation_error'] = '<div class="alert alert-danger">Mål är obligatoriskt.</div>';
				return false;
			}
		}
		return true;
	}
	
	public function create( )
	{
		$this->_get_task_type_data();
		$this->_get_task_goal_type_data();
		$this->_get_task_metric_type_data();
		$this->_get_task_state_data();
		$this->_get_task_reminder_data();
		$this->_get_task_recurrence_data();

		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$this->data['groups'] = $this->group_model->get_all([],'position');

		if( $this->input->method(TRUE) === 'POST' )
		{
			if( $new_task_id = $this->input->post('uuid_kvalprak') )
			{
				if( $this->VALID_UUIDv4($new_task_id) )
				{
					$this->data['uuid_kvalprak'] = $new_task_id;
				}
			}
		}
		else
		{
			$this->data['uuid_kvalprak'] = $new_task_id = UUIDv4();
		}

		// Load rules...
		$validation_rules = $this->_get_rules();

		// @TODO: Only includes those groups you got access too
		$this->data['rights'] = FALSE;
		if( $this->auth_kiv )
		{
			$this->data['rights'] = TRUE;
		}

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE && $this->_validate_targets() == TRUE )
		{
			$task               = new stdClass();
			$task->task_id  = $new_task_id;
			$task->created_by   = $this->auth_user_id;
			$task->created_date = date('Y-m-d H:i:s');

			$status = 'published';
			if( $this->auth_kiv )
			{
				$status = 'draft';
			}

			$task->status = $status;

			if( $this->task_model->save( $task, $status, $this->data['rights'] ) === TRUE )
			{

				$task_editors = $this->task_model->_get_tasks_resources();
				if( !empty($task_editors) )
				{
					if( $this->task_model->tasks_resources( $new_task_id, $task_editors ) === FALSE )
					{
						// @STEP2: If there are a error
					}
				}

				$task_groups = $this->task_model->_get_tasks_groups();
				if( !empty($task_groups) )
				{
					if( $this->task_model->tasks_groups( $new_task_id, $task_groups ) === FALSE )
					{
						// @STEP2: If there are a error
					}
				}

				$this->load->model('user_messages_model');
				if( $this->auth_kiv && $status == 'draft')
				{
					$this->user_messages_model->create(
						$this->input->post('tasks_accepted_by'),
						'tasks',
						$new_task_id,
						$status,
						'warning'
					);
				}

				redirect('tasks/' . $new_task_id ); exit;
			}
			else
			{
				// @TODO: Redirect if there are a problem
			}
		}
		else
		{
			$this->load->view('general/tasks/create',$this->data);
		}
	}

	private function task_progress_rules() {
		return array(
			array(
				'field' => 'created_date',
				'label' => lang('documents_created'),
				'rules' => array(
					'required'
				)
			),
			array(
				'field' => 'tasks_progress',
				'label' => lang('tasks_progress'),
				'rules' => array(
					'numeric',
					'required'
				)
			),
			array(
				'field' => 'tasks_hours_worked',
				'label' => lang('tasks_hours_worked'),
				'rules' => array(
					'numeric'
				)
			),
		);
	}

	public function edit_progress( $task_id, $progress_id )
	{

		$this->VALID_UUIDv4($progress_id);

		$this->data['progress'] = $this->task_model->get_task_progress($progress_id);
		if( empty($this->data['progress']) ) { show_404(); }

		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$rules = $this->task_progress_rules();

		$this->form_validation->set_rules( $rules );

		if( $this->form_validation->run() === TRUE )
		{
			$progress               = new stdClass();
			$progress->progress_id  = $progress_id;
			$progress->task_id      = $task_id;
			$progress->user_id   		= $this->auth_user_id;
			$progress->created_date = $this->input->post('created_date');

			$progress->progress = $this->input->post('tasks_progress');
			$progress->action = $this->input->post('action');
			$progress->achievement = $this->input->post('achievement');
			$progress->hours_worked = $this->input->post('hours_worked');
			$progress->issues = $this->input->post('issues');
			$progress->comment = $this->input->post('comment');
			$this->task_model->edit_progress( $progress );
			
			redirect('tasks/' . $task_id );

		}
		else
		{
			$this->data['task_id'] = $task_id;
			$this->load->view('general/tasks/add_edit_progress',$this->data);
		}
	}

	public function add_progress( $task_id )
	{
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');

		$new_progress_id = UUIDv4();

		$rules = $this->task_progress_rules();
		$this->form_validation->set_rules( $rules );

		if( $this->form_validation->run() === TRUE )
		{
			$progress               = new stdClass();
			$progress->progress_id  = $new_progress_id;
			$progress->task_id      = $task_id;
			$progress->user_id   		= $this->auth_user_id;
			$progress->created_date = $this->input->post('created_date');

			$progress->progress = $this->input->post('tasks_progress');
			$progress->action = $this->input->post('action');
			$progress->achievement = $this->input->post('achievement');
			$progress->hours_worked = $this->input->post('hours_worked');
			$progress->issues = $this->input->post('issues');
			$progress->comment = $this->input->post('comment');
			$this->task_model->add_progress( $progress );
			
			redirect('tasks/' . $task_id );
		}
		else
		{
			$this->data['task_id'] = $task_id;
			$this->load->view('general/tasks/add_edit_progress',$this->data);
		}
	}

	public function list( )
	{
		$this->_get_task_state_data();
		$this->_get_task_type_data();
		$this->_get_task_goal_type_data();
		$this->_get_task_goal_type_data();
		$tasks_list = $this->task_model->get_all_by_owner( $this->auth_user_id );
		$this->data['tasks'] = array_key_exists('published', $tasks_list) ? $tasks_list['published'] : array();
		$charts = array();
		foreach ($this->data['tasks'] as $key => $task) {
			if (!array_key_exists($task->task_type, $this->data['tasks_type'])) continue;
			$task_type = $this->data['tasks_type'][$task->task_type];
			if ($task->target == 0) continue;
			if (!array_key_exists($task_type, $charts))
				$charts[$task_type] = [0, 0];
			$charts[$task_type][0] += 100*$task->progress/$task->target;
			$charts[$task_type][1] += 100;
		}
		$this->data['charts'] = array();
		foreach ($charts as $key => $value) {
			$percent = 100*$value[0]/$value[1];
			$chart = array(
				'tooltip' => array('show' => true),
				'series' => [array(
				'name' => $key,
				'type' => 'gauge',
				'detail' => array('valueAnimation' => true),
				'color' => ($percent > 75) ? '#3ba272' : ( ($percent > 30) ? '#fc8452' : '#ee6666'),
				'progress' => array("show" => true),
				'data' => [ array("value" => round($percent), 'name' =>  $key) ]
			)]);
			$this->data['charts'][] = $chart;
		}
		$this->load->view('general/tasks/list',$this->data);
	}

	// @TODO: Check ACL
	public function update( $task_id = NULL )
	{
		$this->VALID_UUIDv4($task_id);

		$this->data['task'] = $task = $this->task_model->get($task_id);
		if( empty($task) ) { show_404(); }

		// $this->data['task'] = $task = $this->task_model->get($autosave_id, ['draft','edit-draft','auto-draft', 'waiting-approval']);

		$this->_get_task_type_data();
		$this->_get_task_goal_type_data();
		$this->_get_task_metric_type_data();
		$this->_get_task_state_data();
		$this->_get_task_reminder_data();
		$this->_get_task_recurrence_data();

		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$this->data['groups'] = $this->group_model->get_all([],'position');

		// UUID for Dropzone
		$this->data['uuid_kvalprak'] = $task_id;

		// UUID for autosave
		if($this->input->method(TRUE) == 'POST')
		{
			$this->data['uuid_autosave'] = $this->input->post('uuid_autosave');
		}
		else
		{
			$draft = ($task->status == 'published') ? $this->task_model->get_draft( $task_id ) : $task;
			if ($draft == null)
			{
				$this->task_model->create_draft( clone $task );
				$draft = $this->task_model->get_draft( $task_id );
			}
			$this->data['uuid_autosave'] = $draft ? $draft->task_id : (empty($this->data['uuid_autosave']) ? UUIDv4() : $this->data['uuid_autosave']);
			$this->data['draft'] = $draft;
		}

		// Load task editors
		$this->data['task_editors'] = $this->group_model->get_all_by_relationship( 'task_resource', 'task_id', $task_id, FALSE, FALSE, 'user_id');
		$this->data['task_groups'] = $this->group_model->get_all_by_relationship( 'task_group', 'task_id', $task_id, FALSE, FALSE, 'group_id');

		// Load rules...
		$validation_rules = $this->_get_rules();

		$this->data['rights'] = FALSE;
		if
		(
			$this->auth_kiv &&
			(
				is_role('Systemadministratör') OR
				in_array($this->auth_user_id, [$task->owner])
			)
		)
		{
			$this->data['rights'] = TRUE;
		}

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE && $this->_validate_targets() == TRUE )
		{
			$task->parent_id = $new_task_id = $this->data['uuid_autosave'];
			$owner = $this->data['task']->owner;

			$status = 'published';
			if( $this->auth_kiv )
			{
				$status = 'draft';
			}
			elseif( $this->auth_flex )
			{				
				$new_task_id = $task_id;
			}

			// In case a user denies a task that never have been published.
			if( in_array($task->status, ['draft']) )
			{
				$new_task_id = $task_id;
			}

			// @STEP2: temp save groups, editors
			if( $this->task_model->save( $task, $status, $this->data['rights'] ) === TRUE )
			{
				if( $this->data['rights'] )
				{
					
					$task_editors = $this->task_model->_get_tasks_resources();
					if( $this->task_model->tasks_resources( $task_id, $task_editors ) === FALSE )
					{
						// @STEP2: If there are a error
					}
					$task_groups = $this->task_model->_get_tasks_groups();
					if( $this->task_model->tasks_groups( $task_id, $task_groups ) === FALSE )
					{
						// @STEP2: If there are a error
					}
				}
				$this->task_model->tasks_resources($new_task_id, $task_editors);
				$this->task_model->tasks_groups($new_task_id, $task_groups);
				$this->load->model('user_messages_model');
				if( $this->auth_kiv )
				{
					if( $this->data['rights'] )
					{
						$owner = $this->input->post('tasks_accepted_by');
					}
					if ($status == 'draft') {
						$this->user_messages_model->create(
							$owner,
							'tasks',
							$new_task_id,
							$status,
							'warning'
						);
				  }
				}


				// Delete user message, in case there are one
				$this->user_messages_model->remove('tasks', $this->auth_user_id, $task_id);

				// Delete empty user_messages
				if( $ghost_tasks = $this->user_messages_model->check_empty('tasks', 'tasks', 'task_id') )
				{
					foreach($ghost_tasks as $d_id => $action)
					{
						$this->user_messages_model->remove_all_by_id('tasks', $d_id, $action);
					}
				}

				redirect('tasks/' . $new_task_id ); exit;
			}
			else
			{
				// @TODO: Redirect if there are a problem
			}
		}
		else
		{
			$this->load->view('general/tasks/update',$this->data);
		}
	}

	// public function update_edit_date($task_id)
	// {
	// 	$this->task_model->update_edit_date($task_id);
	// }

	public function archive()
	{
		if( ! $this->input->is_ajax_request() && $this->input->method(TRUE) !== 'POST' )
			exit;

		// @STEP2: Store it in $_SESSION and match it for security
		$task_id = $this->input->post('id');
		if( $this->task_model->get_task_exists($task_id) )
		{
			if( $this->task_model->archive($task_id) )
			{
				$this->output
						->set_status_header(200)
						->set_content_type('application/json', 'utf-8')
						->set_output(json_encode(['result' => TRUE, 'href' => NULL], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
						->_display();
				exit;
			}
		}

		$this->output
				->set_output( 'Okänt fel' )
				->set_status_header(400)
				->_display();
		exit;
	}

	private function _get_rules()
	{
		return array(
			array(
				'field' => 'tasks_name',
				'label' => lang('tasks_name'),
				'rules' => array(
					'trim',
					'required',
					'min_length[3]',
					'max_length[256]', // text
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']',
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'tasks_description',
				'label' => lang('tasks_description'),
				'rules' => array(
					'trim',
					'required',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']',
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'tasks_created',
				'label' => lang('tasks_created'),
				'rules' => array(
					'trim',
					'required',
					'regex_match['.$this->config->item('error_date').']',
				),
				'errors' => array(
					'regex_match' => lang('error_date')
				)
			),
			array(
				'field' => 'tasks_goal_type',
				'label' => lang('tasks_goal_type'),
				'rules' => array(
					'trim',
					'required',
					'in_list['.implode(',',array_keys($this->data['tasks_goal_type'])).']'
				)
			),
			array(
				'field' => 'tasks_type',
				'label' => lang('tasks_type'),
				'rules' => array(
					'trim',
					'required',
					'in_list['.implode(',',array_keys($this->data['tasks_type'])).']'
				)
			),
			array(
				'field' => 'tasks_metric_type',
				'label' => lang('tasks_metric_type'),
				'rules' => array(
					'trim',
					'required',
					'in_list['.implode(',',array_keys($this->data['tasks_metric_type'])).']'
				)
			),
			array(
				'field' => 'tasks_last_revised',
				'label' => lang('tasks_last_revised'),
				'rules' => array(
					'trim',
					'required',
					'regex_match['.$this->config->item('error_date').']',
				),
				'errors' => array(
					'regex_match' => lang('error_date')
				)
			),
			array(
				'field' => 'tasks_finish_date',
				'label' => lang('tasks_finish_date'),
				'rules' => array(
					'trim',
					'required',
					'regex_match['.$this->config->item('error_date').']',
				),
				'errors' => array(
					'regex_match' => lang('error_date')
				)
			),
			array(
				'field' => 'tasks_hours_needed',
				'label' => lang('tasks_hours_needed'),
				'rules' => array(
					'integer',
				),
				'errors' => array(
					'integer' => lang('error_number')
				)
			),
			array(
				'field' => 'tasks_state',
				'label' => lang('tasks_state'),
				'rules' => array(
					'trim',
					'required',
					'in_list['.implode(',',array_keys($this->data['tasks_state'])).']'
				)
			),
			array(
				'field' => 'tasks_reminder',
				'label' => lang('tasks_reminder'),
				'rules' => array(
					'trim',
					'required',
					'in_list['.implode(',',array_keys($this->data['reminder'])).']'
				)
			),
			array(
				'field' => 'tasks_recurrence',
				'label' => lang('tasks_recurrence'),
				'rules' => array(
					'trim',
					'required',
					'in_list['.implode(',',array_keys($this->data['recurrence'])).']'
				)
			),
			array(
				'field' => 'tasks_target',
				'label' => lang('tasks_target'),
				'rules' => array(
					'numeric'
				)
			),
		);
	}

	private function _get_rules_owner()
	{
		return array(
			array(
				'field' => 'tasks_owner',
				'label' => lang('tasks_owner'),
				'rules' => array(
					'trim',
					'in_list['.implode(',',array_keys($this->data['users']['owner']['available'])).']'
				)
			),
		);
	}
}