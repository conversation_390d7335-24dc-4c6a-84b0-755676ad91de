<?php
defined('BASEPATH') OR exit('No direct script access allowed');
require_once( dirname(__FILE__) . '/../libraries/onlyoffice/common.php' );
require_once( dirname(__FILE__) . "/../../vendor/autoload.php");

class Documents extends Menu_Controller
{
	function __construct()
	{
		parent::__construct();
		$this->load->model('document_model');
	}

	private function _get_document_category_data()
	{
		$this->data['documents_category'] = $this->document_model->get_document_category();
	}

	private function _get_document_reminder_data()
	{
		$this->data['reminder'] = array(
			0 =>           lang('no'),
			1 => 1 . ' ' . lang('month'),
			2 => 2 . ' ' . lang('months'),
			3 => 3 . ' ' . lang('months'),
			4 => 4 . ' ' . lang('months'),
			5 => 5 . ' ' . lang('months'),
			6 => 6 . ' ' . lang('months'),
		);
	}

	public function help()
	{
		$this->load->view('general/documents/help', $this->data);
	}

	public function standards()
	{
		$this->load->view('general/documents/standards', $this->data);
	}

	public function orna_analys_manual()
	{
		$this->load->view('general/documents/orna_analys', $this->data);
	}

	public function orna_updates()
	{
		$this->load->view('general/documents/orna_updates', $this->data);
	}

	// @TODO: Check read permissions
	public function index( $document_id = NULL )
	{
		$this->load->helper('form');
		$this->data['document_id'] = $document_id;
		$this->VALID_UUIDv4($document_id);
		$this->_get_document_type_data();
		$this->_get_document_category_data();

		$this->data['document'] = $this->document_model->get($document_id);
		if( empty($this->data['document']) ) { show_404(); }

		// @TODO: Check permissions and mark this as true
		if( ! empty($this->data['document']->content) )
			$_SESSION['document_'.$document_id] = TRUE;

		$this->data['document_version'] = count($this->document_model->get_all_by_parent_id($document_id)) + 1;
		// Remove persmission after 60 seconds
		// $this->session->mark_as_temp($_SESSION['document_'.$document_id], 60);
		$this->data['document_editors'] = $this->group_model->get_all_by_relationship( 'document_editor', 'document_id', $document_id, FALSE, FALSE, 'user_id');
		$this->data['attachments'] = $this->document_model->get_attachments($document_id);
		$this->data['main_attachment'] = $attachment = null;
		$upload_base_path = CI_UPLOAD_PATH . 'documents';
		$upload_path = $upload_base_path . DIRECTORY_SEPARATOR . $this->auth_company_id;
		$this->data['auth_company_id'] = $this->auth_company_id;
		if (CI_ONLY_OFFICE)
		{
			$attachment = $this->document_model->get_main_attachment($document_id);
			$this->data['main_attachment'] = ($attachment && is_file($upload_path . DIRECTORY_SEPARATOR . $attachment->file_name)) ? $attachment->file_name : null;
		}

		$this->load->library('user_agent');
		$education = FALSE;
		if( ! empty($this->agent->referrer()) )
		{
			if( str_replace(base_url(),'',$this->agent->referrer()) === 'reports/warning/education' )
			{
				$education = TRUE;
			} elseif (isset($_GET["education"]) && $_GET["education"] == 'true') {
				$education = TRUE;
			}
		}

		if( $this->data['document']->parent_id !== NULL)
			{
				$this->data['document_parent'] = $this->document_model->get($this->data['document']->parent_id, ['published','unpublished']);
				if( ! empty($this->data['document_parent']) )
				{
					// @TODO: Check permissions and mark this as true
					if( ! empty($this->data['document_parent']->content) )
						$_SESSION['document_'.$this->data['document_parent']->document_id] = TRUE;

					$this->data['attachments'] = $this->document_model->get_attachments($this->data['document_parent']->document_id);
					$this->data['parent_main_attachment'] = null;

					if (CI_ONLY_OFFICE) 
					{
						$attachment = $this->document_model->get_main_attachment($this->data['document_parent']->document_id);

						if($attachment != NULL)
						{
							$this->data['parent_main_attachment'] = $attachment->file_name;
							$file_path = $upload_path . DIRECTORY_SEPARATOR . $attachment->file_name;
							$createExt = pathinfo($file_path, PATHINFO_EXTENSION);
							$this->data['main_attachment'] = $document_id . '.' . $createExt;
						}
					}
				}
			}
			else
			{
				$this->data['attachments'] = $this->document_model->get_attachments($this->data['document']->document_id);
			}

		if( $education && in_array($this->data['document']->document_id, array_keys($this->data['report']['education']['warning'])) )
		{
			$this->data['folder'] = $this->folder_model->get($this->data['document']->folder_id);
			if( empty($this->data['folder']) ) { show_404(); }
			$this->load->view('general/documents/education',$this->data);
		}
		else if($this->data['document']->status === 'unpublished')
		{
			$this->data['folder'] = $this->folder_model->get($this->data['document']->folder_id);
			if( empty($this->data['folder']) ) { $this->data['folder'] = FALSE; }
			$this->load->view('general/documents/unpublished',$this->data);
		}
		else if( in_array($this->data['document']->status, ['waiting-approval']) )
		{

			$this->load->helper('form');
			if( isset($this->data['document_parent']->status) && $this->data['document_parent']->status === 'unpublished' )
			{
				$this->data['folder'] = $this->folder_model->get($this->data['document']->folder_id);
				if( empty($this->data['folder']) )
				{
					$this->data['folder'] = FALSE;
				}
				else
				{
					if( ! is_role('Systemadministratör') )
					{
						$this->data['folder'] = TRUE;
					}
					else
					{
						$this->_get_folder_data($this->data['document']->folder_id);
					}
				}
				$this->load->view('general/documents/draft_unpublished',$this->data);
			}
			else
			{
				$this->_get_folder_data($this->data['document']->folder_id);
				$this->load->view('general/documents/draft',$this->data);
			}
		}
		else
		{
			$this->document_model->insert_readlog( $document_id );
			$this->load->helper('form');
			$this->_get_folder_data($this->data['document']->folder_id);
			$this->data['groups'] = $this->group_model->get_all_by_relationship( 'folder_group', 'folder_id', $this->data['document']->folder_id, FALSE );
			if( empty($this->data['groups']) )
			{
				$this->data['groups'] = $this->group_model->get_all_by_relationship( 'menu_group', 'menu_id', $this->data['menu']['current']->menu_id, FALSE );
			}
			$this->load->view('general/documents/view',$this->data);
		}
	}

	public function inline( $document_id = NULL )
	{
		$this->VALID_UUIDv4($document_id);

		$this->data['document'] = $this->document_model->get_inline($document_id);
		if( empty($this->data['document']) ) { show_404(); }
		if( !isset($_SESSION['document_'.$document_id]) OR $_SESSION['document_'.$document_id] !== TRUE ) { show_404(); }
		unset($_SESSION['document_'.$document_id]);

		$this->load->view('general/documents/inline',$this->data);
	}

	public function inline_history( $document_id = NULL )
	{
		$this->VALID_UUIDv4($document_id);

		$this->data['document'] = $this->document_model->get_inline($document_id);
		if( empty($this->data['document']) ) { show_404(); }
		if( !isset($_SESSION['document_'.$document_id]) OR $_SESSION['document_'.$document_id] !== TRUE ) { show_404(); }
		unset($_SESSION['document_'.$document_id]);

		$this->load->view('general/documents/inline_history',$this->data);
	}
	// @TODO: ACL
	public function readlog( $document_id = NULL )
	{
		$this->VALID_UUIDv4($document_id);
		$this->data['document'] = $this->document_model->get($document_id);
		if( empty($this->data['document']) ) { show_404(); }
		$this->_get_folder_data($this->data['document']->folder_id);

		$this->data['readlog'] = $this->document_model->get_readlog_summary($document_id);
		$this->load->view('general/documents/readlog',$this->data);
	}

	public function approve_multiple()
	{
		foreach ($this->input->post('selected') as $document_id ) {
			$this->draft_internal( $document_id );
		}
	}


	// @TODO: ACL
	public function draft_internal( $document_id = NULL )
	{
		$this->VALID_UUIDv4($document_id);
		$this->data['document'] = $this->document_model->get($document_id);
		if( empty($this->data['document']) ) { show_404(); }
		$this->_get_folder_data($this->data['document']->folder_id);

		$this->load->model('user_messages_model');
		$this->user_messages_model->remove( 'documents', $this->auth_user_id, $document_id );
		$this->user_messages_model->remove_all_by_id( 'documents', $document_id, 'waiting-approval' );

		if( $this->input->post('denied') )
		{
			if (empty($this->input->post('comment'))) {
				$this->session->set_flashdata('error', 'Kommentar är obligatoriskt. Skriv kommentar i rutan nedan bredvid neka knappen.');
				return 'documents/' . $document_id;
			}
			$this->user_messages_model->create(
				$this->data['document']->edited_by,
				'documents',
				$document_id,
				'denied',
				'critical',
				$this->input->post('comment')
			);
		}
		else
		{
			if(
				$this->data['document']->status === 'edit-draft' &&
				$this->data['document']->owner === $this->auth_user_id
			)
			{
				$this->document_model->accepted($document_id);
				$this->user_messages_model->create(
					$this->data['menu']['current']->owner,
					'documents',
					$document_id,
					'draft',
					'warning'
				);
			}
			else
			{
				$parent_id = $this->data['document']->parent_id;
				$upload_base_path = CI_UPLOAD_PATH . 'documents';
				$upload_path = $upload_base_path . DIRECTORY_SEPARATOR . $this->auth_company_id;
				$this->user_messages_model->create(
					$this->data['document']->edited_by,
					'documents',
					empty($parent_id) ? $document_id : $parent_id,
					'accepted',
					'success',
					$this->input->post('comment')
				);
				$this->document_model->publish( $this->data['document'], $upload_path);

				if( ! empty($parent_id) )
				{
					if (CI_ONLY_OFFICE)
					{
						$attachment = $this->document_model->get_main_attachment($parent_id);
						if($attachment != NULL) {
						$new_file_path = $upload_path . DIRECTORY_SEPARATOR . $attachment->file_name;
						$createExt = pathinfo($new_file_path, PATHINFO_EXTENSION);

						$path = $upload_path . DIRECTORY_SEPARATOR . $document_id . '.' . $createExt;
						if(!@rename($path, $new_file_path))
							log_message('error', "Copy file error (draft 1) from " . $path . " to ". $new_file_path . " -- parent id: " . $parent_id);
						
						delDirectory($new_file_path . "-hist");
						if (!@rename($path . "-hist", $new_file_path . "-hist"))
							log_message('error', "Copy file error (draft 2) from " . $path . "-hist to ". $new_file_path . "-hist");
						// delDirectory($path . "-hist");
						// createMeta($new_file_path, $this->auth_user_id, $this->auth_name);

						$this->document_model->update_content($parent_id, $new_file_path);
						}
					}
					// Update education plan
					$this->load->model('education_model');
					$version = $this->education_model->get_document( $parent_id, TRUE );
					if( $version !== $this->data['document']->last_revised )
					{
						$this->education_model->update_document( $parent_id, $this->data['document']->last_revised );
					}
					return 'documents/' . $parent_id;
				}
				else if (CI_ONLY_OFFICE) {
					$attachment = $this->document_model->get_main_attachment($document_id);
					if($attachment != NULL)
						$this->document_model->update_content($document_id, $upload_path . DIRECTORY_SEPARATOR . $attachment->file_name);
				}
				return 'documents/' . $document_id;
			}
		}

		return 'reports/warning/draft';
	}

	public function draft( $document_id = NULL )
	{
		$redirect_url = $this->draft_internal($document_id);
		redirect($redirect_url);
	}

	// @TODO: Do you have write permissions
	public function create( $folder_id = NULL )
	{
		$this->VALID_UUIDv4($folder_id);
		$this->_get_folder_data($folder_id);
		$this->_get_document_type_data();
		$this->_get_document_category_data();
		$this->_get_document_reminder_data();

		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');

		$upload_base_path = CI_UPLOAD_PATH . 'documents';
		$dir_exists       = TRUE;
		$upload_path      = $upload_base_path . DIRECTORY_SEPARATOR . $this->auth_company_id;

		if( !is_dir($upload_path) )
			$dir_exists = mkdir($upload_path, 0777);

		// Get document_id from POST, so that attachments and text are added to the same document.
		// @STEP2: Store it in $_SESSION and match it for security
		if( $this->input->method(TRUE) === 'POST' )
		{
			if( $new_document_id = $this->input->post('uuid_kvalprak') )
			{
				if( $this->VALID_UUIDv4($new_document_id) )
				{
					$createExt = $this->input->post('createExt_kvalprak');

					if(!in_array($createExt, ['docx', 'xlsx', 'pptx']))
					{
						show_404();
					}

					$this->data['uuid_kvalprak'] = $new_document_id;
					$this->data['createExt_kvalprak'] = $createExt;
					$this->data['attachments'] = $this->document_model->get_attachments($new_document_id);
				}
			}
		}
		else
		{
			$this->data['uuid_kvalprak'] = $new_document_id = $this->input->get('uuid') ?? UUIDv4();

			$createExt = $this->input->get('createExt') ?? 'docx';

			if(!in_array($createExt, ['docx', 'xlsx', 'pptx']))
			{
				show_404();
			}

			$this->data['createExt_kvalprak'] = $createExt;

			// @ONLYOFFICE
			if(CI_ONLY_OFFICE)
			{
				$template_path = realpath(APPPATH . '..' . DIRECTORY_SEPARATOR . 'assets/new') . DIRECTORY_SEPARATOR . 'new.' . $createExt;
				if(file_exists(CI_UPLOAD_PATH . 'template.json'))
				{
					if(($json = file_get_contents(CI_UPLOAD_PATH . 'template.json')) !== FALSE)
					{
						if(($array = json_decode($json, true)) !== NULL)
						{
							$settings = $array;
							if (array_key_exists( $createExt, $settings ))
							{
								$template_path = CI_UPLOAD_PATH . $settings[$createExt];
							}
						}
					}
				}
				$new_file_path = $upload_path . DIRECTORY_SEPARATOR . $new_document_id . '.' . $createExt;

				if(!file_exists($new_file_path))
				{
					if(!@copy($template_path, $new_file_path))
					{
						log_message('error', "Copy file error (create) from " . $template_path . " to ". $new_file_path);
							//Copy error!!!
					}

					createMeta($new_file_path, $this->auth_user_id, $this->auth_name);
				}

				$document = $this->document_model->get($new_document_id, ['draft']);
				if (!$document) {
					$document               = new stdClass();
					$document->document_id  = $new_document_id;
					$document->created_by   = $this->auth_user_id;
					$document->created_date = date('Y-m-d H:i:s');
					$document->folder_id    = $folder_id;
					$document->status = 'draft';
					$document->document_category = UUID_TO_BIN(array_key_first($this->data['documents_category']));
					$this->document_model->save( $document, 'draft', $this->auth_kiv  );
				}
				$attachment = $this->document_model->get_main_attachment($new_document_id);
				if (!$attachment) {
					$attachment_id = UUIDv4();
					$upload_data = array();
					$upload_data['document_id']   = UUID_TO_BIN($new_document_id);
					$upload_data['attachment_id'] = UUID_TO_BIN($attachment_id);
					$upload_data['file_name']     = $new_document_id . '.' . $createExt;
					$upload_data['file_ext']      = '.' . $createExt;
					$upload_data['uploaded_on']   = date("Y-m-d H:i:s");

					if(!$this->document_model->save_attachments($upload_data))
					{
						log_message('error', 'cannot create attachment ' . $upload_path . DIRECTORY_SEPARATOR . $new_document_id . $upload_data['file_ext']);
					}

					$this->load->model('user_messages_model');
					$this->user_messages_model->create(
						$this->auth_user_id,
						'autosave',
						$new_document_id,
						'draft',
						'warning'
					);
				} else {
					$attachment->file_name = $new_document_id . '.' . $createExt;
					$attachment->file_ext  = '.' . $createExt;
					$this->document_model->update_attachment($attachment);
				}
			}
		}

		// UUID for autosave
		$this->data['uuid_autosave'] = UUIDv4();

		// Load rules...
		$validation_rules = $this->_get_rules();

		// @TODO: Only includes those groups you got access too
		$this->data['rights'] = FALSE;
		$position             = FALSE;
		$department           = FALSE;
		if( $this->auth_kiv )
		{
			$this->data['rights'] = TRUE;
		}
		if( $this->data['rights'] )
		{
			if( $this->config->item('groups_folder_and_documents') === TRUE )
			{
				$this->data['groups'] = $this->group_model->get_all_by_relationship( 'folder_group', 'folder_id', $folder_id );
				if( empty($this->data['groups']) )
				{
					$this->data['groups'] = $this->group_model->get_all_by_relationship( 'menu_group', 'menu_id', $this->data['menu']['current']->menu_id );
				}
				if( !empty($this->data['groups']['position']) )
				{
					$position         = TRUE;
					$validation_rules = array_merge($validation_rules, $this->_get_rules_position());
				}
				if( !empty($this->data['groups']['department']) )
				{
					$department       = TRUE;
					$validation_rules = array_merge($validation_rules, $this->_get_rules_department());
				}
			}
			else
			{
				$this->data['groups'] = $this->group_model->get_all_by_relationship( 'menu_group', 'menu_id', $this->data['menu']['current']->menu_id );
			}

			if( $position && $department )
			{
				$this->data['users']['available'] = $this->user_model->get_users_by_groups(
					array_keys($this->data['groups']['position']),
					array_keys($this->data['groups']['department'])
				);
			}
			else
			{
				$this->data['users']['available'] = $this->user_model->get_users_by_all_groups(
					array_merge(
						array_keys($this->data['groups']['position']),
						array_keys($this->data['groups']['department'])
					)
				);
			}
		}

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			$document               = new stdClass();
			$document->document_id  = $new_document_id;
			$document->created_by   = $this->auth_user_id;
			$document->created_date = date('Y-m-d H:i:s');
			$document->folder_id    = $folder_id;
			$status = 'published';
			if( $this->auth_kiv )
			{
				$status = 'draft';
				if ($this->input->post('submit-for-approval'))
				{
					$status = 'waiting-approval';
				}
			}
			$document->status = $status;
			$this->document_model->save( $document, $status, $this->data['rights'] );

			$document_groups = array();
			if( $position )
			{
				$document_groups = $this->document_model->_get_documents_position();
			}
			if( $department )
			{
				$document_groups = array_merge( $document_groups, $this->document_model->_get_documents_department() );
			}
			if( !empty($document_groups) )
			{
				if( $this->group_model->save( 'document_group', 'document_id', $new_document_id, $document_groups ) === FALSE )
				{
					// @STEP2: If there are a error
				}
			}

			$document_editors = $this->document_model->_get_documents_editors();
			if( !empty($document_editors) )
			{
				if( $this->document_model->documents_editors( $new_document_id, $document_editors ) === FALSE )
				{
					// @STEP2: If there are a error
				}
			}

			$this->load->model('user_messages_model');
			if( $this->auth_kiv && $document->status == 'waiting-approval')
			{
				$this->user_messages_model->create(
					$this->input->post('documents_accepted_by'),
					'documents',
					$new_document_id,
					$status,
					'warning'
				);
			}

			$this->document_model->delete_autosave( $new_document_id, $this->auth_user_id, 'auto-draft' );
			$this->user_messages_model->remove('autosave', $this->auth_user_id, $new_document_id);

			redirect('documents/' . $new_document_id . '?review=true' ); exit;
		}
		else
		{
			$this->load->view('general/documents/create',$this->data);
		}
	}
	// @TODO: ACL
	public function autosave($action = NULL, $autosave_id = NULL)
	{
		$this->load->model('user_messages_model');

		if( ! empty($autosave_id) )
		{
			$this->VALID_UUIDv4($autosave_id);
			$this->_get_document_type_data();
			$this->data['document'] = $document = $this->document_model->get($autosave_id, ['auto-draft']);
			if( empty($document) ) { show_404(); }
			$this->_get_folder_data($this->data['document']->folder_id);
			// @TODO: Delete uploaded files if there are no parent
			if( $action === 'permanentdelete')
			{
				if( $this->document_model->delete_autosave( $this->data['document']->parent_id, $this->auth_user_id, 'auto-draft' ) === TRUE )
				{
					$this->user_messages_model->remove('autosave', $this->auth_user_id, $this->data['document']->parent_id);
				}
				redirect('reports/warning/autosave'); exit;
			}

			if( $action === 'delete')
			{
				if( $this->document_model->delete_autosave( $this->data['document']->parent_id, $this->auth_user_id, 'auto-draft' ) === TRUE )
				{
					$this->user_messages_model->remove('autosave', $this->auth_user_id, $this->data['document']->parent_id);
				}
				redirect('documents/update/' . $this->data['document']->parent_id); exit;
			}

			if( $action === 'update' )
			{
				redirect('documents/update/' . $this->data['document']->parent_id . '/' . $autosave_id); exit;
			}

			if( $this->data['document']->parent_id !== NULL)
			{
				$this->data['document_parent'] = $this->document_model->get($this->data['document']->parent_id);
				if( ! empty($this->data['document_parent']) ) {
					// @TODO: Check permissions and mark this as true
					if( ! empty($this->data['document_parent']->content) )
						$_SESSION['document_'.$this->data['document_parent']->document_id] = TRUE;
				}
			}

			if( ! empty($this->data['document']->content) )
				$_SESSION['document_'.$autosave_id] = TRUE;

			$this->load->helper('form');
			$this->load->view('general/documents/autosave',$this->data);
		}

		if( $this->input->method(TRUE) === 'POST' && $this->input->post('uuid_kvalprak') && $this->input->post('uuid_autosave') )
		{
			$document               = new stdClass();
			$document->document_id  = $document_id = $this->input->post('uuid_kvalprak');
			$document->parent_id    = $this->input->post('uuid_autosave');
			$document->created_by   = $this->auth_user_id;
			$document->created_date = date('Y-m-d H:i:s');
			$document->folder_id    = $this->input->post('folder_id');
			$document->status       = 'auto-draft';

			if( $this->document_model->delete_autosave( $document->document_id, $document->created_by, 'auto-draft' ) === TRUE )
			{
				$this->user_messages_model->remove('autosave', $document->created_by, $document_id);

				if( $this->document_model->save( $document, 'auto-draft' ) === TRUE )
				{
					$this->user_messages_model->create(
						$this->auth_user_id,
						'autosave',
						$document_id,
						'autosave',
						'warning'
					);
					// @TODO: Success
				}
				else
				{
					// @TODO: Fail
				}
			}
		}
	}
	// @TODO: Check ACL
	public function update( $document_id = NULL, $autosave_id = NULL )
	{
		$this->VALID_UUIDv4($document_id);
		$this->VALID_UUIDv4($autosave_id,FALSE);

		$this->data['document'] = $document = $this->document_model->get($document_id);
		if( empty($document) ) { show_404(); }

		if( $autosave_id != NULL )
		{
			// UUID for autosave
			$this->data['uuid_autosave'] = $autosave_id;
		}

		// var_dump($document);exit;
		$this->_get_folder_data($document->folder_id);
		if( ! isset($this->data['menu']['current']->owner) )
		{
			redirect('documents/' . $document_id);
		}

		$this->_get_document_type_data();
		$this->_get_document_category_data();
		$this->_get_document_reminder_data();

		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');

		// UUID for Dropzone
		$this->data['uuid_kvalprak'] = $document_id;

		// UUID for autosave
		if($this->input->method(TRUE) == 'POST')
		{
			$this->data['uuid_autosave'] = $this->input->post('uuid_autosave');
		}
		elseif (!array_key_exists('attachment', $_GET))
		{
			$draft = ($document->status == 'published') ? $this->document_model->get_draft( $document_id ) : $document;
			if ($draft == null)
			{
				$this->document_model->create_draft( clone $document );
				$draft = $this->document_model->get_draft( $document_id );
			}
			$this->data['uuid_autosave'] = $draft ? $draft->document_id : (empty($this->data['uuid_autosave']) ? UUIDv4() : $this->data['uuid_autosave']);
			$this->data['draft'] = $draft;
		}

		$this->data['main_attachment'] = null;

		if(CI_ONLY_OFFICE && $autosave_id == NULL)
		{
			// @ONLYOFFICE
			$attachment = array_key_exists('attachment', $_GET) ? $this->document_model->get_attachment( $_GET['attachment'] ) : $this->document_model->get_main_attachment($document_id);
			$upload_base_path = CI_UPLOAD_PATH . 'documents';
			$upload_path = $upload_base_path . DIRECTORY_SEPARATOR . $this->auth_company_id;

			if(array_key_exists('attachment', $_GET))
			{
				$path = $upload_path . DIRECTORY_SEPARATOR . $attachment->file_name;
				$createExt = pathinfo($path, PATHINFO_EXTENSION);
				$this->data['main_attachment'] = $_GET['attachment'] . '.' . $createExt;
				$this->data['uuid_autosave'] = 'attachment';
			}
			elseif ($attachment && is_file($upload_path . DIRECTORY_SEPARATOR . $attachment->file_name))
			{
				$path = $upload_path . DIRECTORY_SEPARATOR . $attachment->file_name;
				$createExt = pathinfo($path, PATHINFO_EXTENSION);
				$new_file_path = $upload_path . DIRECTORY_SEPARATOR . $this->data['uuid_autosave'] . '.' . $createExt;

				if(!file_exists($new_file_path))
				{
					if(!@copy($path, $new_file_path))
					{
						log_message('error', "Copy file error (update) from " . $path . " to ". $new_file_path);
							//Copy error!!!
					}

					createMeta($new_file_path, $this->auth_user_id, $this->auth_name);
				}

				$this->data['main_attachment'] = $this->data['uuid_autosave'] . '.' . $createExt;
			}
		}

		// Load document editors
		if( $this->config->item('document_author') === FALSE )
		{
			$this->data['document_editors'] = $this->group_model->get_all_by_relationship( 'document_editor', 'document_id', $document_id, FALSE, FALSE, 'user_id');
		}

		// Load rules...
		$validation_rules = $this->_get_rules();

		$this->data['rights'] = FALSE;
		$position             = FALSE;
		$department           = FALSE;
		if
		(
			$this->auth_kiv &&
			(
				is_role('Systemadministratör') OR
				in_array($this->auth_user_id, [$document->owner,$this->data['menu']['current']->owner])
			)
		)
		{
			$this->data['rights'] = TRUE;
		}
		if( $this->data['rights'] )
		{
			if( $this->config->item('groups_folder_and_documents') === TRUE )
			{
				$this->data['groups'] = $this->group_model->get_all_by_relationship( 'folder_group', 'folder_id', $document->folder_id );
				if( empty($this->data['groups']) )
				{
					$this->data['groups'] = $this->group_model->get_all_by_relationship( 'menu_group', 'menu_id', $this->data['menu']['current']->menu_id );
				}

				$this->data['groups_checked'] = $this->group_model->get_all_by_relationship( 'document_group', 'document_id', $document_id, FALSE);

				if( !empty($this->data['groups']['position']) )
				{
					$position         = TRUE;
					$validation_rules = array_merge($validation_rules, $this->_get_rules_position());
				}
				if( !empty($this->data['groups']['department']) )
				{
					$department       = TRUE;
					$validation_rules = array_merge($validation_rules, $this->_get_rules_department());
				}
			}
			else
			{
				$this->data['groups'] = $this->group_model->get_all_by_relationship( 'menu_group', 'menu_id', $this->data['menu']['current']->menu_id );
			}

			if( $position && $department )
			{
				$this->data['users']['available'] = $this->user_model->get_users_by_groups(
					array_keys($this->data['groups']['position']),
					array_keys($this->data['groups']['department'])
				);
			}
			else
			{
				$this->data['users']['available'] = $this->user_model->get_users_by_all_groups(
					array_merge(
						array_keys($this->data['groups']['position']),
						array_keys($this->data['groups']['department'])
					)
				);
			}
		}

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			$document->parent_id = $new_document_id = (CI_ONLY_OFFICE && $autosave_id == NULL) ? $this->data['uuid_autosave'] : UUIDv4();
			$owner = $this->data['document']->owner;

			$status = 'published';
			if( $this->auth_kiv )
			{
				$status = 'draft';
				if ($this->input->post('submit-for-approval'))
				{
					$status = 'waiting-approval';
				}
			}
			elseif( $this->auth_flex )
			{
				$old_file_path = $upload_path . DIRECTORY_SEPARATOR . $this->data['uuid_autosave'] . '.' . $createExt;
				if(!@rename($old_file_path, $path ))
					log_message('error', "Copy file error (update flex 1) from " . $old_file_path . " to ". $path);
				
				delDirectory($path . "-hist");
				if (!@rename($old_file_path . "-hist", $path . "-hist"))
						log_message('error', "Copy file error (update flex 2) from " . $old_file_path . "-hist to ". $path . "-hist");
				// delDirectory($old_file_path . "-hist");
				// createMeta($path, $this->auth_user_id, $this->auth_name);
				
				$new_document_id = $document_id;
			}

			// In case a user denies a document that never have been published.
			if( in_array($document->status, ['draft','auto-draft-new', 'waiting-approval']) )
			{
				$new_document_id = $document_id;
			}

			// @STEP2: temp save groups, editors
			if( $this->document_model->save( $document, $status, $this->data['rights'] ) === TRUE )
			{
				if( $this->data['rights'] )
				{
					$document_groups = array();
					if( $position )
					{
						$document_groups = $this->document_model->_get_documents_position();
					}
					if( $department )
					{
						$document_groups = array_merge( $document_groups, $this->document_model->_get_documents_department() );
					}
					if( !empty($document_groups) )
					{
						if( $this->group_model->save( 'document_group', 'document_id', $document_id, $document_groups ) === FALSE )
						{
							// @STEP2: If there are a error
						}
					}

					$document_editors = $this->document_model->_get_documents_editors();
					if( $this->document_model->documents_editors( $document_id, $document_editors ) === FALSE )
					{
						// @STEP2: If there are a error
					}
				}
				$this->document_model->documents_editors($new_document_id, $this->data['document_editors']);
				$this->load->model('user_messages_model');
				if( $this->auth_kiv )
				{
					if( $this->data['rights'] )
					{
						$owner = $this->input->post('documents_accepted_by');
					}
					if ($status == 'waiting-approval') {
						$this->user_messages_model->create(
							$owner,
							'documents',
							$new_document_id,
							$status,
							'warning'
						);
				  }
				}


				// Delete user message, in case there are one
				$this->user_messages_model->remove('documents', $this->auth_user_id, $document_id);
				$this->user_messages_model->remove('autosave', $this->auth_user_id, $document_id);
				$this->user_messages_model->remove('documents', $this->auth_user_id, $new_document_id);
				$this->user_messages_model->remove('autosave', $this->auth_user_id, $new_document_id);

				// Delete message in case a user updates an edited file
				if( $autosave_id !== NULL )
				{
					$this->user_messages_model->remove('documents', $this->auth_user_id, $autosave_id);
				}

				// Delete empty user_messages
				if( $ghost_documents = $this->user_messages_model->check_empty('documents', 'documents', 'document_id') )
				{
					foreach($ghost_documents as $d_id => $action)
					{
						$this->user_messages_model->remove_all_by_id('documents', $d_id, $action);
					}
				}

				redirect('documents/' . $new_document_id . '?review=true'); exit;
			}
			else
			{
				// @TODO: Redirect if there are a problem
			}
		}
		else
		{
			$this->data['attachments'] = $this->document_model->get_attachments($document_id);
			$this->load->view('general/documents/update',$this->data);
		}
	}

	public function update_edit_date($document_id)
	{
		$this->document_model->update_edit_date($document_id);
	}

	public function upload()
	{
		if( ! $this->input->is_ajax_request() && $this->input->method(TRUE) !== 'POST' )
			exit;

		$input_document_id = $this->input->post('uuid_kvalprak');
		$template = $this->input->post('template');
		$template_name = $this->input->post('name');
		// @STEP2: Store it in $_SESSION and match it for security
		if( $document_id = $input_document_id )
		{
			if ($document_id == 'new')
			{
				$document_id = UUIDv4();
			}
			elseif ( ! VALID_UUIDv4($document_id) )
			{
				$this->output
						->set_output( 'Okänt fel' )
						->set_status_header(400)
						->_display();
				exit;
			}
			// Upload files
			$upload_base_path = CI_UPLOAD_PATH . 'documents';
			$dir_exists       = TRUE;
			$upload_data      = array();
			if( isset($template) || (!empty($_FILES['file']['name']) && $upload_base_path !== FALSE) )
			{
				// Create a folder for your company in case it dosen't exists
				$upload_path = $upload_base_path . DIRECTORY_SEPARATOR . $this->auth_company_id;
				if( !is_dir($upload_path) )
					$dir_exists = mkdir($upload_path, 0777);

				if( $dir_exists )
				{
					// Generate a unique ID for attached file
					$attachment_id = ($input_document_id == 'new') ? $document_id : UUIDv4();

					// File upload configuration
					$config['upload_path'] = $upload_path;
					$config['allowed_types'] = ($input_document_id == 'new') ? ['docx', 'xlsx', 'pptx'] : $this->config->item('allowed_types');
					$config['file_name'] = $attachment_id;

					// Load and initialize upload library
					$this->load->library('upload', $config);
					$this->upload->initialize($config);

					// Upload file to server
					if( !isset($template) && !$this->upload->do_upload('file') ){
						$this->output
								->set_output( $this->upload->display_errors('','') )
								->set_status_header(400)
								->_display();
						exit;
					}
					else
					{
						// Uploaded file data
						$filename = '';
						$file_ext = '';
						if (isset($template)) {
							$filename = $template_name . '.test';
							$file_ext = substr($template, -5);
							$template_path = (substr( $template, 0, 5 ) === "orna/") ? (realpath(APPPATH . '..' . DIRECTORY_SEPARATOR . 'assets/new') . 
								DIRECTORY_SEPARATOR . substr( $template, 5 )) : 
								(CI_UPLOAD_PATH . $template);
							$new_file_path = $upload_path . DIRECTORY_SEPARATOR . $attachment_id . $file_ext;
							if(!file_exists($new_file_path))
							{
								if(!@copy($template_path, $new_file_path))
								{
									log_message('error', "Copy file error (upload template) from " . $template_path . " to ". $new_file_path);
								}
							}
						}
						else {
							$file_data = $this->upload->data();
							$filename = empty($file_data['client_name']) ? $file_data['orig_name'] : $file_data['client_name'];
							$file_ext = $file_data['file_ext'];
						}
						
						$upload_data['document_id']   = UUID_TO_BIN($document_id);
						$upload_data['attachment_id'] = UUID_TO_BIN($attachment_id);
						$upload_data['file_name']     = ($input_document_id == 'new') ? ($document_id . $file_ext) : $filename;
						$upload_data['file_ext']      = $file_ext;
						$upload_data['uploaded_on']   = date("Y-m-d H:i:s");

						if( ! empty($upload_data) )
						{
							if ($input_document_id == 'new')
							{
								$folder_id = $this->input->post('folder_id');
								$this->VALID_UUIDv4($folder_id);
								$this->_get_folder_data($folder_id);
								$this->_get_document_category_data();
															
								$document               = new stdClass();
								$document->name					= substr($filename, 0, -5);
								$document->document_id  = $document_id;
								$document->created_by   = $this->auth_user_id;
								$document->created_date = date('Y-m-d H:i:s');
								$document->folder_id    = $folder_id;
								$document->status = 'draft';
								$document->document_category = UUID_TO_BIN(array_key_first($this->data['documents_category']));
								$this->document_model->save( $document, 'draft', $this->auth_kiv  );
							}

							// Insert files data into the database
							if( $this->document_model->save_attachments($upload_data) )
							{
								$uid = $this->auth_user_id;
								$name = $this->auth_name;
								createMeta($upload_path . DIRECTORY_SEPARATOR . $attachment_id . $upload_data['file_ext'], $uid, $name);
								$this->output
										->set_content_type('application/json', 'utf-8')
										->set_output( json_encode([
											'file_name'     => $upload_data['file_name'],
											'uploaded_on'   => $upload_data['uploaded_on'],
											'document_id'		=> $document_id,
											'attachment_id' => $attachment_id,
											'response'      => 'OK'
										], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES) )
										->set_status_header(200)
										->_display();
								exit;
							}
							else
							{
								// @STEP2: Delete file
							}
						}
					}
				}
			}
		}

		$this->output
				->set_output( 'Okänt fel' )
				->set_status_header(400)
				->_display();
		exit;
	}

	public function download( $attachment_id )
	{
		$this->VALID_UUIDv4($attachment_id);
		$attachment = $this->document_model->get_attachment( $attachment_id );
		if( empty($attachment) ) { show_404(); }

		$document = $this->document_model->get($attachment->document_id);
		if( empty($document) ) { show_404(); }

		$this->_get_folder_data($document->folder_id);

		$upload_base_path =  CI_UPLOAD_PATH . 'documents';
		$upload_path = $upload_base_path . DIRECTORY_SEPARATOR . $this->auth_company_id;
		$upload_file = $upload_path . DIRECTORY_SEPARATOR . $attachment->attachment_id . $attachment->file_ext;
		log_message('debug', "path " . $upload_file);
		if( ! file_exists($upload_file) )
			show_404();

		if($attachment->file_ext === '.pdf')
		{
			$this->load->helper('pdf');
			pdf($attachment, $upload_file);
		}
		else
		{
			$this->load->helper('download');
			force_download($attachment->file_name, file_get_contents($upload_file), TRUE);
		}
	}

	// Delete attachments
	// @STEP2: Secure delete
	public function delete()
	{
		if( ! $this->input->is_ajax_request() && $this->input->method(TRUE) !== 'POST' )
			exit;

		$attachment_id = $this->input->post('id');
		$attachment    = $this->document_model->get_attachment( $attachment_id );
		if( ! empty($attachment) )
		{
			if( $attachment->document_id === $this->input->post('uuid_kvalprak') )
			{
				$upload_base_path = CI_UPLOAD_PATH . 'documents';
				$upload_path = $upload_base_path . DIRECTORY_SEPARATOR . $this->auth_company_id;
				$upload_file = $upload_path . DIRECTORY_SEPARATOR . $attachment->attachment_id . $attachment->file_ext;

				if( file_exists($upload_file) )
				{
					if( $this->document_model->delete_attachments( $attachment->attachment_id ) )
					{
						if( unlink($upload_file) )
						{
							$this->output
									->set_status_header(200)
									->set_content_type('application/json', 'utf-8')
									->set_output(json_encode(['result' => TRUE], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
									->_display();
							exit;
						}
					}
				}
			}
		}

		$this->output
				->set_status_header(404)
				->set_content_type('application/json', 'utf-8')
				->set_output(json_encode(['result' => FALSE], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
				->_display();
		exit;
	}

	public function search()
	{
		$this->data['documents'] = NULL;
		$this->data['search'] = $this->input->get('s') ? $this->input->get('s') : NULL;

		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');

		$config = array(
			array(
				'field' => 'search',
				'label' => lang('search'),
				'rules' => array(
					'trim',
					'required',
					'min_length[3]',
					'max_length[64]', // text
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']',
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
		);

		$this->form_validation->set_data(['search' => $this->data['search']]);
		$this->form_validation->set_rules($config);
		if( $this->form_validation->run() === TRUE )
		{
			$this->data['documents'] = $this->document_model->search(
				explode(' ', $this->data['search']),
				array_merge($this->groups['types']['bin']['position'], $this->groups['types']['bin']['department']),
				$this->groups['types']['bin']['position'],
				$this->groups['types']['bin']['department']
			);
		}

		$this->load->view('general/documents/search',$this->data);
	}
	// @TODO: ACL
	public function move($document_id = NULL)
	{
		$this->VALID_UUIDv4($document_id);
		$this->_get_document_type_data();
		$this->_get_document_category_data();

		$this->data['document'] = $this->document_model->get($document_id);
		if( empty($this->data['document']) ) { show_404(); }
		// $this->_get_folder_data($this->data['document']->folder_id);

		// @TODO: Get $this->menus['all'] and get folders based on group
		$this->data['move']['menu'] = $this->menu_model->get_all();
		// var_dump($this->data['move']['menu']);exit;
		$this->data['move']['folder'] = $this->folder_model->get_all($this->data['move']['menu']['id'], TRUE);

		// var_dump($this->data['move']['menu'],$this->data['move']['folder']);

		$keep = [];
		foreach($this->data['move']['folder']['id'] AS $folder_id => $menu_id)
		{
			if( $this->data['move']['menu']['parent'][$menu_id] )
			{
				$parent_id = $this->data['move']['menu']['parent'][$menu_id];
				if( isset($this->data['move']['menu'][$parent_id]) )
				{
					$keep[] = $menu_id;
					$keep[] = $parent_id;
				}

				if(isset($this->data['move']['menu']['parent'][$parent_id]))
				{
					$top_menu = $this->data['move']['menu']['parent'][$parent_id];
					if( isset($this->data['move']['menu'][$top_menu]) )
					{
						$keep[] = $top_menu;
					}
				}
			}
		}

		$this->data['move']['menu']['loop'] = $keep;

		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		// var_dump($this->data['move']['folder']);

		$config = array(
			array(
				'field' => 'move_document',
				'label' => lang('folder_folder'),
				'rules' => array(
					'trim',
					'required',
					'in_list['.implode(',',array_keys($this->data['move']['folder']['id'])).']'
				),
			),
		);

		$this->form_validation->set_rules($config);
		if( $this->form_validation->run() === TRUE )
		{
			$folder_id = $this->input->post('move_document');
			if( $this->document_model->move($document_id, $folder_id) )
			{
				redirect('documents/' . $document_id); exit;
			}
		}
		else
		{
			$this->load->view('general/documents/move',$this->data);
		}
	}
	// @TODO: ACL
	public function history( $document_id = NULL )
	{
		$this->VALID_UUIDv4($document_id);
		$this->_get_document_type_data();
		$this->_get_document_category_data();

		$this->data['document'] = $this->document_model->get($document_id);
		$this->data['main_attachment'] = $attachment = null;
		if (CI_ONLY_OFFICE)
		{
			$attachment = $this->document_model->get_main_attachment($document_id);
			$this->data['main_attachment'] = $attachment ? $attachment->file_name : null;
		}
		if( empty($this->data['document']) ) { show_404(); }

		$this->_get_folder_data($this->data['document']->folder_id);
		if( ! isset($this->data['menu']['current']->owner) )
		{
			redirect('documents/' . $document_id);
		}
		// $this->data['sidebar']['active'] = FALSE;
		$this->data['body_class'] .= " sidebar-collapse";

		// @TODO: Check permissions and mark this as true
		if( ! empty($this->data['document']->content) )
			$_SESSION['document_'.$document_id] = TRUE;

		$this->data['documents'] = $this->document_model->get_all_by_parent_id($document_id);
		if( ! empty($this->data['documents']) )
		{
			$createExt = $attachment ? pathinfo($attachment->file_name, PATHINFO_EXTENSION) : null;
			$history_attachments = [];
			foreach($this->data['documents'] as $d)
			{
				$upload_base_path = CI_UPLOAD_PATH . 'documents';
				$upload_path = $upload_base_path . DIRECTORY_SEPARATOR . $this->auth_company_id;
				if ( !is_file($upload_path . DIRECTORY_SEPARATOR . $d->document_id . '.' . $createExt))
					$_SESSION['document_'.$d->document_id] = TRUE;
				else
					$history_attachments['document_'.$d->document_id] = $attachment ? ($d->document_id . '.' . $createExt) : null;
			}
			$this->data['history_attachments'] = $history_attachments;
		}

		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');

		$config = array(
			array(
				'field' => 'uuid_kvalprak',
				'label' => lang('restore'),
				'rules' => array(
					'trim',
					'required',
					'in_list['.implode(',',array_keys($this->data['documents'])).']'
				),
			),
		);

		$this->form_validation->set_rules($config);
		if( $this->form_validation->run() === TRUE )
		{
			$document = $this->document_model->get($this->input->post('uuid_kvalprak'), ['archived']);
			$document->document_id = UUIDv4();
			$new_document_id = $document->parent_id;

			if (CI_ONLY_OFFICE)
			{
				$attachment = $this->document_model->get_main_attachment($this->input->post('uuid_kvalprak'));
				if($attachment != NULL && is_file($upload_path . DIRECTORY_SEPARATOR . $attachment->file_name)) {
					$upload_base_path = CI_UPLOAD_PATH . 'documents';
					$upload_path = $upload_base_path . DIRECTORY_SEPARATOR . $this->auth_company_id;

					$path = $upload_path . DIRECTORY_SEPARATOR . $attachment->file_name;
					$createExt = pathinfo($path, PATHINFO_EXTENSION);
					$new_file_path = $upload_path . DIRECTORY_SEPARATOR . $document->document_id . '.' . $createExt;

					if(!file_exists($new_file_path))
					{
						if(!@copy($path, $new_file_path))
						{
							log_message('error', "Copy file error (update) from " . $path . " to ". $new_file_path);
								//Copy error!!!
						}
	
						createMeta($new_file_path, $this->auth_user_id, $this->auth_name);
					}
				}
			}

			$status = 'published';
			if( $this->auth_kiv )
			{
				$status = 'draft';
				$new_document_id = $document->document_id;

			}
			if( $this->document_model->history( $document, $status ) === TRUE )
			{
				if( $this->auth_kiv )
				{
					$this->load->model('user_messages_model');
					$this->user_messages_model->create(
						$this->data['menu']['current']->owner,
						'documents',
						$new_document_id,
						$status,
						'warning'
					);

					// @STEP2: Flash message
				}

				redirect('documents/' . $new_document_id ); exit;
			}
			else
			{
				// @STEP2: Redirect if there are a problem
			}
		}
		else
		{
			$this->load->view('general/documents/history',$this->data);
		}
	}

	public function delete_draft()
	{
		if( ! $this->input->is_ajax_request() && $this->input->method(TRUE) !== 'POST' )
			exit;
		$other = $this->input->post('other');
		$redirect = $this->input->post('redirect');
		$document_id = $this->input->post('id');
		if( $this->document_model->delete_draft($document_id) )
		{
			$this->load->model('user_messages_model');
			$this->user_messages_model->remove('documents', $this->auth_user_id, $document_id);
			$this->user_messages_model->remove('autosave', $this->auth_user_id, $document_id);
			$this->output
					->set_status_header(200)
					->set_content_type('application/json', 'utf-8')
					->set_output(json_encode(['result' => TRUE, 'href' => $other == 'redirect' ? 
					((isset($redirect) && !empty($redirect)) ? $redirect : '/reports/warning/autosave') : NULL], 
					JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
					->_display();
			exit;
		}
	}

	public function archive()
	{
		if( ! $this->input->is_ajax_request() && $this->input->method(TRUE) !== 'POST' )
			exit;

		// @STEP2: Store it in $_SESSION and match it for security
		$document_id = $this->input->post('id');
		if( $this->document_model->get_document_exists($document_id) )
		{
			if( $this->document_model->archive($document_id) )
			{
				$this->output
						->set_status_header(200)
						->set_content_type('application/json', 'utf-8')
						->set_output(json_encode(['result' => TRUE, 'href' => NULL], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
						->_display();
				exit;
			}
		}

		$this->output
				->set_output( 'Okänt fel' )
				->set_status_header(400)
				->_display();
		exit;
	}

	public function unarchive()
	{
		if( ! $this->input->is_ajax_request() && $this->input->method(TRUE) !== 'POST' )
			exit;

		// @STEP2: Store it in $_SESSION and match it for security
		$document_id = $this->input->post('id');
		if( $this->document_model->get_document_exists($document_id) )
		{
			if( $this->document_model->unarchive($document_id) )
			{
				$this->output
						->set_status_header(200)
						->set_content_type('application/json', 'utf-8')
						->set_output(json_encode(['result' => TRUE, 'href' => NULL], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
						->_display();
				exit;
			}
		}

		$this->output
				->set_output( 'Okänt fel' )
				->set_status_header(400)
				->_display();
		exit;
	}

	public function education()
	{
		if( ! $this->input->is_ajax_request() && $this->input->method(TRUE) !== 'POST' )
			exit;

		// @STEP2: Store it in $_SESSION and match it for security
		$document_id = $this->input->post('id');
		if( $version = $this->document_model->get_document_exists($document_id, TRUE) )
		{
			$this->load->model('education_model');
			if( $this->education_model->read($document_id, $version) )
			{
				$this->output
						->set_status_header(200)
						->set_content_type('application/json', 'utf-8')
						->set_output(json_encode(['result' => TRUE, 'href' => base_url('reports/warning/education')], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
						->_display();
				exit;
			}
		}

		$this->output
				->set_output( 'Okänt fel' )
				->set_status_header(400)
				->_display();
		exit;
	}

	public function elfinder()
	{
		// Delete cache
		$cache = CI_UPLOAD_PATH . 'elfinder' . DIRECTORY_SEPARATOR . '.cache';

		if(file_exists($cache))
			unlink($cache);

		// elFinder autoload
		require APPPATH .'/libraries/elfinder/autoload.php';

		$opts = array(
			'debug' => true,
			'roots' => array(
				// Items volume
				array(
					'driver'        => 'LocalFileSystem',                               // driver for accessing file system (REQUIRED)
					'path'          => CI_UPLOAD_PATH . 'elfinder/',                    // path to files (REQUIRED)
					'URL'           => '/resources/uploads/',                           // URL to files (REQUIRED)
					'trashHash'     => 't1_Lw',                                         // elFinder's hash of trash folder
					'winHashFix'    => DIRECTORY_SEPARATOR !== '/',                     // to make hash same to Linux one on windows too
					'uploadDeny'    => array('all'),                                    // All Mimetypes not allowed to upload
					'uploadAllow'   => array('image', 'text/plain', 'application/pdf'), // Mimetype `image` and `text/plain` allowed to upload
					'uploadOrder'   => array('deny', 'allow'),                          // allowed Mimetype `image` and `text/plain` only
					'accessControl' => array($this, 'elfinderAccess'),                  // disable and hide dot starting files (OPTIONAL)
					'uploadOverwrite' => false,
				),
				// Trash volume
				array(
					'id'            => '1',
					'driver'        => 'Trash',
					'path'          => CI_UPLOAD_PATH . 'elfinder/.trash/',
					'tmbURL'        => base_url('/resources/uploads/.trash/.tmb/'),
					'winHashFix'    => DIRECTORY_SEPARATOR !== '/',                     // to make hash same to Linux one on windows too
					'uploadDeny'    => array('all'),                                    // Recomend the same settings as the original volume that uses the trash
					'uploadAllow'   => array('image', 'text/plain', 'application/pdf'), // Same as above
					'uploadOrder'   => array('deny', 'allow'),                          // Same as above
					'accessControl' => array($this, 'elfinderAccess'),                  // Same as above
				)
			)
		);

		$connector = new elFinderConnector(new elFinder($opts));
		$connector->run();
	}

	/**
	 * Simple function to demonstrate how to control file access using "accessControl" callback.
	 * This method will disable accessing files/folders starting from '.' (dot)
	 *
	 * @param  string    $attr    attribute name (read|write|locked|hidden)
	 * @param  string    $path    absolute file path
	 * @param  string    $data    value of volume option `accessControlData`
	 * @param  object    $volume  elFinder volume driver object
	 * @param  bool|null $isDir   path is directory (true: directory, false: file, null: unknown)
	 * @param  string    $relpath file path relative to volume root directory started with directory separator
	 * @return bool|null
	 **/
	public function elfinderAccess($attr, $path, $data, $volume, $isDir, $relpath) {
		$basename = basename($path);
		return $basename[0] === '.'                  // if file/folder begins with '.' (dot)
				 && strlen($relpath) !== 1           // but with out volume root
			? !($attr == 'read' || $attr == 'write') // set read+write to false, other (locked+hidden) set to true
			:  null;                                 // else elFinder decide it itself
	}

	private function _get_rules()
	{
		return array(
			array(
				'field' => 'documents_name',
				'label' => lang('documents_name'),
				'rules' => array(
					'trim',
					'required',
					'min_length[3]',
					'max_length[256]', // text
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']',
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'documents_description',
				'label' => lang('documents_description'),
				'rules' => array(
					'trim',
					// 'required',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']',
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'documents_created',
				'label' => lang('documents_created'),
				'rules' => array(
					'trim',
					'required',
					'regex_match['.$this->config->item('error_date').']',
				),
				'errors' => array(
					'regex_match' => lang('error_date')
				)
			),
			array(
				'field' => 'documents_type',
				'label' => lang('documents_type'),
				'rules' => array(
					'trim',
					'required',
					'in_list['.implode(',',array_keys($this->data['documents_type'])).']'
				)
			),
			array(
				'field' => 'documents_category',
				'label' => lang('documents_category'),
				'rules' => array(
					'trim',
					'in_list['.implode(',',array_keys($this->data['documents_category'])).']'
				)
			),
			array(
				'field' => 'documents_last_revised',
				'label' => lang('documents_last_revised'),
				'rules' => array(
					'trim',
					'required',
					'regex_match['.$this->config->item('error_date').']',
				),
				'errors' => array(
					'regex_match' => lang('error_date')
				)
			),
			array(
				'field' => 'documents_valid_until',
				'label' => lang('documents_valid_until'),
				'rules' => array(
					'trim',
					'required',
					'regex_match['.$this->config->item('error_date').']',
				),
				'errors' => array(
					'regex_match' => lang('error_date')
				)
			),
			array(
				'field' => 'documents_reminder',
				'label' => lang('documents_reminder'),
				'rules' => array(
					'trim',
					'required',
					'in_list['.implode(',',array_keys($this->data['reminder'])).']'
				)
			),
		);
	}

	private function _get_rules_owner()
	{
		return array(
			array(
				'field' => 'documents_owner',
				'label' => lang('documents_owner'),
				'rules' => array(
					'trim',
					'in_list['.implode(',',array_keys($this->data['users']['owner']['available'])).']'
				)
			),
		);
	}

	private function _get_rules_editors()
	{
		return array(
			array(
				'field' => 'documents_editors[]',
				'label' => lang('documents_editors'),
				'rules' => array(
					'trim',
					'in_list['.implode(',',array_keys($this->data['users']['editors']['available'])).']'
				)
			),
		);
	}

	private function _get_rules_position()
	{
		return array(
			array(
				'field' => 'documents_position[]',
				'label' => lang('groups_type_position'),
				'rules' => array(
					'trim',
					'in_list['.implode(',',array_keys($this->data['groups']['position'])).']'
				)
			),
		);
	}

	private function _get_rules_department()
	{
		return array(
			array(
				'field' => 'documents_department[]',
				'label' => lang('groups_type_department'),
				'rules' => array(
					'trim',
					'in_list['.implode(',',array_keys($this->data['groups']['department'])).']'
				)
			),
		);
	}
}