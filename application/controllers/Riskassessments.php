<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Riskassessments extends User_Controller
{
	public function __construct()
	{
		parent::__construct();
		$this->data['body_class'] .= 'skin-white fixed';
		$this->load->model('group_model');
		$db = [
			'hostname' => $this->db->hostname,
			'database' => $this->db->database,
			'username' => $this->db->username,
			'password' => $this->db->password,
		];
		$this->load->library('riskassessmentslib',$db);
		$this->load->helper(array('form','forms'));
		$this->load->helper('old_form');
		$this->departments = '';
		$this->data['sidebar']['module'] = 'riskassessment';
		// $this->data['body_class'] .= " sidebar-collapse";
	}
	
	public function add()
	{
		$this->data['rights'] = $this->acl['deviation'];
		if( empty($this->data['rights']['create']) ) { show_404(); }
		
		if( $this->input->method(TRUE) === 'POST' )
		{
			// All general forms
			$answers = array();
			foreach($_POST as $key => $val) {
				if( $key != 'submit' && 
					!preg_match('/department/',$key) && 
					!preg_match('/risk/',$key)
				) {
					$answers[0][$key] = $val;
				}
			}
			
			$ra_id = $this->riskassessmentslib->insertRisk($answers,$this->auth_company_id);
			
			// Risk department
			if( $this->auth_kiv )
			{
				$de_id = (isset($_POST['department']))?$_POST['department']:'';
				if($de_id != '') {
					$department = array();
					foreach($de_id AS $key => $val) {
						$department[] = array(
							'ra_id' => UUID_TO_BIN($ra_id),
							'de_id' => UUID_TO_BIN($val)
						);
					}
				}
			}

			// var_dump($department);exit;
			
			// Risks
			if(isset($_POST['riskName'])) {
				$risks = array();
				$i = 0;
				foreach($_POST['riskName'] as $id => $risk) {
					$risk_id = UUIDv4();
					$risks[] = array(
						'id'			=> UUID_TO_BIN($risk_id),
						'ra_id'			=> UUID_TO_BIN($ra_id),
						'risk'			=> $risk,
						'occurrence'	=> $_POST['riskOccurrence'][$i],
						'severity'		=> $_POST['riskSeverity'][$i],
						'explanation'	=> $_POST['riskExplanation'][$i],
						'acceptable'	=> isset($_POST['riskAcceptable'][$i]) ? $_POST['riskAcceptable'][$i] : 0,
						'measure'		=> $_POST['riskMeasure'][$i],
						'responsible'	=> isset($_POST['riskResponsible'][$i]) ? UUID_TO_BIN($_POST['riskResponsible'][$i]) : NULL,
						'done'			=> isset($_POST['riskDone'][$i]) && !empty($_POST['riskDone'][$i]) ? $_POST['riskDone'][$i] : date('Y-m-d'),
						'finished'		=> isset($_POST['riskFinished'][$i]) && !empty($_POST['riskFinished'][$i]) ? $_POST['riskFinished'][$i] : date('Y-m-d')
					);
					++$i;
				}
			}
			
			
			if(isset($department) && ! empty($department))
				$this->riskassessmentslib->insertRiskDepartment($department);
			
			if(!empty($risks))
				$this->riskassessmentslib->insertRisks($risks);
			
			redirect('riskassessments/view/' . $ra_id); exit;
		}
		else
		{
			$this->data['fields'] = array(
				array(
					'id' => 'name',
					'input' => 'input',
					'required' => 1,
					'title' => 'Rubrik',
					'description' => 'Ange kortfattat vad som rapporteras.',
					'value' => null,
					'values' => null
				),
				array(
					'id' => 'description',
					'input' => 'text',
					'required' => 1,
					'title' => 'Ärendebeskrivning',
					'description' => 'Beskriv riskbedömningen.',
					'value' => null,
					'values' => null
				),
				array(
					'id' => 'date',
					'input' => 'date',
					'required' => 0,
					'title' => 'Datum',
					'description' => 'När skapades riskbedömningen?',
					'value' => date('Y-m-d'),
					'values' => null
				),
			);
			
			if( $this->auth_kiv )
			{
				$this->data['groups'] = $this->group_model->get_all($this->groups['types']['uuid']['department'], 'department');
				$this->data['fields'] = array_merge($this->data['fields'], array(
					array(
						'id' => 'us_id',
						'input' => 'users',
						'required' => 0,
						'title' => 'Användare',
						'description' => 'Vem är ansvarig för riskbedömningen?',
						'value' => null,
						'values' => $this->users
					),
					array(
						'id' => 'department',
						'input' => 'department',
						'required' => 0,
						'title' => 'Enhet',
						'description' => 'Ange de(n) enhet(er) som riskbedömningen berör.',
						'value' => array(),
						'values' => $this->data['groups']['department']
					)
				));
			}

			$riskOccurrenceValues = array(
				0 => '-- Frekvens --',
				1 => 'Låg (Har inte hänt i Sverige)',
				2 => 'Måttlig (Har hänt någon gång i Sverige)',
				3 => 'Hög (Händer någon gång per år I verksamheten)',
				4 => 'Mycket hög (Händer flera gånger om året i verksamheten)'
			);
			$riskSeverityValues = array(
				0 => '-- Allvarlighetsgrad --',
				1 => 'Lindrig (övergående obehag)',
				2 => 'Kännbar (kortare sjukfrånvaro, måttliga besvär)',
				3 => 'Allvarlig (längre sjukskrivning, allvarliga besvär)',
				4 => 'Mycket allvarlig (livslång påverkan, dödsfall)'
			);
			$this->data['risksFields'] = array(
				'risk' => array(
					'type' => 'input',
					'name' => 'riskName[]',
					'class' => 'required riskName',
					'placeholder' => 'Risk...',
					'title' => 'Risk...'
				),
				'occurrence' => array(
					'type' => 'select',
					'name' => 'riskOccurrence[]',
					'class' => 'required riskOccurrence',
					'values' => $riskOccurrenceValues
				),
				'severity' => array(
					'type' => 'select',
					'name' => 'riskSeverity[]',
					'class' => 'required riskSeverity',
					'values' => $riskSeverityValues
				),
				'explanation' => array(
					'prepend' => '<p>',
					'append' => '</p>',
					'type' => 'textarea',
					'name' => 'riskExplanation[]',
					'class' => 'required',
					'placeholder' => 'Förklarande text...'
				),
				'measure' => array(
					'type' => 'textarea',
					'name' => 'riskMeasure[]',
					'class' => 'riskMeasure',
					'placeholder' => 'Åtgärd',
					'title' => 'Åtgärd'
				),
			);
			
			if( $this->auth_kiv )
			{
				$this->data['risksFields'] = array_merge($this->data['risksFields'], array(
					'responsible' => array(
						'type' => 'users',
						'name' => 'riskResponsible[]',
						'class' => 'riskResponsible',
						'values' => $this->users,
						'title' => 'Ansvarig'
					),
				));
			}
			$this->data['risksFields'] = array_merge($this->data['risksFields'], array(
				'done' => array(
					'type' => 'input',
					'name' => 'riskDone[]',
					'class' => 'riskDone datepicker',
					'placeholder' => 'Skall vara klart',
					'title' => 'Skall vara klart'
				),
				'acceptable' => array(
					'title' => 'Är risken acceptabel i förhållande till nyttan?',
					'type' => 'checkbox',
					'name' => 'riskAcceptable[]',
					'class' => 'riskAcceptable',
					'values' => 1
				),
				'finished' => array(
					'type' => 'input',
					'name' => 'riskFinished[]',
					'class' => 'riskFinished datepicker',
					'placeholder' => 'Utfört',
					'title' => 'Utfört'
				)
			));
			
			$this->load->view('general/riskassessments/create',$this->data);
		}
	}
	
	public function edit( $ra_id = NULL )
	{
		$this->data['rights'] = $this->acl['deviation'];
		if( empty($this->data['rights']['update']) ) { show_404(); }
		
		$this->VALID_UUIDv4($ra_id);
		
		$getRisk = $this->riskassessmentslib->getRisk($ra_id);
		if( empty($getRisk) OR $getRisk->company_id !== $this->auth_company_id )
			show_404();
		
		if( $this->auth_kiv )
		{
			$getRiskDepartment = $riskDepartmentRights = $this->riskassessmentslib->getRiskDepartment($ra_id);
			if( empty($riskDepartmentRights) )
				$riskDepartmentRights = $this->data['rights']['update'];
		}
		
		$getRisks = $this->riskassessmentslib->getRisks($ra_id);
		
		$this->data['getRisk']  = $getRisk;
		$this->data['getRisks'] = $getRisks;
		
		if( $this->input->method(TRUE) === 'POST' )
		{
			// All general forms
			$answers = array();
			foreach($_POST as $key => $val) {
				if( $key != 'submit' && 
					!preg_match('/department/',$key) && 
					!preg_match('/risk/',$key)
				) {
					$answers[0][$key] = $val;
				}
			}
			
			// Risk department
			if( $this->auth_kiv )
			{
				$de_id = (isset($_POST['department']))?$_POST['department']:'';
				if($de_id != '') {
					$department = array();
					foreach($de_id AS $key => $val) {
						$department[] = array(
							'ra_id' => UUID_TO_BIN($ra_id),
							'de_id' => UUID_TO_BIN($val)
						);
					}
				}
			}
			
			// Risks
			if(isset($_POST['riskName'])) {
				$risks = array();
				$i = 0;
				foreach($_POST['riskName'] as $id => $risk) {
					$risk_id = UUIDv4();
					$risks[] = array(
						'id'			=> UUID_TO_BIN($risk_id),
						'ra_id'			=> UUID_TO_BIN($ra_id),
						'risk'			=> $risk,
						'occurrence'	=> $_POST['riskOccurrence'][$i],
						'severity'		=> $_POST['riskSeverity'][$i],
						'explanation'	=> $_POST['riskExplanation'][$i],
						'acceptable'	=> isset($_POST['riskAcceptable'][$i]) ? $_POST['riskAcceptable'][$i] : 0,
						'measure'		=> $_POST['riskMeasure'][$i],
						'responsible'	=> isset($_POST['riskResponsible'][$i]) ? UUID_TO_BIN($_POST['riskResponsible'][$i]) : NULL,
						'done'			=> isset($_POST['riskDone'][$i]) && !empty($_POST['riskDone'][$i]) ? $_POST['riskDone'][$i] : date('Y-m-d'),
						'finished'		=> isset($_POST['riskFinished'][$i]) && !empty($_POST['riskFinished'][$i]) ? $_POST['riskFinished'][$i] : date('Y-m-d')
					);
					++$i;
				}
			}
			
			// Risks update
			if(isset($_POST['riskNameEdit'])) {
				$risksEdit = array();
				$i = 0;
				foreach($_POST['riskNameEdit'] as $id => $risk) {
					$risksEdit[$_POST['riskIdEdit'][$i]] = array(
						'risk'			=> $risk,
						'occurrence'	=> $_POST['riskOccurrenceEdit'][$i],
						'severity'		=> $_POST['riskSeverityEdit'][$i],
						'explanation'	=> $_POST['riskExplanationEdit'][$i],
						'acceptable'	=> isset($_POST['riskAcceptableEdit'][$i]) ? $_POST['riskAcceptableEdit'][$i] : 0,
						'measure'		=> $_POST['riskMeasureEdit'][$i],
						'responsible'	=> isset($_POST['riskResponsibleEdit'][$i]) ? UUID_TO_BIN($_POST['riskResponsibleEdit'][$i]) : NULL,
						'done'			=> isset($_POST['riskDoneEdit'][$i]) && !empty($_POST['riskDoneEdit'][$i]) ? $_POST['riskDoneEdit'][$i] : date('Y-m-d'),
						'finished'		=> isset($_POST['riskFinishedEdit'][$i]) && !empty($_POST['riskFinishedEdit'][$i]) ? $_POST['riskFinishedEdit'][$i] : date('Y-m-d')
					);
					++$i;
				}
			}
			
			$this->riskassessmentslib->updateRisk($answers[0],UUID_TO_BIN($ra_id));
			
			if( $this->auth_kiv )
			{
				if( $de_id !== $getRiskDepartment ) {
					$this->riskassessmentslib->removeDepartment($ra_id);
					if( ! empty($department) )
					{
						$this->riskassessmentslib->insertRiskDepartment($department);
					}
				}
			}
			
			if(!empty($risks))
				$this->riskassessmentslib->insertRisks($risks);
			
			if(!empty($risksEdit))
				$this->riskassessmentslib->updateRisks($risksEdit);
			
			redirect('riskassessments/view/' . $ra_id); exit;
		}
		else
		{
			$this->data['fields'] = array(
				array(
					'id' => 'name',
					'input' => 'input',
					'required' => 1,
					'title' => 'Rubrik',
					'description' => 'Ange kortfattat vad som rapporteras.',
					'value' => $getRisk->name,
					'values' => null
				),
				array(
					'id' => 'description',
					'input' => 'text',
					'required' => 1,
					'title' => 'Ärendebeskrivning',
					'description' => 'Beskriv riskbedömningen.',
					'value' => $getRisk->description,
					'values' => null
				),
				array(
					'id' => 'date',
					'input' => 'date',
					'required' => 0,
					'title' => 'Datum',
					'description' => 'När skapades riskbedömningen?',
					'value' => $getRisk->date?$getRisk->date:date('Y-m-d'),
					'values' => null
				),
			);
			
			if( $this->auth_kiv )
			{
				$this->data['groups'] = $this->group_model->get_all($this->groups['types']['uuid']['department'], 'department');
				$this->data['fields'] = array_merge($this->data['fields'], array(
					array(
						'id' => 'us_id',
						'input' => 'users',
						'required' => 0,
						'title' => 'Användare',
						'description' => 'Vem är ansvarig för riskbedömningen?',
						'value' => $getRisk->us_id,
						'values' => $this->users
					),
					array(
						'id' => 'department',
						'input' => 'department',
						'required' => 0,
						'title' => 'Enhet',
						'description' => 'Ange de(n) enhet(er) som riskbedömningen berör.',
						'value' => $getRiskDepartment,
						'values' => $this->data['groups']['department']
					)
				));
			}

			$riskOccurrenceValues = array(
				0 => '-- Frekvens --',
				1 => 'Låg (Har inte hänt i Sverige)',
				2 => 'Måttlig (Har hänt någon gång i Sverige)',
				3 => 'Hög (Händer någon gång per år I verksamheten)',
				4 => 'Mycket hög (Händer flera gånger om året i verksamheten)'
			);
			$riskSeverityValues = array(
				0 => '-- Allvarlighetsgrad --',
				1 => 'Lindrig (övergående obehag)',
				2 => 'Kännbar (kortare sjukfrånvaro, måttliga besvär)',
				3 => 'Allvarlig (längre sjukskrivning, allvarliga besvär)',
				4 => 'Mycket allvarlig (livslång påverkan, dödsfall)'
			);
			$this->data['risksFields'] = array(
				'risk' => array(
					'type' => 'input',
					'name' => 'riskName[]',
					'class' => 'required riskName',
					'placeholder' => 'Risk...',
					'title' => 'Risk...'
				),
				'occurrence' => array(
					'type' => 'select',
					'name' => 'riskOccurrence[]',
					'class' => 'required riskOccurrence',
					'values' => $riskOccurrenceValues
				),
				'severity' => array(
					'type' => 'select',
					'name' => 'riskSeverity[]',
					'class' => 'required riskSeverity',
					'values' => $riskSeverityValues
				),
				'explanation' => array(
					'prepend' => '<p>',
					'append' => '</p>',
					'type' => 'textarea',
					'name' => 'riskExplanation[]',
					'class' => 'required',
					'placeholder' => 'Förklarande text...'
				),
				'measure' => array(
					'type' => 'textarea',
					'name' => 'riskMeasure[]',
					'class' => 'riskMeasure',
					'placeholder' => 'Åtgärd',
					'title' => 'Åtgärd'
				),
			);
			
			if( $this->auth_kiv )
			{
				$this->data['risksFields'] = array_merge($this->data['risksFields'], array(
					'responsible' => array(
						'type' => 'users',
						'name' => 'riskResponsible[]',
						'class' => 'riskResponsible',
						'values' => $this->users,
						'title' => 'Ansvarig'
					),
				));
			}
			$this->data['risksFields'] = array_merge($this->data['risksFields'], array(
				'done' => array(
					'type' => 'input',
					'name' => 'riskDone[]',
					'class' => 'riskDone datepicker',
					'placeholder' => 'Skall vara klart',
					'title' => 'Skall vara klart'
				),
				'acceptable' => array(
					'title' => 'Är risken acceptabel i förhållande till nyttan?',
					'type' => 'checkbox',
					'name' => 'riskAcceptable[]',
					'class' => 'riskAcceptable',
					'values' => 1
				),
				'finished' => array(
					'type' => 'input',
					'name' => 'riskFinished[]',
					'class' => 'riskFinished datepicker',
					'placeholder' => 'Utfört',
					'title' => 'Utfört'
				)
			));
			
			$this->load->view('general/riskassessments/update',$this->data);
		}
	}
	
	public function view( $ra_id = NULL )
	{
		$this->VALID_UUIDv4($ra_id);
		$this->data['ra_id'] = $ra_id;
		
		$this->data['rights'] = $this->acl['deviation'];
		if( empty($this->data['rights']['create']) ) { show_404(); }
		
		$getRisk = $this->riskassessmentslib->getRisk($ra_id);
		if( empty($getRisk) OR $getRisk->company_id !== $this->auth_company_id )
			show_404();
		$getRiskDepartment = $riskDepartmentRights = $this->riskassessmentslib->getRiskDepartment($ra_id);
		$getRisks = $this->riskassessmentslib->getRisks($ra_id);
		
		if( empty($riskDepartmentRights) )
			$riskDepartmentRights = $this->data['rights']['create'];
		
		$getRiskAssessmentsDeviationMap     = $this->riskassessmentslib->getRiskAssessmentsDeviationMap($ra_id);
		$getRiskAssessmentsEventAnalysisMap = $this->riskassessmentslib->getRiskAssessmentsEventAnalysisMap($ra_id);
		$getDeviationNames                  = $this->riskassessmentslib->getDeviationNames($riskDepartmentRights,$getRiskAssessmentsDeviationMap,$this->auth_company_id);
		$getEventAnalysisNames              = $this->riskassessmentslib->getEventAnalysisNames($this->auth_company_id);
		
		$this->data['getRisk']                            = $getRisk;
		$this->data['getRisks']                           = $getRisks;
		$this->data['getRiskAssessmentsDeviationMap']     = $getRiskAssessmentsDeviationMap;
		$this->data['getDeviationNames']                  = $getDeviationNames;
		$this->data['getRiskAssessmentsEventAnalysisMap'] = $getRiskAssessmentsEventAnalysisMap;
		$this->data['getEventAnalysisNames']              = $getEventAnalysisNames;
		
		$this->data['fields'] = array(
			array(
				'id' => 'name',
				'input' => 'input',
				'required' => 1,
				'title' => 'Rubrik',
				'description' => 'Ange kortfattat vad som rapporteras.',
				'value' => $getRisk->name,
				'values' => NULL,
			),
			array(
				'id' => 'description',
				'input' => 'text',
				'required' => 1,
				'title' => 'Ärendebeskrivning',
				'description' => 'Beskriv riskbedömningen.',
				'value' => $getRisk->description,
				'values' => NULL,
			),
			array(
				'id' => 'date',
				'input' => 'date',
				'required' => 0,
				'title' => 'Datum',
				'description' => 'När skapades riskbedömningen?',
				'value' => $getRisk->date?$getRisk->date:date('Y-m-d'),
				'values' => NULL,
			),
		);
		
		if( $this->auth_kiv )
		{
			$this->data['groups'] = $this->group_model->get_all($this->groups['types']['uuid']['department'], 'department');
			$this->data['fields'] = array_merge($this->data['fields'], array(
				array(
					'id' => 'us_id',
					'input' => 'users',
					'required' => 0,
					'title' => 'Användare',
					'description' => 'Vem är ansvarig för riskbedömningen?',
					'value' => $getRisk->us_id,
					'values' => $this->users
				),
				array(
					'id' => 'department',
					'input' => 'department',
					'required' => 0,
					'title' => 'Enhet',
					'description' => 'Ange de(n) enhet(er) som riskbedömningen berör.',
					'value' => $getRiskDepartment,
					'values' => $this->data['groups']['department']
				)
			));
		}

		$this->data['riskOccurrenceValues'] = array(
			0 => '-- Frekvens --',
			1 => 'Låg (Har inte hänt i Sverige)',
			2 => 'Måttlig (Har hänt någon gång i Sverige)',
			3 => 'Hög (Händer någon gång per år I verksamheten)',
			4 => 'Mycket hög (Händer flera gånger om året i verksamheten)'
		);
		$this->data['riskSeverityValues'] = array(
			0 => '-- Allvarlighetsgrad --',
			1 => 'Lindrig (övergående obehag)',
			2 => 'Kännbar (kortare sjukfrånvaro, måttliga besvär)',
			3 => 'Allvarlig (längre sjukskrivning, allvarliga besvär)',
			4 => 'Mycket allvarlig (livslång påverkan, dödsfall)'
		);
		
		$this->data['valueToText'] = array(
			0 => array('',''),
			1 => array('',''),
			2 => array('Låg','riskok'),
			3 => array('Låg','riskok'),
			4 => array('Måttlig','riskwarning'),
			5 => array('Måttlig','riskwarning'),
			6 => array('Allvarligt','riskdanger'),
			7 => array('Mycket allvarligt','riskmeltdown'),
			8 => array('Mycket allvarligt','riskmeltdown')
		);
		
		$this->load->view('general/riskassessments/view',$this->data);
	}
	
	public function connect( $ra_id = NULL )
	{
		$this->VALID_UUIDv4($ra_id);
		
		$this->data['rights'] = $this->acl['deviation'];
		if( empty($this->data['rights']['update']) ) { show_404(); }
		
		$getRisk = $this->riskassessmentslib->getRisk($ra_id);
		if( empty($getRisk) OR $getRisk->company_id !== $this->auth_company_id )
			show_404();
		$getRiskDepartment = $this->riskassessmentslib->getRiskDepartment($ra_id);
		
		if( empty($getRiskDepartment) )
			$getRiskDepartment = $this->data['rights']['update'];
		
		if( $this->input->method(TRUE) === 'POST' )
		{
			$deviation = array();
			if(isset($_POST['deviation'])) {
				foreach($_POST['deviation'] AS $value) {
					$deviation[] = array(
						'a_id'			=> UUID_TO_BIN($value),
						'ra_id'			=> UUID_TO_BIN($ra_id)
					);
				}
			}
			
			// Spara
			$this->riskassessmentslib->insertRiskAssessmentsDeviationMap($deviation,$ra_id);
			
			$eventanalysis = array();
			if(isset($_POST['eventanalysis'])) {
				foreach($_POST['eventanalysis'] AS $value) {
					$eventanalysis[] = array(
						'ra_id'			=> UUID_TO_BIN($ra_id),
						'ea_id'			=> UUID_TO_BIN($value)
					);
				}
			}
			
			// Spara
			$this->riskassessmentslib->insertRiskAssessmentsEventAnalysisMap($eventanalysis,$ra_id);
			
			redirect('riskassessments/view/' . $ra_id); exit;
		}
		
		$getRiskAssessmentsDeviationMap     = $this->riskassessmentslib->getRiskAssessmentsDeviationMap($ra_id);
		$getRiskAssessmentsEventAnalysisMap = $this->riskassessmentslib->getRiskAssessmentsEventAnalysisMap($ra_id);
		$getDeviationNames                  = $this->riskassessmentslib->getDeviationNames($getRiskDepartment,$getRiskAssessmentsDeviationMap,$this->auth_company_id);
		$getEventAnalysisNames              = $this->riskassessmentslib->getEventAnalysisNames($this->auth_company_id);
		
		$this->data['getRisk']                            = $getRisk;
		$this->data['getRiskAssessmentsDeviationMap']     = $getRiskAssessmentsDeviationMap;
		$this->data['getRiskAssessmentsEventAnalysisMap'] = $getRiskAssessmentsEventAnalysisMap;
		$this->data['getDeviationNames']                  = $getDeviationNames;
		$this->data['getEventAnalysisNames']              = $getEventAnalysisNames;
		
		$this->data['deviationFields'] = array(
			'deviation' => array(
				'type' => 'select-multiple',
				'id' => 'deviation',
				'name' => 'deviation[]',
				'class' => 'required',
				'values' => array('' => 'Välj avvikelse'),
				'placeholder' => 'Välj avvikelse',
				'prepend' => '<label>Anslut avvikelse</label>'
			),
			'eventanalysis' => array(
				'type' => 'select-multiple',
				'id' => 'eventanalysis',
				'name' => 'eventanalysis[]',
				'class' => 'required',
				'values' => array('' => 'Välj händelseanalys'),
				'placeholder' => 'Välj händelseanalys',
				'prepend' => '<label>Anslut händelseanalyser</label>'
			)
		);
		
		$this->data['deviationFields']['deviation']['values']            += $this->data['getDeviationNames'];
		$this->data['deviationFields']['deviation']['multiple-value']     = $this->data['getRiskAssessmentsDeviationMap'];
		$this->data['deviationFields']['eventanalysis']['values']        += $this->data['getEventAnalysisNames'];
		$this->data['deviationFields']['eventanalysis']['multiple-value'] = $this->data['getRiskAssessmentsEventAnalysisMap'];
		
		$this->load->view('general/riskassessments/connect',$this->data);
	}
	
	public function display()
	{
		$this->data['rights'] = $this->acl['deviation'];
		$this->data['risks'] = $this->riskassessmentslib->getAll($this->auth_company_id);
		$this->load->view('general/riskassessments/display',$this->data);
	}
}