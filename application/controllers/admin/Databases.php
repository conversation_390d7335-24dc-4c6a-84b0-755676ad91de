<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Databases extends Admin_Controller
{
	public function __construct()
	{
		parent::__construct();
		$this->data['body_class'] .= 'skin-white fixed';
		$this->load->model('database_model');
	}
	
    public function index()
	{
		$this->view();
    }
	
	public function view( $limit = NULL, $offset = NULL )
	{
		$this->data['databases'] = $this->database_model->get_all($limit,$offset);
		$this->load->view('admin/databases/view',$this->data);
	}
	
    public function create()
	{
		$this->data['database'] = NULL;
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$validation_rules = $this->_get_rules();

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			$database_id = UUIDv4();
			
			if ( $this->database_model->create( $database_id ) === TRUE )
			{
				redirect('admin/databases'); exit;
			}
		}
		else
		{
			$this->load->view('admin/databases/create',$this->data);
		}
	}
	
    public function update( $database_id )
	{
		$this->VALID_UUIDv4($database_id);
		
		$this->data['database'] = $this->database_model->get($database_id);
		if( empty($this->data['database']) ) { show_404(); }
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$validation_rules = $this->_get_rules();
		
		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			if ( $this->database_model->update( $this->data['database'] ) === TRUE )
			{
				redirect('admin/databases'); exit;
			}
		}
		else
		{
			$this->load->view('admin/databases/update',$this->data);
		}
	}
	
	public function _is_unique()
	{
		return $this->database_model->is_unique( $this->data['database'] );
	}
	
	private function _get_rules()
	{
		return array(
			array(
				'field' => 'databases_name',
				'label' => lang('databases_name'),
				'rules' => array(
					'trim',
					'required',
					'min_length[3]',
					'max_length[25]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']',
					array('is_unique', array( $this, '_is_unique' ) )
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'databases_default',
				'label' => lang('databases_default'),
				'rules' => array(
					'trim',
					'required',
					'in_list[0,1]'
				)
			)
		);
	}
}