<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Tasks extends Admin_Controller
{

	public function __construct()
	{
		parent::__construct();
		$this->data['body_class'] .= 'skin-white fixed';
    $this->load->model(['task_model']);
	}

	public function types()
	{
    $this->data['type'] = 'type';
    $this->data['list'] = $this->task_model->get_task_type_data();
    $this->load->view('admin/tasks/list', $this->data);
	}

  public function metric_types()
	{
    $this->data['type'] = 'metric_type';
    $this->data['list'] = $this->task_model->get_task_metric_type_data();
    $this->load->view('admin/tasks/list', $this->data);
	}

  public function add($type)
	{
    $this->data['type'] = $type;
    $this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');

		$validation_rules = [
			[
				'field' => 'name',
				'label' => lang(lang('tasks_' . $type)),
				'rules' => [
					'required'
				]
			],
		];

		$this->form_validation->set_rules($validation_rules);
    if( $this->form_validation->run() === TRUE ) {
      if ($type == 'metric_type') {
        $this->task_model->add_task_metric_type($this->input->post('name'));
        redirect('admin/tasks/metric_types');
      }
      else {
        $this->task_model->add_task_type($this->input->post('name'));
        redirect('admin/tasks/types');
      }
    }
    else {
      $this->load->view('admin/tasks/add_edit_record', $this->data);
    }
	}

  public function edit($type, $id)
	{
    $this->data['type'] = $type;
    if ($type == 'metric_type') {
      $metric_types = $this->task_model->get_task_metric_type_data();
      $this->data['name'] = $metric_types[$id];
    } else {
      $types = $this->task_model->get_task_type_data();
      $this->data['name'] = $types[$id];
    }
    $this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');

		$validation_rules = [
			[
				'field' => 'name',
				'label' => lang('tasks_name'),
				'rules' => [
					'required'
				]
			],
		];

		$this->form_validation->set_rules($validation_rules);
    if( $this->form_validation->run() === TRUE ) {
      if ($type == 'metric_type') {
        $this->task_model->edit_task_metric_type($id, $this->input->post('name'));
        redirect('admin/tasks/metric_types');
      }
      else {
        $this->task_model->edit_task_type($id, $this->input->post('name'));
        redirect('admin/tasks/types');
      }
    }
    else {
      $this->load->view('admin/tasks/add_edit_record', $this->data);
    }
	}

  public function delete($type, $id)
	{
    $this->data['type'] = $type;
    $this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');

		$validation_rules = [
			[
				'field' => 'confirm_delete',
				'label' => lang('confirm_delete'),
				'rules' => [
					'required'
				]
			],
		];

		$this->form_validation->set_rules($validation_rules);
    if( $this->form_validation->run() === TRUE ) {
      if ($type == 'metric_type') {
        $this->task_model->delete_task_metric_type($id);
        redirect('admin/tasks/metric_types');
      } else {
        $this->task_model->delete_task_type($id);
        redirect('admin/tasks/types');
      }      
    }
    else {
      $this->load->view('admin/tasks/delete_record', $this->data);
    }   
	}
}