<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Deviation extends Admin_Controller
{
	private function _get_inputs()
	{
		$this->data['inputs'] = [
			'input'			=> 'Inmatningsfält',
			'text'			=> 'Textfält',
			'text_wysiwyg'	=> 'Textfält (ordbehandlare)',
			'dropdown'		=> 'Värdelista',
			'date'			=> 'Da<PERSON><PERSON><PERSON><PERSON>jare (ÅÅÅÅ-MM-DD)',
			'radio'		    => 'Checkbox (Ja/Nej)',
			'users'			=> 'Användarlista',
			//'upload'		=> 'Filuppladdning',
			//'email'		=> 'Kontaktuppgifter'
		];
	}

	private function _get_no_yes()
	{
		$this->data['no_yes'] = [
			1 => 'Nej',
			2 => 'Ja'
		];
	}

	public function __construct()
	{
		parent::__construct();
		$this->data['body_class'] .= 'skin-white fixed';
		$this->load->model('deviation_model');
	}

	public function fields($type = 'display', $field_id = NULL, $type_id = NULL)
	{
		in_array($type,[
			'display', // OK
			'add', // OK
			'edit', // OK
			'delete', // OK
			'email', // OK
			'change_email', // OK
			'dropdown', // OK
			'add_dropdown', // OK
			'sort_dropdown', // OK
			'edit_dropdown', // OK
			'delete_dropdown', // OK
			'sort',
		],TRUE) OR show_404();

		$this->VALID_UUIDv4($field_id, FALSE);
		$this->VALID_UUIDv4($type_id, FALSE);

		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');

		$callback = 'field_' . $type;
		$this->{$callback}($field_id,$type_id);
	}

	private function field_display($field_id,$department_id)
	{
		$this->_get_inputs();
		$this->_get_no_yes();
		$this->data['inputs']['upload']        = 'Filuppladdning';
		$this->data['inputs']['email']         = 'Kontaktuppgifter';
		$this->data['inputs']['department']    = 'Enhet';
		$this->data['inputs']['eventanalysis'] = 'Händelseanalys';

		$this->data['fields'] = $this->deviation_model->get_fields();
		// var_dump($this->data['fields']);
		$this->load->view('admin/deviation/fields_display', $this->data);
	}
	
	private function field_add($field_id,$department_id)
	{
		$this->_get_inputs();
		$this->_get_no_yes();

		$config = array(
			array(
				'field' => 'deviation_field_title',
				'label' => lang('deviation_field_title'),
				'rules' => array(
					'trim',
					'required',
					'min_length[3]',
					'max_length[128]', // 255
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']',
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
		);

		$this->form_validation->set_rules( $config );
		if( $this->form_validation->run() === TRUE )
		{
			$field_id = $this->deviation_model->add_field();
			if( $field_id !== FALSE && $this->input->post('deviation_field_input') === 'radio')
			{
				$this->deviation_model->add_dropdown($field_id, 'Nej', 1);
				$this->deviation_model->add_dropdown($field_id, 'Ja');
			}
			redirect('admin/deviation/fields'); exit;
		}
		else
		{
			$this->load->view('admin/deviation/fields_add', $this->data);
		}

	}

	private function field_edit($field_id,$department_id)
	{
		$this->_get_inputs();
		$this->_get_no_yes();

		$this->data['field'] = $this->deviation_model->get_field($field_id);

		$config = array(
			array(
				'field' => 'deviation_field_title',
				'label' => lang('deviation_field_title'),
				'rules' => array(
					'trim',
					'required',
					'min_length[3]',
					'max_length[128]', // 255
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']',
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
		);

		$this->form_validation->set_rules( $config );
		if( $this->form_validation->run() === TRUE )
		{
			$this->deviation_model->edit_field($this->data['field']);
			redirect('admin/deviation/fields'); exit;
		}
		else
		{
			$this->data['answers'] = $this->deviation_model->get_field_answer($field_id);
			$this->load->view('admin/deviation/fields_edit', $this->data);
		}
	}

	private function field_delete($field_id,$department_id)
	{
		$validation_rules = $this->_get_rules_delete();
		
		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			$this->deviation_model->delete_field($field_id);
			
			redirect('admin/deviation/fields'); exit;
		}
		else
		{
			$this->data['field'] = $this->deviation_model->get_field($field_id);
			$this->load->view('admin/deviation/fields_delete',$this->data);
		}
	}

	private function field_email($field_id,$department_id)
	{
		$this->data['inputs']['email'] = 'Kontaktuppgifter';
		$this->load->model('group_model');
		$this->data['groups'] = $this->group_model->get_all([],'department');
		$this->data['field'] = $this->deviation_model->get_field($field_id);
		$this->load->view('admin/deviation/fields_email',$this->data);
	}

	private function field_change_email($field_id,$department_id)
	{
		$this->data['inputs']['email'] = 'Kontaktuppgifter';
		$this->load->model(['group_model','user_model']);
		$this->data['users']['available'] = $this->user_model->get_users_by_all_groups([$department_id]);

		$config = array(
			array(
				'field' => 'contacts[]',
				'label' => lang('deviation_field_input'),
				'rules' => array(
					'trim',
					'in_list['.implode(',', array_keys($this->data['users']['available'])).']'
				)
			),
		);

		$this->form_validation->set_rules( $config );
		if( $this->form_validation->run() === TRUE )
		{
			$this->deviation_model->change_email($field_id, $department_id);
			redirect('admin/deviation/fields'); exit;
		}
		else
		{
			$this->data['group'] = $this->group_model->get($department_id);
			$this->data['field'] = $this->deviation_model->get_field($field_id);
			$this->data['contacts'] = $this->deviation_model->get_email_default($field_id,$department_id);
			$this->load->view('admin/deviation/fields_change_email',$this->data);
		}
	}

	private function field_dropdown($field_id, $type_id)
	{
		$this->data['dropdown'] = $this->deviation_model->getDropdown($field_id);
		if( empty($this->data['dropdown']) )
		{
			redirect('admin/deviation/fields/add_dropdown/' . $field_id); exit;
		}
		$this->data['field']   = $this->deviation_model->get_field($field_id);
		$this->data['answers'] = $this->deviation_model->get_field_answers($field_id);
		$this->data['inputs']['dropdown'] = 'Värdelista';
		$this->data['uuid_kvalprak'] = UUIDv4();
		$this->load->view('admin/deviation/fields_dropdown', $this->data);
	}

	private function field_add_dropdown($field_id, $type_id)
	{
		$config = array(
			array(
				'field' => 'deviation_field_title',
				'label' => lang('deviation_field_title'),
				'rules' => array(
					'trim',
					'required',
					'min_length[2]',
					'max_length[128]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']',
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
		);

		$this->form_validation->set_rules( $config );
		if( $this->form_validation->run() === TRUE )
		{
			$this->deviation_model->add_dropdown($field_id);
			redirect('admin/deviation/fields/dropdown/' . $field_id); exit;
		}
		else
		{
			$this->data['inputs']['dropdown'] = 'Värdelista';
			$this->data['field'] = $this->deviation_model->get_field($field_id);
			$this->load->view('admin/deviation/fields_add_dropdown', $this->data);
		}
	}
	// @STEP2: Flash message
	private function field_sort_dropdown($field_id, $type_id)
	{
		$uuid_kvalprak = $this->input->post('uuid_kvalprak');
		$sort_order    = $this->input->post('sort_order');
		$sort_order    = ! empty($sort_order) ? explode(',',$sort_order) : NULL;
		if( ! is_array($sort_order) OR empty($sort_order) ) { show_404(); }

		$new_dropdown = FALSE;

		// New value added
		if( ! empty($uuid_kvalprak) && in_array($uuid_kvalprak,$sort_order) )
		{
			$new_dropdown = TRUE;
			$this->deviation_model->add_empty_dropdown($field_id,$uuid_kvalprak);
		}

		// Sort
		$this->deviation_model->sort_dropdown($sort_order);

		if( $new_dropdown )
		{
			redirect('admin/deviation/fields/edit_dropdown/' . $field_id . '/' . $uuid_kvalprak); exit;
		}
		else
		{
			redirect('admin/deviation/fields/dropdown/' . $field_id); exit;
		}
	}

	private function field_edit_dropdown($field_id, $type_id)
	{
		$this->data['dropdown'] = $this->deviation_model->get_dropdown($type_id);

		$config = array(
			array(
				'field' => 'deviation_field_title',
				'label' => lang('deviation_field_title'),
				'rules' => array(
					'trim',
					'required',
					'min_length[2]',
					'max_length[128]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']',
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
		);

		$this->form_validation->set_rules( $config );
		if( $this->form_validation->run() === TRUE )
		{
			$this->deviation_model->edit_dropdown($this->data['dropdown']);
			redirect('admin/deviation/fields/dropdown/' . $field_id); exit;
		}
		else
		{
			$this->data['inputs']['dropdown'] = 'Värdelista';
			$this->data['field'] = $this->deviation_model->get_field($field_id);
			$this->load->view('admin/deviation/fields_edit_dropdown', $this->data);
		}
	}

	private function field_delete_dropdown($field_id, $type_id)
	{
		$validation_rules = $this->_get_rules_delete();
		
		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			$this->deviation_model->delete_dropdown($type_id);
			
			redirect('admin/deviation/fields/dropdown/' . $field_id); exit;
		}
		else
		{
			$this->data['field'] = $this->deviation_model->get_field($field_id);
			$this->data['dropdown'] = $this->deviation_model->get_dropdown($type_id);
			$this->load->view('admin/deviation/fields_delete_dropdown',$this->data);
		}
	}

	private function field_sort($field_id,$type_id)
	{
		$required_kvalprak = 0;
		$maxUploadPerPage  = 0;
		$maxEmailPerPage   = 0;

		if( $this->input->method(TRUE) === 'POST' )
		{
			$fieldAvailable = $this->input->post('fieldAvailable');
			$fieldAvailable = ! empty($fieldAvailable) ? explode(',',$fieldAvailable) : [];
			$fieldOne       = $this->input->post('fieldOne');
			$fieldOne       = ! empty($fieldOne) ? explode(',',$fieldOne) : [];
			$fieldTwo       = $this->input->post('fieldTwo');
			$fieldTwo       = ! empty($fieldTwo) ? explode(',',$fieldTwo) : [];
			$fieldThree     = $this->input->post('fieldThree');
			$fieldThree     = ! empty($fieldThree) ? explode(',',$fieldThree) : [];

			$res = $this->deviation_model->sort_fields(
				[
					$fieldAvailable,
					$fieldOne,
					$fieldTwo,
					$fieldThree,
				],
				$this->deviation_model->get_fields()
			);
		}

		$this->_get_inputs();
		$this->_get_no_yes();
		$this->data['inputs']['upload']        = 'Filuppladdning';
		$this->data['inputs']['email']         = 'Kontaktuppgifter';
		$this->data['inputs']['department']    = 'Enhet';
		$this->data['inputs']['eventanalysis'] = 'Händelseanalys';
		$this->data['fields'] = $this->deviation_model->getDeviationFieldsByPage([0,1,2,3],TRUE);
		// var_dump($this->data['fields']);exit;
		$this->data['newDeviation'] = $this->deviation_model->newDeviation();
		$this->load->view('admin/deviation/fields_sort',$this->data);
	}

	private function _get_rules_delete()
	{
		return array(
			array(
				'field' => 'confirm_delete',
				'label' => lang('confirm_delete'),
				'rules' => array(
					'required'
				)
			),
		);
	}
}