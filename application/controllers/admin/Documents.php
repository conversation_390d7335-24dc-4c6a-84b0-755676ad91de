<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Documents extends Admin_Controller
{
	public function __construct()
	{
		parent::__construct();
		$this->data['body_class'] .= 'skin-white fixed';
		$this->load->model(['document_model','folder_model']);
	}
	
    public function index()
	{
		$this->view();
	}

	public function get_kvo()
	{
		// The URL of the external endpoint returning the .docx file
		$externalUrl = 'https://orna-analys.kvalprak.se/admin/getDocumentOrna?companyName='.$this->config->item('program_name').'&type=kvo';
		// $externalUrl = 'http://localhost:3000/admin/getDocumentOrna?companyName=Skaraborgshalsan&type=kvo';

		$ch = curl_init($externalUrl);
		curl_setopt($ch, CURLOPT_HTTPHEADER, [
			'X-API-KEY: !0PKMt9b95?lY&i7Hhx`Z{k1pnr@g',
		]);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

		// Execute the cURL request
		$response = curl_exec($ch);

		// Close cURL
		curl_close($ch);

		// Set headers for downloading the file
		header('Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document');
		header('Content-Disposition: attachment; filename="report.docx"');
		echo $response;
	}

	public function get_report()
	{
		// The URL of the external endpoint returning the .docx file
		$externalUrl = 'https://orna-analys.kvalprak.se/admin/getReportOrna?companyName='.$this->config->item('program_name').'&type=kvo';
		// $externalUrl = 'http://localhost:3000/admin/getReportOrna?companyName=Skaraborgshalsan&type=kvo';


		$ch = curl_init($externalUrl);
		curl_setopt($ch, CURLOPT_HTTPHEADER, [
			'X-API-KEY: !0PKMt9b95?lY&i7Hhx`Z{k1pnr@g',
		]);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

		// Execute the cURL request
		$response = curl_exec($ch);

		// Close cURL
		curl_close($ch);

		// Set headers for downloading the file
		header('Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document');
		header('Content-Disposition: attachment; filename="report.docx"');
		echo $response;
	}

	public function orna_analys_auto() {
		$this->load->helper('form');
		$this->data['success'] = false;
		$this->data["limit"] = false;
		if( $this->input->method(TRUE) === 'POST' )
		{
			$this->load->model('document_model');
			$this->document_model->send_to_orna_analys('', $this->config->item('program_name'), $this->acl['deviation']['read']);
			$this->data['success'] = true;
		}
		$this->load->view('admin/documents/orna_analys_auto', $this->data);
	}

	public function orna_analys()
	{
		$this->load->helper('form');
		$this->data['success'] = false;
		$this->data["limit"] = false;
		if( $this->input->method(TRUE) === 'POST' )
		{
			$this->load->model('document_model');
			$orna_analysis_path = CI_UPLOAD_PATH . 'orna-analys.json';
			$array = [$this->input->post('email')];
			if(file_exists($orna_analysis_path))
			{
				if(($json = file_get_contents($orna_analysis_path)) !== FALSE)
				{
					if(($array = json_decode($json, true)) !== NULL) {
						if (!in_array($this->input->post('email'), $array)) 
						{
							if (count($array) >= 4 ){
								$this->data["limit"] = true;
								$this->load->view('admin/documents/orna_analys', $this->data);
								return;
							}
							$array[] = $this->input->post('email');
						}
					}
				}
			}
			if(($json = json_encode($array)) !== FALSE)
			{
				file_put_contents($orna_analysis_path, $json);
			}
			
			$this->document_model->send_to_orna_analys($this->input->post('email'), $this->config->item('program_name'), $this->acl['deviation']['read']);
			$this->data['success'] = true;
		} 
		$this->load->view('admin/documents/orna_analys', $this->data);
	}
	
	public function view()
	{
		$this->data['move']['menu'] = $this->menu_model->get_all();
		$this->data['move']['folder'] = $this->folder_model->get_all($this->data['move']['menu']['id'], TRUE);
		$this->data['move']['document'] = $this->document_model->get_all_documents_in_folder(array_keys($this->data['move']['folder']['id']));
		if( empty($this->data['move']['document']) ) { redirect('admin'); exit; }
		
		$keep = [];
		foreach($this->data['move']['folder']['id'] AS $folder_id => $menu_id)
		{
			if(
				in_array($folder_id, $this->data['move']['document']['folders']) && 
				isset($this->data['move']['menu']['parent'][$menu_id]) )
			{
				$parent_id = $this->data['move']['menu']['parent'][$menu_id];
				if( isset($this->data['move']['menu'][$parent_id]) )
				{
					$keep[] = $menu_id;
					$keep[] = $parent_id;
				}
				
				if(isset($this->data['move']['menu']['parent'][$parent_id]))
				{
					$top_menu = $this->data['move']['menu']['parent'][$parent_id];
					if( isset($this->data['move']['menu'][$top_menu]) )
					{
						$keep[] = $top_menu;
					}
				}
			}
		}
		
		$this->data['move']['menu']['loop'] = $keep;
		$this->load->view('admin/documents/view',$this->data);
	}
	
	public function categories($type = 'display', $id = NULL)
	{
		in_array($type,[
			'display',
			'create',
			'update',
			'delete',
		],TRUE) OR show_404();

		$this->VALID_UUIDv4($id, FALSE);

		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');

		$callback = 'categories_' . $type;
		$this->{$callback}($id);
	}
	
	public function categories_display($id)
	{
		$this->data['categories'] = $this->document_model->get_document_category();
		$this->data['categories_in_use'] = $this->document_model->get_document_category_in_use();
		$this->load->view('admin/documents/display_categories',$this->data);
	}
	
	public function categories_create($id)
	{
		$validation_rules = $this->_get_rules_categories();
		
		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			if( $this->document_model->create_categories() === TRUE )
			{
				redirect('admin/documents/categories'); exit;
			}
			
			// @STEP2: Give error
			redirect('admin/documents/categories'); exit;
		}
		else
		{
			$this->load->view('admin/documents/create_categories',$this->data);
		}
	}
	
	public function categories_update($id)
	{
		$this->data['category'] = $this->document_model->get_document_category($id);
		if( empty($this->data['category']) ) { show_404(); }
		
		$validation_rules = $this->_get_rules_categories();
		
		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			if( $this->document_model->update_categories($id) === TRUE )
			{
				redirect('admin/documents/categories'); exit;
			}
			
			// @STEP2: Give error
			redirect('admin/documents/categories'); exit;
		}
		else
		{
			$this->load->view('admin/documents/update_categories',$this->data);
		}
	}
	
	public function categories_delete($id)
	{
		$this->data['category'] = $this->document_model->get_document_category($id);
		if( empty($this->data['category']) ) { show_404(); }
		
		$validation_rules = $this->_get_rules_delete();
		
		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			if ( $this->document_model->delete_categories( $id ) === TRUE )
			{
				redirect('admin/documents/categories'); exit;
			}
			
			// @STEP2: Give error
			redirect('admin/documents/categories'); exit;
		}
		else
		{
			$this->load->view('admin/documents/delete_categories',$this->data);
		}
	}
	
	public function owner()
	{
		$owners = $this->document_model->get_all_by_owner(array_keys($this->users_all));
		if( ! empty($owners) )
			$owners = array_keys($owners);
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$validation_rules = $this->_get_rules_owner();

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			$this->document_model->change_owner();
			redirect('admin/documents/owner'); exit;
		}
		else
		{
			$from = [];
			$to   = [];
			if( ! empty($owners) )
			{
				foreach($this->users_all as $user_id => $user)
				{
					if( in_array($user_id,$owners) )
					{
						$from[$user_id] = $this->users_all[$user_id]->name;
					}
				}
				foreach($this->users as $user_id => $user)
				{
					$to[$user_id] = $this->users[$user_id]->name;
				}
			}
			$this->data['from'] = $from;
			$this->data['to'] = $to;
			// var_dump($this->users,$from,$to);
			$this->load->view('admin/documents/owner',$this->data);
		}
	}
	
	public function owner_individual()
	{
		$this->data['move']['menu'] = $this->menu_model->get_all();
		$this->data['move']['folder'] = $this->folder_model->get_all($this->data['move']['menu']['id'], TRUE);
		$this->data['move']['document'] = $this->document_model->get_all_documents_in_folder(array_keys($this->data['move']['folder']['id']));
		if( empty($this->data['move']['document']) ) { redirect('admin'); exit; }
		
		$keep = [];
		foreach($this->data['move']['folder']['id'] AS $folder_id => $menu_id)
		{
			if(
				in_array($folder_id, $this->data['move']['document']['folders']) && 
				isset($this->data['move']['menu']['parent'][$menu_id]) )
			{
				$parent_id = $this->data['move']['menu']['parent'][$menu_id];
				if( isset($this->data['move']['menu'][$parent_id]) )
				{
					$keep[] = $menu_id;
					$keep[] = $parent_id;
				}
				
				if(isset($this->data['move']['menu']['parent'][$parent_id]))
				{
					$top_menu = $this->data['move']['menu']['parent'][$parent_id];
					if( isset($this->data['move']['menu'][$top_menu]) )
					{
						$keep[] = $top_menu;
					}
				}
			}
		}
		
		$this->data['move']['menu']['loop'] = $keep;
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$validation_rules = $this->_get_rules_owner_individual();

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			if( $this->document_model->change_owner_individual() )
			{
				
			}
			
			redirect('admin/documents/owner_individual'); exit;
		}
		else
		{
			foreach($this->users as $user_id => $user)
			{
				$to[$user_id] = $this->users[$user_id]->name;
			}
			$this->data['to'] = $to;
			$this->load->view('admin/documents/owner_individual',$this->data);
		}
	}
	
	public function move()
	{
		$this->data['move']['menu'] = $this->menu_model->get_all();
		$this->data['move']['folder'] = $this->folder_model->get_all($this->data['move']['menu']['id'], TRUE);
		$this->data['move']['document'] = $this->document_model->get_all_documents_in_folder(array_keys($this->data['move']['folder']['id']));
		if( empty($this->data['move']['document']) ) { redirect('admin'); exit; }
		
		$keep = [];
		foreach($this->data['move']['folder']['id'] AS $folder_id => $menu_id)
		{
			if(
				in_array($folder_id, $this->data['move']['document']['folders']) && 
				isset($this->data['move']['menu']['parent'][$menu_id]) )
			{
				$parent_id = $this->data['move']['menu']['parent'][$menu_id];
				if( isset($this->data['move']['menu'][$parent_id]) )
				{
					$keep[] = $menu_id;
					$keep[] = $parent_id;
				}
				
				if(isset($this->data['move']['menu']['parent'][$parent_id]))
				{
					$top_menu = $this->data['move']['menu']['parent'][$parent_id];
					if( isset($this->data['move']['menu'][$top_menu]) )
					{
						$keep[] = $top_menu;
					}
				}
			}
		}
		
		$this->data['move']['menu']['loop'] = $keep;
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$validation_rules = $this->_get_rules_move();

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			if ( $this->input->post('copy_only'))
				$this->document_model->copy_multiple();
			else 
				$this->document_model->move_multiple();
			
			redirect('admin/documents/move'); exit;
		}
		else
		{
			$this->load->view('admin/documents/move',$this->data);
		}
	}
	
	private function _get_rules_categories()
	{
		return array(
			array(
				'field' => 'documents_categories_name',
				'label' => lang('documents_categories_name'),
				'rules' => array(
					'trim',
					'required',
					'min_length[3]',
					'max_length[100]', // 255
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']',
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
		);
	}
	
	private function _get_rules_delete()
	{
		return array(
			array(
				'field' => 'confirm_delete',
				'label' => lang('confirm_delete'),
				'rules' => array(
					'required'
				)
			),
		);
	}
	
	private function _get_rules_owner()
	{
		return array(
			array(
				'field' => 'documents_owner_from',
				'label' => lang('documents_owner'),
				'rules' => array(
					'required',
					'in_list['.implode(',',array_keys($this->users_all)).']'
				)
			),
			array(
				'field' => 'documents_owner_to',
				'label' => lang('documents_owner'),
				'rules' => array(
					'required',
					'in_list['.implode(',',array_keys($this->users)).']'
				)
			),
			array(
				'field' => 'confirm_change',
				'label' => lang('confirm_change'),
				'rules' => array(
					'required'
				)
			),
		);
	}
	
	private function _get_rules_owner_individual()
	{
		return array(
			array(
				'field' => 'documents_owner_to',
				'label' => lang('documents_owner'),
				'rules' => array(
					'required',
					'in_list['.implode(',',array_keys($this->users)).']'
				)
			),
			array(
				'field' => 'tree_documents[]',
				'label' => lang('documents_document'),
				'rules' => array(
					'trim',
					'required',
					'in_list['.implode(',',$this->data['move']['document']['version']).']'
				)
			),
			array(
				'field' => 'confirm_change',
				'label' => lang('confirm_change'),
				'rules' => array(
					'required'
				)
			),
		);
	}
	
	private function _get_rules_move()
	{
		return array(
			array(
				'field' => 'tree_documents[]',
				'label' => lang('documents_document'),
				'rules' => array(
					'trim',
					'required',
					'in_list['.implode(',',$this->data['move']['document']['version']).']'
				)
			),
			array(
				'field' => 'move_document',
				'label' => lang('folder_folder'),
				'rules' => array(
					'trim',
					'required',
					'in_list['.implode(',',array_keys($this->data['move']['folder']['id'])).']'
				),
			),
			array(
				'field' => 'confirm_change',
				'label' => lang('confirm_change'),
				'rules' => array(
					'required'
				)
			),
		);
	}
}
