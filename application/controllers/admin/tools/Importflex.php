<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Importflex extends Admin_Controller
{
	public function customers() {
		$specialities = [
			1 => [
				'speciality_id' => UUID_TO_BIN(UUIDv4()),
				'name' => 'Akutmedicin'
			],
			2 => [
				'speciality_id' => UUID_TO_BIN(UUIDv4()),
				'name' => 'Allergologi'
			],
			3 => [
				'speciality_id' => UUID_TO_BIN(UUIDv4()),
				'name' => 'Allmänmedicin'
			],
			4 => [
				'speciality_id' => UUID_TO_BIN(UUIDv4()),
				'name' => 'Anestesiologi'
			],
			5 => [
				'speciality_id' => UUID_TO_BIN(UUIDv4()),
				'name' => 'Barnmedicin'
			],
			6 => [
				'speciality_id' => UUID_TO_BIN(UUIDv4()),
				'name' => 'Barnpsykiatri'
			],
			7 => [
				'speciality_id' => UUID_TO_BIN(UUIDv4()),
				'name' => 'Dermatologi'
			],
			8 => [
				'speciality_id' => UUID_TO_BIN(UUIDv4()),
				'name' => 'Fysiologi'
			],
			9 => [
				'speciality_id' => UUID_TO_BIN(UUIDv4()),
				'name' => 'Gynekologi'
			],
			10 => [
				'speciality_id' => UUID_TO_BIN(UUIDv4()),
				'name' => 'Invärtes medicin'
			],
			11 => [
				'speciality_id' => UUID_TO_BIN(UUIDv4()),
				'name' => 'Kardiologi'
			],
			12 => [
				'speciality_id' => UUID_TO_BIN(UUIDv4()),
				'name' => 'Kirurgi'
			],
			13 => [
				'speciality_id' => UUID_TO_BIN(UUIDv4()),
				'name' => 'Lungmedicin'
			],
			14 => [
				'speciality_id' => UUID_TO_BIN(UUIDv4()),
				'name' => 'Neurologi'
			],
			15 => [
				'speciality_id' => UUID_TO_BIN(UUIDv4()),
				'name' => 'Ortopedi'
			],
			16 => [
				'speciality_id' => UUID_TO_BIN(UUIDv4()),
				'name' => 'Psykiatri'
			],
			17 => [
				'speciality_id' => UUID_TO_BIN(UUIDv4()),
				'name' => 'Psykoterapi'
			],
			18 => [
				'speciality_id' => UUID_TO_BIN(UUIDv4()),
				'name' => 'Radiologi'
			],
			19 => [
				'speciality_id' => UUID_TO_BIN(UUIDv4()),
				'name' => 'Reumatologi'
			],
			20 => [
				'speciality_id' => UUID_TO_BIN(UUIDv4()),
				'name' => 'Urologi'
			],
			21 => [
				'speciality_id' => UUID_TO_BIN(UUIDv4()),
				'name' => 'Ögonsjukdomar'
			],
			22 => [
				'speciality_id' => UUID_TO_BIN(UUIDv4()),
				'name' => 'Öron, näsa, hals'
			],
			23 => [
				'speciality_id' => UUID_TO_BIN(UUIDv4()),
				'name' => 'Annan'
			],
		];
		
		$q = $this->db->query('SELECT * FROM kvalpraklakare.customers');
		$customers = [];
		$insert_customers = [];
		$users = [];
		$groups = [];
		$user_group = [];
		$acl_users_and_groups = [];
		$menus = [];
		$menu_group = [];
		$folders = [];
		$date = date('Y:m:i H:i:s');
		if( $q->num_rows() !== 0 )
		{
			$kvalprak_flex_uuid = 'bdfc18eb-215b-423d-8693-e4c4e6a34c45';
			$kvalprak_manuell_uuid = 'de8ee482-655c-4055-818f-2ab54309fdc2';
			$kvalprak_web_uuid = '67f7ada9-67c3-4813-81cc-d6da20aed18a';

			foreach($q->result() as $field)
			{
				$customers[$field->cid] = $field;
				$membership_id = ($field->type == 2 OR $field->type == 3) ? $kvalprak_flex_uuid : $kvalprak_manuell_uuid;
				$membership_id = UUID_TO_BIN($membership_id);
				$company_id = UUID_TO_BIN(UUIDv4());
				$insert_customers[] = [
					'company_id' => $company_id,
					 // FLEX / FLEX Manuell
					'membership_id' => $membership_id,
					'database_id' => UUID_TO_BIN('790ce472-3547-4be3-bc34-310070599e90'),
					'speciality_id' => $specialities[$field->sid]['speciality_id'],
					'active' => 1,
					'name' => $field->first_name . ' ' . $field->last_name,
					'alias' => $field->company,
					'address' => $field->address,
					'address2' => $field->address2,
					'zip' => $field->zip,
					'city' => $field->city,
					'phone' => $field->phone,
					'email' => $field->email,
					'fax' => $field->fax,
					'type' => $field->type,
				];
				
				$pwd = strtolower(substr($field->first_name,0,1).substr($field->last_name,0,1)).$field->cid;
				$user_id = UUID_TO_BIN(UUIDv4());
				
				$users[] = [
					'user_id' => $user_id,
					'company_id' => $company_id,
					'username' => NULL,
					// 'email' => $field->email ? $field->email : NULL,
					'email' => NULL,
					'flex' => 1,
					'auth_level' => 0,
					'banned' => '0',
					'name' => $field->first_name . ' ' . $field->last_name,
					'cid' => $field->cid,
					'passwd' => $this->hash_password($pwd),
					'created_at' => $date
				];
				
				// Groups
				$position = UUID_TO_BIN(UUIDv4());
				$department = UUID_TO_BIN(UUIDv4());
				
				$groups[] = [
					'company_id' => $company_id,
					'group_id'   => $position,
					'type'       => 'position',
					'name'       => 'Befattning'
				];
				
				$groups[] = [
					'company_id' => $company_id,
					'group_id'   => $department,
					'type'       => 'department',
					'name'       => 'Avdelning'
				];
				
				$user_group[] = [
					'user_id'  => $user_id,
					'group_id' => $position
				];
				
				$user_group[] = [
					'user_id'  => $user_id,
					'group_id' => $department
				];
				
				// ACL
				$acl_users_and_groups[] = [
					'user_id'   => $user_id,
					'group_id'  => $position,
					'action_id' => UUID_TO_BIN('b7562f85-38ac-400d-98ac-7d82d3d9ff6c') // Read
				];
				
				$acl_users_and_groups[] = [
					'user_id'   => $user_id,
					'group_id'  => $position,
					'action_id' => UUID_TO_BIN('1886185d-cac6-4ca8-9d94-e8d507355207') // Create
				];
				
				$acl_users_and_groups[] = [
					'user_id'   => $user_id,
					'group_id'  => $position,
					'action_id' => UUID_TO_BIN('b116acf0-d699-4d61-a28d-d9a315967361') // Update
				];
				
				$acl_users_and_groups[] = [
					'user_id'   => $user_id,
					'group_id'  => $department,
					'action_id' => UUID_TO_BIN('b7562f85-38ac-400d-98ac-7d82d3d9ff6c') // Read
				];
				
				$acl_users_and_groups[] = [
					'user_id'   => $user_id,
					'group_id'  => $department,
					'action_id' => UUID_TO_BIN('7cab2d38-c4a8-4dfd-8b8d-bce7782d0088') // create
				];
				
				$acl_users_and_groups[] = [
					'user_id'   => $user_id,
					'group_id'  => $department,
					'action_id' => UUID_TO_BIN('5e7db833-7f91-4ace-ba05-566c0f3510ef') // deviation_two
				];
				
				$acl_users_and_groups[] = [
					'user_id'   => $user_id,
					'group_id'  => $department,
					'action_id' => UUID_TO_BIN('8d40873f-4750-4839-b784-53ebf22245ad') // deviation_three
				];
				
				$acl_users_and_groups[] = [
					'user_id'   => $user_id,
					'group_id'  => $department,
					'action_id' => UUID_TO_BIN('69cf90fd-c8ea-4eb8-bc7e-b4f00649345d') // read
				];
				
				$acl_users_and_groups[] = [
					'user_id'   => $user_id,
					'group_id'  => $department,
					'action_id' => UUID_TO_BIN('a850de4f-4bcd-4d5c-a457-e317041dbf62') // update
				];
				
				$menu_id = UUID_TO_BIN(UUIDv4());
				
				$menus[] = [
					'company_id' => $company_id,
					'menu_id' => $menu_id,
					'parent_id' => NULL,
					'program_id' => NULL,
					'owner' => $user_id,
					'name' => 'Dokument',
					'description' => '',
					'type' => 1,
					'sticky' => 0,
					'href' => NULL
				];
				
				$child_menu_id = UUID_TO_BIN(UUIDv4());
				
				$menus[] = [
					'company_id' => $company_id,
					'menu_id' => $child_menu_id,
					'parent_id' => $menu_id,
					'program_id' => NULL,
					'owner' => $user_id,
					'name' => 'Dokument',
					'description' => '',
					'type' => 0,
					'sticky' => 0,
					'href' => NULL
				];
				
				$menu_group[] = [
					'menu_id'  => $menu_id,
					'group_id' => $position
				];
				
				$menu_group[] = [
					'menu_id'  => $child_menu_id,
					'group_id' => $position
				];
				
				$menu_group[] = [
					'menu_id'  => $menu_id,
					'group_id' => $department
				];
				
				$menu_group[] = [
					'menu_id'  => $child_menu_id,
					'group_id' => $department
				];
				
				$folders[] = [
					'folder_id' => UUID_TO_BIN(UUIDv4()),
					'menu_id'   => $child_menu_id,
					'name'      => 'Affärsdokument'
				];
				
				$folders[] = [
					'folder_id' => UUID_TO_BIN(UUIDv4()),
					'menu_id'   => $child_menu_id,
					'name'      => 'Ekonomi/Administration'
				];
				
				$folders[] = [
					'folder_id' => UUID_TO_BIN(UUIDv4()),
					'menu_id'   => $child_menu_id,
					'name'      => 'IT'
				];
				
				$folders[] = [
					'folder_id' => UUID_TO_BIN(UUIDv4()),
					'menu_id'   => $child_menu_id,
					'name'      => 'Korrespondens'
				];
				
				$folders[] = [
					'folder_id' => UUID_TO_BIN(UUIDv4()),
					'menu_id'   => $child_menu_id,
					'name'      => 'Lagar/regler'
				];
				
				$folders[] = [
					'folder_id' => UUID_TO_BIN(UUIDv4()),
					'menu_id'   => $child_menu_id,
					'name'      => 'Marknad/försäljning'
				];
				
				$folders[] = [
					'folder_id' => UUID_TO_BIN(UUIDv4()),
					'menu_id'   => $child_menu_id,
					'name'      => 'Miljö'
				];
				
				$folders[] = [
					'folder_id' => UUID_TO_BIN(UUIDv4()),
					'menu_id'   => $child_menu_id,
					'name'      => 'Organisation'
				];
				
				$folders[] = [
					'folder_id' => UUID_TO_BIN(UUIDv4()),
					'menu_id'   => $child_menu_id,
					'name'      => 'Patient'
				];
				
				$folders[] = [
					'folder_id' => UUID_TO_BIN(UUIDv4()),
					'menu_id'   => $child_menu_id,
					'name'      => 'Personal'
				];
				
				$folders[] = [
					'folder_id' => UUID_TO_BIN(UUIDv4()),
					'menu_id'   => $child_menu_id,
					'name'      => 'Rutiner/policys'
				];
				
				$folders[] = [
					'folder_id' => UUID_TO_BIN(UUIDv4()),
					'menu_id'   => $child_menu_id,
					'name'      => 'Teknik'
				];
				
				$folders[] = [
					'folder_id' => UUID_TO_BIN(UUIDv4()),
					'menu_id'   => $child_menu_id,
					'name'      => 'Övrigt'
				];
				
			}
		}
		
		// echo $this->hash_password('test');
		
		$this->db->insert_batch('specialities', $specialities);
		$this->db->insert_batch('companies', $insert_customers);
		$this->db->insert_batch('groups', $groups);
		$this->db->insert_batch('users', $users);
		$this->db->insert_batch('user_group', $user_group);
		$this->db->insert_batch('acl_users_and_groups', $acl_users_and_groups);
		$this->db->insert_batch('menus', $menus);
		$this->db->insert_batch('menu_group', $menu_group);
		$this->db->insert_batch('folders', $folders);
		
		// var_dump($insert_customers,$users);
		// var_dump($users);
		echo 'Done!';
	}
	
	public function hash_password( $password ) {
		return password_hash(
			base64_encode(
				hash('sha384', $password, true)
			),
			PASSWORD_DEFAULT
		);
	}
	
	public function answers() {
		$q = $this->db->query("SELECT a.*,u.company_id FROM kvalpraklakare.answers AS a INNER JOIN `kiv-8-dev`.users AS u ON a.cid=u.cid");
		$old_answers = [];
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $field)
			{
				if( strlen($field->value) >= 1)
				{
					$old_answers[BIN_TO_UUID($field->company_id)][$field->qid] = [
						'fiscal' => $field->fiscal,
						'value'  => $field->value
					];
				}
			}
		}
		
		$q = $this->db->query("SELECT * FROM form_page_questions WHERE field NOT IN ('heading','table')");
		$answers = [];
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $field)
			{
				if( $field->field === 'checkbox')
				{
					$answers[$field->field][] = BIN_TO_UUID($field->question_id);
				}
				else
				{
					$answers['old'][$field->field][$field->question_id_old] = BIN_TO_UUID($field->question_id);
					// $answers['new'][$field->field][BIN_TO_UUID($field->question_id)] = $field->question_id_old;
				}
				$answers['page'][BIN_TO_UUID($field->question_id)] = BIN_TO_UUID($field->page_id);
			}
		}
		
		$q = $this->db->query("SELECT * FROM form_page_question_options");
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $field)
			{
				$answers['options'][BIN_TO_UUID($field->question_id)][$field->option_id_old] = BIN_TO_UUID($field->option_id);
			}
		}
		
		$insert = [];
		$insert_year = [];
		foreach($old_answers as $company_id => $qid)
		{
			foreach($answers['old']['input'] as $old_qid => $question_id)
			{
				if( isset($qid[$old_qid]) ) {
					$insert[] = [
						'company_id'  => UUID_TO_BIN($company_id),
						'page_id'     => UUID_TO_BIN($answers['page'][$question_id]),
						'question_id' => UUID_TO_BIN($question_id),
						// 'fiscal'      => $qid[$old_qid]['fiscal'],
						'answer'      => $qid[$old_qid]['value'],
					];
					$insert_year[$qid[$old_qid]['fiscal']][] = [
						'company_id'  => UUID_TO_BIN($company_id),
						'page_id'     => UUID_TO_BIN($answers['page'][$question_id]),
						'question_id' => UUID_TO_BIN($question_id),
						'answer'      => $qid[$old_qid]['value'],
					];
				}
			}
			
			foreach($answers['old']['radio'] as $old_qid => $question_id)
			{
				if( isset($qid[$old_qid]) && isset($answers['options'][$question_id]) )
				{
					if( isset($answers['options'][$question_id][$qid[$old_qid]['value']]) )
					{
						$insert[] = [
							'company_id'  => UUID_TO_BIN($company_id),
							'page_id'     => UUID_TO_BIN($answers['page'][$question_id]),
							'question_id' => UUID_TO_BIN($question_id),
							// 'fiscal'      => $qid[$old_qid]['fiscal'],
							'answer'      => $answers['options'][$question_id][$qid[$old_qid]['value']],
						];
						$insert_year[$qid[$old_qid]['fiscal']][] = [
							'company_id'  => UUID_TO_BIN($company_id),
							'page_id'     => UUID_TO_BIN($answers['page'][$question_id]),
							'question_id' => UUID_TO_BIN($question_id),
							'answer'      => $answers['options'][$question_id][$qid[$old_qid]['value']],
						];
					}
				}
			}
			
			foreach($answers['old']['dropdown'] as $old_qid => $question_id)
			{
				if( isset($qid[$old_qid]) && isset($answers['options'][$question_id]) )
				{
					if( isset($answers['options'][$question_id][$qid[$old_qid]['value']]) )
					{
						$insert[] = [
							'company_id'  => UUID_TO_BIN($company_id),
							'page_id'     => UUID_TO_BIN($answers['page'][$question_id]),
							'question_id' => UUID_TO_BIN($question_id),
							// 'fiscal'      => $qid[$old_qid]['fiscal'],
							'answer'      => $answers['options'][$question_id][$qid[$old_qid]['value']],
						];
						$insert_year[$qid[$old_qid]['fiscal']][] = [
							'company_id'  => UUID_TO_BIN($company_id),
							'page_id'     => UUID_TO_BIN($answers['page'][$question_id]),
							'question_id' => UUID_TO_BIN($question_id),
							'answer'      => $answers['options'][$question_id][$qid[$old_qid]['value']],
						];
					}
				}
			}
			
			foreach($answers['checkbox'] as $question_id)
			{
				$fiscal = NULL;
				$checkbox = [];
				foreach($answers['options'][$question_id] as $old_qid => $option_id)
				{
					if( isset($qid[$old_qid]) )
					{
						$checkbox[] = $option_id;
						$fiscal = $qid[$old_qid]['fiscal'];
					}
				}
				
				if( ! empty($checkbox) )
				{
					$insert[] = [
						'company_id'  => UUID_TO_BIN($company_id),
						'page_id'     => UUID_TO_BIN($answers['page'][$question_id]),
						'question_id' => UUID_TO_BIN($question_id),
						// 'fiscal'      => $fiscal,
						'answer'      => json_encode($checkbox),
					];
					$insert_year[$fiscal][] = [
						'company_id'  => UUID_TO_BIN($company_id),
						'page_id'     => UUID_TO_BIN($answers['page'][$question_id]),
						'question_id' => UUID_TO_BIN($question_id),
						'answer'      => json_encode($checkbox),
					];
				}
			}
		}
		
		// var_dump($insert);exit;
		// var_dump($insert_year['2016']);exit;
		$this->db->insert_batch('form_survey_025b5eca49', $insert);
		// $this->db->insert_batch('form_survey_025b5eca49_2016', $insert_year['2016']);
		$this->db->insert_batch('form_survey_025b5eca49_2017', $insert_year['2017']);
		echo 'Import done!';
	}
}