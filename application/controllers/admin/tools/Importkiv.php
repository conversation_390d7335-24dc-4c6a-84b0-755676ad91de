<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Importkiv extends MY_Controller
{
	private $database = 'kiv7';
	private $admin = 0;
	private $management = 0;
	private $security_quality_filler;
	private $security_quality_validator;
	private $menu = [
		'read'   => 'b7562f85-38ac-400d-98ac-7d82d3d9ff6c',
		'create' => '1886185d-cac6-4ca8-9d94-e8d507355207',
		'update' => 'b116acf0-d699-4d61-a28d-d9a315967361',
		'folder' => '85d400f0-250b-49a4-9d9e-1b25294f1a87',
		'author' => '42a8878a-be36-4b3c-bf42-0d42a7bb8a34',
	];
	private $deviation = [
		'create'          => '7cab2d38-c4a8-4dfd-8b8d-bce7782d0088',
		'deviation_two'   => '5e7db833-7f91-4ace-ba05-566c0f3510ef',
		'deviation_three' => '8d40873f-4750-4839-b784-53ebf22245ad',
		'read'            => '69cf90fd-c8ea-4eb8-bc7e-b4f00649345d',
		'update'          => 'a850de4f-4bcd-4d5c-a457-e317041dbf62',
	];
	private $upgrade;
	
	private $membership_id = 'c45d3b30-f46b-441e-a0ae-65b8743a6be4';
	private $database_id = '790ce472-3547-4be3-bc34-310070599e90';
	
	private $null = '00000000-0000-0000-0000-000000000000';
	
	private $import_form = TRUE;
	private $form_id = 'd9f98481-8afe-46f9-9314-f703ae4ebfe7';
	private $survey_id = 'f703ae4ebf';
	
	private $yp = ['4','5','6','7','8','9','13','14','18','19','20','25','26','32'];
	
	// TRUE = Författare. FALSE = ACL.
	private $document_author = TRUE;

	private function deviation($deviation, $date, $regby, $users_map)
	{
		if( empty($deviation->{$date}) )
			return NULL;

		if( empty($deviation->{$regby}) OR ! isset($users_map[$deviation->{$regby}]) )
			return UUID_TO_BIN('00000000-0000-0000-0000-000000000000');

		return $users_map[$deviation->{$regby}];
	}
	
	public function document_clean($content)
	{
		return trim(
			preg_replace(
				"/\s+/",
				' ',
				preg_replace(
					"/\r|\n/",
					' ',
					strip_tags($content)
				)
			)
		);
	}
	
	// ONLY RUN THIS ONES
	public function forms()
	{
		$this->form_id = UUID_TO_BIN($this->form_id);
		
		$q = $this->db->query("SELECT form_id, CONCAT_WS('.',f1,f2,f3,f4) as f_nr, form_name, form_type FROM {$this->database}.forms WHERE form='qa' ORDER BY `f1` ASC, `f2` ASC, `f3` ASC, `f4` ASC");
		if( $q->num_rows() !== 0 )
		{
			$topicNew = FALSE;
			$parent_id = NULL;
			$pages_map = [];
			
			$this->upgrade['forms'] = [
				'company_id'  => UUID_TO_BIN('14e217a3-a860-43e0-9874-3d5131d60ed4'),
				'form_id'     => $this->form_id,
				'survey_id'   => $this->survey_id,
				'name'        => 'Kvalitetssäkring KIV',
				'description' => 'Välj perspektiv till vänster för att börja kvalitetssäkra ditt företag.',
				'type'        => 'qa_kiv',
				'global'      => 1,
			];

			foreach($q->result() as $f)
			{
				$page_id = UUID_TO_BIN(UUIDv4());
				$topicNew = $f->form_type == 'topic' ? TRUE : FALSE;
				
				if( $topicNew )
					$parent_id = $page_id;
				
				$pages_map[$f->form_id] = $page_id;
					
				$this->upgrade['form_pages'][] = [
					'form_id'     => $this->form_id,
					'page_id'     => $page_id,
					'parent_id'   => ! $topicNew ? $parent_id : NULL,
					'name'        => $f->f_nr . ' ' . $f->form_name,
					'description' => '',
					'type'        => $topicNew ? 0 : 1,
					'settings'    => NULL,
				];
			}
			
			$page_id = UUID_TO_BIN(UUIDv4());
			$question_id = UUID_TO_BIN(UUIDv4());
			
			$this->upgrade['form_pages'][] = [
				'form_id'     => $this->form_id,
				'page_id'     => $page_id,
				'parent_id'   => NULL,
				'name'        => '98. Skicka in',
				'description' => '',
				'type'        => 1,
				'settings'    => '{"formdone": true}',
			];
			
			$this->upgrade['form_page_questions'][] = [
				'page_id'         => $page_id,
				'question_id'     => $question_id,
				'question_id_old' => 0,
				'parent_id'       => NULL,
				'sort'            => 0,
				'name'            => 'Du har nu gått igenom hela kvalitetssäkringen',
				'description'     => 'Lämna in protokollet genom att trycka på knappen Lämna in.',
				'field'           => 'heading',
				'validate'        => NULL,
				'settings'        => NULL,
			];
			
			$page_id = UUID_TO_BIN(UUIDv4());
			$question_id = UUID_TO_BIN(UUIDv4());
			
			$this->upgrade['form_pages'][] = [
				'form_id'     => $this->form_id,
				'page_id'     => $page_id,
				'parent_id'   => NULL,
				'name'        => '99. Kvitto',
				'description' => '',
				'type'        => 1,
				'settings'    => '{"lastpage": true}',
			];
			
			$this->upgrade['form_page_questions'][] = [
				'page_id'         => $page_id,
				'question_id'     => $question_id,
				'question_id_old' => 0,
				'parent_id'       => NULL,
				'sort'            => 0,
				'name'            => 'Här kommer ditt kvitto på att du lämnat in kvalitetssäkringen!',
				'description'     => '',
				'field'           => 'heading',
				'validate'        => NULL,
				'settings'        => NULL,
			];
		}
		
		$q = $this->db->query("SELECT * FROM {$this->database}.questions WHERE form_id NOT IN ? ORDER BY `questions`.`form_id` ASC, `questions`.`sn` ASC",[
			$this->yp
		]);
		if( $q->num_rows() !== 0 )
		{
			$sort = 0;
			$form_id = NULL;
			$parent_id = NULL;
			$question_id_map = [];
			$question_radio = [];
			$question_select = [];
			foreach($q->result() as $f)
			{
				if( $form_id !== $f->form_id )
					$sort = 0;
					
				$form_id = $f->form_id;
				$question_id = UUID_TO_BIN(UUIDv4());
				$question_id_map[$f->question_id] = $question_id;
				
				if( ! $f->child )
					$parent_id = $question_id;
				
				if( $f->type === 'radio' )
					$question_radio[] = $f->question_id;
				
				if( $f->type === 'slide' )
					$f->type = 'input';
				
				if( $f->type === 'email' )
					$f->type = 'input';
				
				if( $f->type === 'text' )
					$f->type = 'heading';
				
				if( $f->type === 'select' )
				{
					$f->type = 'dropdown';
					$question_select[] = $f->question_id;
				}
				
				$this->upgrade['form_page_questions'][] = [
					'page_id'         => $pages_map[$f->form_id],
					'question_id'     => $question_id,
					'question_id_old' => $f->question_id,
					'parent_id'       => ( $f->child ) ? $parent_id : NULL,
					'sort'            => $sort,
					'name'            => $f->sn . ' ' . $f->question,
					'description'     => $f->description,
					'field'           => $f->type,
					'validate'        => NULL,
					'settings'        => NULL,
				];
				
				$sort += 1;
			}
		}
		
		if( ! empty($question_radio) )
		{
			foreach($question_radio AS $radio)
			{
				$yes = UUID_TO_BIN(UUIDv4());
				$no = UUID_TO_BIN(UUIDv4());
				
				$this->upgrade['form_page_question_options'][] = [
					'question_id'     => $question_id_map[$radio],
					'option_id'       => $yes,
					'option_id_old'   => 0,
					'sort'            => 0,
					'name'            => 'Ja',
					'default'         => 0,
				];
				
				$this->upgrade['form_page_question_options'][] = [
					'question_id'     => $question_id_map[$radio],
					'option_id'       => $no,
					'option_id_old'   => 0,
					'sort'            => 1,
					'name'            => 'Nej',
					'default'         => 0,
				];
			}
		}
		
		$q = $this->db->query("SELECT * FROM {$this->database}.select_options WHERE form_id NOT IN ? ORDER BY `question_id` ASC, `option_id` ASC",[
			$this->yp
		]);
		if( $q->num_rows() !== 0 )
		{
			$sort = 0;
			$question_id = NULL;
			foreach($q->result() as $f)
			{
				if( ! isset($question_id_map[$f->question_id]) )
					continue;
				
				if( $question_id !== $f->question_id )
					$sort = 0;
					
				$question_id = $f->question_id;
				
				$option_id = UUID_TO_BIN(UUIDv4());
				
				$this->upgrade['form_page_question_options'][] = [
					'question_id'     => $question_id_map[$f->question_id],
					'option_id'       => $option_id,
					'option_id_old'   => $f->option_id,
					'sort'            => $sort,
					'name'            => $f->option_name,
					'default'         => 0,
				];
				
				$sort += 1;
			}
		}
		
		// INSERT SO THAT YOU CAN USE THESE
		$this->db->insert('forms', $this->upgrade['forms']);
		$this->db->insert_batch('form_pages', $this->upgrade['form_pages']);
		$this->db->insert_batch('form_page_questions', $this->upgrade['form_page_questions']);
		$this->db->insert_batch('form_page_question_options', $this->upgrade['form_page_question_options']);
		
		$table = 'form_survey_'. $this->survey_id;
		$q = "CREATE TABLE IF NOT EXISTS `{$table}` (
			`company_id` binary(16) NOT NULL,
			`page_id` binary(16) NOT NULL,
			`question_id` binary(16) NOT NULL,
			`answer` text COLLATE utf8mb4_swedish_ci NOT NULL,
			PRIMARY KEY (`company_id`,`page_id`,`question_id`)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_swedish_ci;";
		$this->db->query($q);
		
		$q = "CREATE TABLE IF NOT EXISTS `{$table}_done` (
		  `company_id` binary(16) NOT NULL,
		  `fiscal` year(4) NOT NULL,
		  `done` datetime NOT NULL
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_swedish_ci;";
		$this->db->query($q);
		
		$q = "CREATE TABLE IF NOT EXISTS `{$table}_attachment` (
		  `question_id` binary(16) NOT NULL,
		  `attachment_id` binary(16) NOT NULL,
		  `document_id` binary(16) NULL,
		  `uploaded_on` datetime NOT NULL,
		  `file_name` varchar(255) COLLATE utf8mb4_swedish_ci NOT NULL,
		  `file_ext` varchar(16) COLLATE utf8mb4_swedish_ci NOT NULL,
		  PRIMARY KEY (`attachment_id`)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_swedish_ci;";
		$this->db->query($q);
	}
	
	public function index()
	{
		$answers_department = NULL;
		$q = $this->db->query("SELECT * FROM {$this->database}.answers GROUP BY `department`");
		if( $q->num_rows() !== 0 )
		{
			$answers_department = [];
			foreach($q->result() as $a)
			{
				if( $a->department == 0)
					continue;
				
				if( $a->department == '' )
					exit('Answers can\'t have an empty department.');
				
				$answers_department[] = $a->department;
			}
			
			if( count($answers_department) >= 2 )
			{
				// @TODO: 
				exit('Answers have been made by more than one department...');
			}
			
			$answers_department = array_shift($answers_department);
		}
		
		$company_id = UUID_TO_BIN(UUIDv4());
		
		$this->upgrade['companies'] = [
			'company_id'    => $company_id,
			'membership_id' => UUID_TO_BIN($this->membership_id),
			'database_id'   => UUID_TO_BIN($this->database_id),
			'name'          => '7 -> 8',
			'alias'         => '',
			'address'       => '',
			'zip'           => '',
			'city'          => ''
		];
		
		// @TABLE departments
		$q = $this->db->query("SELECT * FROM {$this->database}.departments");
		if( $q->num_rows() !== 0 )
		{
			$departments_map = [];
			// $this->data['departments'] = [];
			foreach($q->result() as $d)
			{
				$de_id = UUID_TO_BIN(UUIDv4());
				$departments_map[$d->de_id] = $de_id;
				
				$this->upgrade['groups'][] = [
					'company_id' => $company_id,
					'group_id'   => $de_id,
					'type'       => 'department',
					'name'       => $d->name
				];
				
			}
		}
		else
		{
			exit;
		}
		
		$department_count = count($departments_map);
		
		// @TABLE groups
		$q = $this->db->query("SELECT * FROM {$this->database}.groups");
		if( $q->num_rows() !== 0 )
		{
			$groups_map = [];
			// $groups = [];
			foreach($q->result() as $g)
			{
				$group_id = UUID_TO_BIN(UUIDv4());
				$groups_map[$g->gr_id] = $group_id;
				
				$this->upgrade['groups'][] = [
					'company_id' => $company_id,
					'group_id'   => $group_id,
					'type'       => 'position',
					'name'       => $g->title
				];
				
				if( $g->title === 'Systemadmin' )
				{
					$this->admin = $g->gr_id;
				}
				
				if( $g->title === 'Ledning' )
				{
					$this->management = $g->gr_id;
				}

				if( $g->title === 'Kvalitetssäkrare' )
				{
					$this->security_quality_filler = $g->gr_id;
				}

				if( $g->title === 'Revisor' )
				{
					$this->security_quality_validator = $g->gr_id;
				}
			}
		}
		else
		{
			exit;
		}
		
		$systemadmin_id = UUID_TO_BIN(UUIDv4());
		$this->upgrade['groups'][] = [
			'company_id' => $company_id,
			'group_id'   => $systemadmin_id,
			'type'       => 'security',
			'name'       => 'Systemadministratör'
		];

		$security_administrator_id = UUID_TO_BIN(UUIDv4());
		$this->upgrade['groups'][] = [
			'company_id' => $company_id,
			'group_id'   => $security_administrator_id,
			'type'       => 'security',
			'name'       => 'Administratör'
		];

		// Kvalitetssäkrare
		$security_quality_filler_id = UUID_TO_BIN(UUIDv4());
		$this->upgrade['groups'][] = [
			'company_id' => $company_id,
			'group_id'   => $security_quality_filler_id,
			'type'       => 'security',
			'name'       => 'Kvalitetssäkrare'
		];

		// Revisor
		$security_quality_validator_id = UUID_TO_BIN(UUIDv4());
		$this->upgrade['groups'][] = [
			'company_id' => $company_id,
			'group_id'   => $security_quality_validator_id,
			'type'       => 'security',
			'name'       => 'Kvalitetsvärderare'
		];
		
		// var_dump($groups_map,$groups);
		
		// @TABLE users
		$q = $this->db->query("SELECT * FROM {$this->database}.users ORDER BY us_id");
		if( $q->num_rows() !== 0 )
		{
			$deviation_users = [];
			$users_map = [];
			// $users = [];
			$created_at = date('Y-m-d H:i:s');
			foreach($q->result() as $u)
			{
				$user_id = UUID_TO_BIN(UUIDv4());
				$users_map[$u->us_id] = $user_id;
				
				$this->upgrade['users'][] = [
					'user_id'    => $user_id,
					'company_id' => $company_id,
					'username'   => NULL,
					'email'      => $u->email,
					'kiv'        => 1,
					'created_at' => $created_at,
					'name'       => ( empty($u->f_name) && empty($u->l_name) ) ?  $u->username : trim($u->f_name . ' ' . $u->l_name),
					'position'   => $u->position,
					'HSAID'      => $u->HSAID,
					'phone'      => $u->phone,
					'mobile'     => $u->mobile,
					'address'    => $u->street,
					'zip'        => $u->zip_code,
					'city'       => $u->city,
					'cid'        => 0,
					// 'passwd'     => '$2y$10$L7tTP1CJYJ4X4AQA75yTBeOgMG81To0ivaDM3pJg4yQGv1xmmms5C', // @TODO: Remove this
				];
				
				$deviation_users[$u->us_id] = [
					'department'      => $u->department,
					'deviation_two'   => $u->operations,
					'deviation_three' => ($u->operations OR $u->qualitygroup) ? 1 : 0,
					'read'            => $u->deviation_read,
					'update'          => $u->deviation_write,
				];
			}
		}
		else
		{
			exit;
		}
		
		// var_dump($users_map,$users,$deviation_users);
		
		// $user_group = [];
		// @TABLE department_permissions
		$q = $this->db->query("SELECT * FROM {$this->database}.department_permissions ORDER BY us_id");
		if( $q->num_rows() !== 0 )
		{
			$users_department = [];
			$deparments_in_use = [];
			foreach($q->result() as $dp)
			{
				if( isset($users_map[$dp->us_id]) && isset($departments_map[$dp->de_id]) )
				{
					$this->upgrade['user_group'][] = [
						'user_id'  => $users_map[$dp->us_id],
						'group_id' => $departments_map[$dp->de_id]
					];
					
					$users_department[$dp->us_id][] = $dp->de_id;
					$deparments_in_use[$dp->de_id] = $dp->de_id;
				}
			}
		}
		else
		{
			exit;
		}
		
		// var_dump($this->upgrade);exit;
		
		// deviation_acl
		if( ! empty($deviation_users) )
		{
			foreach($deviation_users as $us_id => $d)
			{
				$tmp_departments = NULL;
				// Prefered department set
				if( ! empty($d['department']) )
				{
					$tmp_departments = explode(',', $d['department']);
				}
				// No prefered department, all departments
				else if( ! empty($users_department[$us_id]) )
				{
					$tmp_departments = $users_department[$us_id];
				}
				
				if( ! empty($tmp_departments) )
				{
					foreach($tmp_departments as $department)
					{
						if( ! isset($departments_map[$department]) )
							continue;
						
						// menu.read
						$this->upgrade['acl'][] = [
							'user_id'   => $users_map[$us_id],
							'group_id'  => $departments_map[$department],
							'object_id' => NULL,
							'action_id' => UUID_TO_BIN($this->menu['read'])
						];
						
						// create
						$this->upgrade['acl'][] = [
							'user_id'   => $users_map[$us_id],
							'group_id'  => $departments_map[$department],
							'object_id' => NULL,
							'action_id' => UUID_TO_BIN($this->deviation['create'])
						];
						
						// deviation_two
						if( ! empty($d['deviation_two']) )
						{
							$this->upgrade['acl'][] = [
								'user_id'   => $users_map[$us_id],
								'group_id'  => $departments_map[$department],
								'object_id' => NULL,
								'action_id' => UUID_TO_BIN($this->deviation['deviation_two'])
							];
						}
						// deviation_three
						if( ! empty($d['deviation_three']) )
						{
							$this->upgrade['acl'][] = [
								'user_id'   => $users_map[$us_id],
								'group_id'  => $departments_map[$department],
								'object_id' => NULL,
								'action_id' => UUID_TO_BIN($this->deviation['deviation_three'])
							];
						}
						// read
						if( ! empty($d['read']) )
						{
							$this->upgrade['acl'][] = [
								'user_id'   => $users_map[$us_id],
								'group_id'  => $departments_map[$department],
								'object_id' => NULL,
								'action_id' => UUID_TO_BIN($this->deviation['read'])
							];
						}
						// update
						if( ! empty($d['update']) )
						{
							$this->upgrade['acl'][] = [
								'user_id'   => $users_map[$us_id],
								'group_id'  => $departments_map[$department],
								'object_id' => NULL,
								'action_id' => UUID_TO_BIN($this->deviation['update'])
							];
						}
					}
				}
			}
		}
		
		// var_dump($this->upgrade);exit;
		
		// @TABLE group_permissions
		$q = $this->db->query("SELECT * FROM {$this->database}.group_permissions ORDER BY us_id");
		if( $q->num_rows() !== 0 )
		{
			$users_groups = [];
			foreach($q->result() as $dp)
			{
				if( isset($users_map[$dp->us_id]) && isset($groups_map[$dp->gr_id]) )
				{
					$this->upgrade['user_group'][] = [
						'user_id'  => $users_map[$dp->us_id],
						'group_id' => $groups_map[$dp->gr_id]
					];
					
					$users_groups[$dp->us_id][] = $dp->gr_id;
					
					$this->upgrade['acl'][] = [
						'user_id'   => $users_map[$dp->us_id],
						'group_id'  => $groups_map[$dp->gr_id],
						'object_id' => NULL,
						'action_id' => UUID_TO_BIN($this->menu['read'])
					];
					
					if( $dp->gr_id == $this->admin )
					{
						$this->upgrade['user_group'][] = [
							'user_id'  => $users_map[$dp->us_id],
							'group_id' => $systemadmin_id
						];
						
						if( $this->document_author === FALSE )
						{
							$this->upgrade['acl'][] = [
								'user_id'   => $users_map[$dp->us_id],
								'group_id'  => $groups_map[$dp->gr_id],
								'object_id' => NULL,
								'action_id' => UUID_TO_BIN($this->menu['update'])
							];
							
							$this->upgrade['acl'][] = [
								'user_id'   => $users_map[$dp->us_id],
								'group_id'  => $groups_map[$dp->gr_id],
								'object_id' => NULL,
								'action_id' => UUID_TO_BIN($this->menu['folder'])
							];
						}
					}

					if( $dp->gr_id == $this->security_quality_filler )
					{
						$this->upgrade['user_group'][] = [
							'user_id'  => $users_map[$dp->us_id],
							'group_id' => $security_quality_filler_id
						];
					}

					if( $dp->gr_id == $this->security_quality_validator )
					{
						$this->upgrade['user_group'][] = [
							'user_id'  => $users_map[$dp->us_id],
							'group_id' => $security_quality_validator_id
						];
					}
				}
			}
		}
		else
		{
			exit;
		}
		
		// var_dump($this->upgrade);exit;
		
		// @TABLE group_permissions_write
		$q = $this->db->query("SELECT * FROM {$this->database}.group_permissions_write ORDER BY us_id");
		if( $q->num_rows() !== 0 )
		{
			if( ! isset($users_groups) )
			{
				$users_groups = [];
			}
			foreach($q->result() as $dp)
			{
				if( isset($users_map[$dp->us_id]) && isset($groups_map[$dp->gr_id]) )
				{
					if( isset($users_groups[$dp->us_id]) && ! in_array($dp->gr_id, $users_groups[$dp->us_id]) )
					{
						$this->upgrade['user_group'][] = [
							'user_id'  => $users_map[$dp->us_id],
							'group_id' => $groups_map[$dp->gr_id]
						];
						
						$users_groups[$dp->us_id][] = $dp->gr_id;
						
						$this->upgrade['acl'][] = [
							'user_id'   => $users_map[$dp->us_id],
							'group_id'  => $groups_map[$dp->gr_id],
							'object_id' => NULL,
							'action_id' => UUID_TO_BIN($this->menu['read'])
						];
					}
					
					if( $this->document_author === FALSE )
					{
						$this->upgrade['acl'][] = [
							'user_id'   => $users_map[$dp->us_id],
							'group_id'  => $groups_map[$dp->gr_id],
							'object_id' => NULL,
							'action_id' => UUID_TO_BIN($this->menu['create'])
						];						
					}
				}
			}
		}
		
		// var_dump($this->upgrade);exit;
		
		// @TABLE menus
		$q = $this->db->query("SELECT * FROM {$this->database}.menu ORDER BY pe_id");
		if( $q->num_rows() !== 0 )
		{
			$sticky = [
				0 => -1,
				1 => 0,
				2 => 1
			];
			
			$menus_map = [];
			// $menus = [];
			foreach($q->result() as $m)
			{
				if( ! empty($m->pe_id) && ! isset($menus_map[$m->pe_id]) )
					continue;
				
				$menu_id = UUID_TO_BIN(UUIDv4());
				$menus_map[$m->me_id] = $menu_id;
				
				$this->upgrade['menus'][] = [
					'company_id' => $company_id,
					'menu_id'    => $menu_id,
					'parent_id'  => empty($m->pe_id) ? NULL : $menus_map[$m->pe_id],
					'program_id' => NULL,
					'owner'      => NULL,
					'name'       => $m->title,
					'description' => '',
					'type'        =>  empty($m->pe_id) ? 1 : 0,
					'sticky'      => $sticky[$m->common],
				];

				// var_dump($m);
			}
		}
		else
		{
			exit;
		}
		
		// var_dump($this->upgrade);exit;
		
		// $menu_group = [];
		// @TABLE menu_departments
		$q = $this->db->query("SELECT * FROM {$this->database}.menu_departments");
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $md)
			{
				if( isset($menus_map[$md->me_id]) && isset($departments_map[$md->de_id]) )
				{
					$this->upgrade['menu_group'][] = [
						'menu_id'  => $menus_map[$md->me_id],
						'group_id' => $departments_map[$md->de_id]
					];
				}
			}
		}
		else
		{
			exit;
		}
		
		// @TABLE menu_permissions
		$q = $this->db->query("SELECT * FROM {$this->database}.menu_permissions ORDER BY me_id");
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $mp)
			{
				if( isset($menus_map[$mp->me_id]) && isset($groups_map[$mp->gr_id]) )
				{
					$this->upgrade['menu_group'][] = [
						'menu_id'  => $menus_map[$mp->me_id],
						'group_id' => $groups_map[$mp->gr_id]
					];
				}
			}
		}
		else
		{
			exit;
		}
		
		// var_dump($this->upgrade);exit;
		
		// @TABLE menu_permissions_user
		$q = $this->db->query("SELECT * FROM {$this->database}.menu_permissions_user ORDER BY us_id, me_id");
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $mp)
			{
				if( isset($menus_map[$mp->me_id]) && isset($users_map[$mp->us_id]) && $this->document_author === FALSE )
				{
					$this->upgrade['acl'][] = [
						'user_id'   => $users_map[$mp->us_id],
						'group_id'  => NULL,
						'object_id' => $menus_map[$mp->me_id],
						'action_id' => UUID_TO_BIN($this->menu['read'])
					];
				}
			}
		}
		
		// var_dump($this->upgrade);exit;
		
		// @TABLE menu_permissions_user_write
		$q = $this->db->query("SELECT * FROM {$this->database}.menu_permissions_user_write ORDER BY us_id, me_id");
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $mp)
			{
				if( isset($menus_map[$mp->me_id]) && isset($users_map[$mp->us_id]) )
				{
					if( $this->document_author === FALSE )
					{
						$this->upgrade['acl'][] = [
							'user_id'   => $users_map[$mp->us_id],
							'group_id'  => NULL,
							'object_id' => $menus_map[$mp->me_id],
							'action_id' => UUID_TO_BIN($this->menu['update'])
						];				
					}
					else
					{
						$this->upgrade['acl'][] = [
							'user_id'   => $users_map[$mp->us_id],
							'group_id'  => NULL,
							'object_id' => $menus_map[$mp->me_id],
							'action_id' => UUID_TO_BIN($this->menu['author'])
						];	
					}
				}
			}
		}
		
		// var_dump($this->upgrade);
		
		$notice_board_id            = UUID_TO_BIN(UUIDv4());
		$notice_board_employees_id  = UUID_TO_BIN(UUIDv4());
		$notice_board_management_id = UUID_TO_BIN(UUIDv4());
		$notice_board_concern_id    = UUID_TO_BIN(UUIDv4());
		
		$notice_board_types = [
			'management' => $notice_board_management_id,
			'employees'  => $notice_board_employees_id,
			'concern'    => $notice_board_concern_id,
		];
		
		$this->upgrade['categories'] = [
			[
				'company_id'  => $company_id,
				'category_id' => $notice_board_id,
				'parent_id'   => NULL,
				'name'        => 'Anslagstavlor',
				'slug'        => 'anslagstavlor' 
			],
			[
				'company_id'  => $company_id,
				'category_id' => $notice_board_employees_id,
				'parent_id'   => $notice_board_id,
				'name'        => 'Anställda',
				'slug'        => 'anstallda' 
			],
			[
				'company_id'  => $company_id,
				'category_id' => $notice_board_management_id,
				'parent_id'   => $notice_board_id,
				'name'        => 'Ledning',
				'slug'        => 'ledning' 
			],
			[
				'company_id'  => $company_id,
				'category_id' => $notice_board_concern_id,
				'parent_id'   => $notice_board_id,
				'name'        => 'Koncern',
				'slug'        => 'koncern' 
			],
		];
		
		$this->upgrade['category_group'] = [
			[
				'group_id'    => $groups_map[$this->admin],
				'category_id' => $notice_board_management_id,
			],
			[
				'group_id'    => $groups_map[$this->admin],
				'category_id' => $notice_board_concern_id,
			],
			[
				'group_id'    => $groups_map[$this->management],
				'category_id' => $notice_board_management_id,
			],
			[
				'group_id'    => $groups_map[$this->management],
				'category_id' => $notice_board_concern_id,
			],
		];
		
		$date = date('Y-m-d H:i:s');
		$anslagstavlor_id = UUID_TO_BIN(UUIDv4());
		
		$this->upgrade['pages'] = [
			[
				'company_id' => $company_id,
				'page_id'    => UUID_TO_BIN('ddda8c79-4219-4f08-8251-37b838e6096e'),
				'parent_id'  => NULL,
				'created_by' => UUID_TO_BIN($this->null),
				'created_date' => $date,
				'edited_by'    => UUID_TO_BIN($this->null),
				'edited_date'  => $date,
				'name'         => '01 Kvalitetsprotokoll',
				'content'      => '',
				'href'         => '/form/id/' . $this->form_id,
				'icon'         => 'fa-user-md',
				'position'     => '1',
				'layout'       => 'folder',
				'status'       => 'private',
			],
			[
				'company_id' => $company_id,
				'page_id'    => UUID_TO_BIN(UUIDv4()),
				'parent_id'  => NULL,
				'created_by' => UUID_TO_BIN($this->null),
				'created_date' => $date,
				'edited_by'    => UUID_TO_BIN($this->null),
				'edited_date'  => $date,
				'name'         => '02 Avvikelser',
				'content'      => '',
				'href'         => '/deviation',
				'icon'         => 'fa-map-signs',
				'position'     => '1',
				'layout'       => 'folder',
				'status'       => 'private',
			],
			[
				'company_id' => $company_id,
				'page_id'    => UUID_TO_BIN(UUIDv4()),
				'parent_id'  => NULL,
				'created_by' => UUID_TO_BIN($this->null),
				'created_date' => $date,
				'edited_by'    => UUID_TO_BIN($this->null),
				'edited_date'  => $date,
				'name'         => '03 Händelseanalys',
				'content'      => '',
				'href'         => '/eventanalysis',
				'icon'         => 'fa-exclamation-triangle',
				'position'     => '1',
				'layout'       => 'folder',
				'status'       => 'private',
			],
			[
				'company_id' => $company_id,
				'page_id'    => UUID_TO_BIN(UUIDv4()),
				'parent_id'  => NULL,
				'created_by' => UUID_TO_BIN($this->null),
				'created_date' => $date,
				'edited_by'    => UUID_TO_BIN($this->null),
				'edited_date'  => $date,
				'name'         => '04 Riskbedömning',
				'content'      => '',
				'href'         => '/riskassessments',
				'icon'         => 'fa-life-bouy',
				'position'     => '1',
				'layout'       => 'folder',
				'status'       => 'private',
			],
			[
				'company_id' => $company_id,
				'page_id'    => $anslagstavlor_id,
				'parent_id'  => NULL,
				'created_by' => UUID_TO_BIN($this->null),
				'created_date' => $date,
				'edited_by'    => UUID_TO_BIN($this->null),
				'edited_date'  => $date,
				'name'         => 'Anslagstavlor',
				'content'      => '',
				'href'         => '',
				'icon'         => 'fa-file-o',
				'position'     => '3',
				'layout'       => 'tags',
				'status'       => 'private',
			],
		];
		
		$this->upgrade['page_category'] = [
			[
				'page_id'     => $anslagstavlor_id,
				'category_id' => $notice_board_employees_id,
			],
			[
				'page_id'     => $anslagstavlor_id,
				'category_id' => $notice_board_management_id,
			],
			[
				'page_id'     => $anslagstavlor_id,
				'category_id' => $notice_board_concern_id,
			],
		];
		
		// @TABLE notice_board_entries
		$q = $this->db->query("SELECT * FROM {$this->database}.notice_board_entries WHERE state ='active'");
		if( $q->num_rows() !== 0 )
		{
			$notice_board_entries_map = [];
			$notice_board_entries_comments = [];
			$notice_board_entries_type = [];
			// $this->data['departments'] = [];
			foreach($q->result() as $nbe)
			{
				$post_id = UUID_TO_BIN(UUIDv4());
				$notice_board_entries_map[$nbe->nbeid] = $post_id;
				$notice_board_entries_comments[$nbe->nbeid] = 0;
				$notice_board_entries_type[$nbe->nbeid] = $nbe->notice_board;
				
				$this->upgrade['posts'][$nbe->nbeid] = [
					'post_id'      => $post_id,
					'created_by'   => isset($users_map[$nbe->uid]) ? $users_map[$nbe->uid] : NULL,
					'created_date' => $nbe->ts,
					'edited_by'    => isset($users_map[$nbe->uid]) ? $users_map[$nbe->uid] : NULL,
					'edited_date'  => $nbe->ts,
					'name'         => $nbe->headline,
					'content'      => $nbe->text,
					'publish'      => substr($nbe->ts, 0,10),
					'comments'     => 0,
					'status'       => 'published'
				];
			}
		}
		
		if( isset($notice_board_entries_type) && ! empty($notice_board_entries_type) )
		{
			foreach($notice_board_entries_type as $nbeid => $type)
			{
				$this->upgrade['post_category'][] = [
					'post_id'     => $notice_board_entries_map[$nbeid],
					'category_id' => $notice_board_types[$type],
				];
			}
		}
		
		// var_dump($this->upgrade['post_category']);exit;
		
		// @TABLE comments
		$q = $this->db->query("SELECT * FROM {$this->database}.comments");
		if( $q->num_rows() !== 0 )
		{
			// $this->data['departments'] = [];
			foreach($q->result() as $nbe)
			{
				if( ! isset($notice_board_entries_comments[$nbe->nbeid]) )
					continue;

				$comment_id = UUID_TO_BIN(UUIDv4());
				$notice_board_entries_comments[$nbe->nbeid] += 1;
				
				$this->upgrade['post_comment'][$nbe->id] = [
					'comment_id'   => $comment_id,
					'post_id'      => $notice_board_entries_map[$nbe->nbeid],
					'created_by'   => isset($users_map[$nbe->uid]) ? $users_map[$nbe->uid] : NULL,
					'created_date' => $nbe->ts,
					'comment'      => $nbe->text,
				];
			}
		}

		if( ! empty($notice_board_entries_comments) )
		{
			foreach($notice_board_entries_comments as $nbeid => $comments)
			{
				$this->upgrade['posts'][$nbeid]['comments'] = $comments;
			}
		}

		// var_dump($this->upgrade['posts']);

		// @TABLE notice_board_entries_read
		$q = $this->db->query("SELECT * FROM {$this->database}.notice_board_entries_read");
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $read)
			{
				if( ! isset($notice_board_entries_map[$read->nbeid]) OR ! isset($users_map[$read->us_id]) )
					continue;

				$this->upgrade['post_read'][] = [
					'post_id' => $notice_board_entries_map[$read->nbeid],
					'user_id' => $users_map[$read->us_id],
				];
			}
		}

		// @TABLE document_category
		$q = $this->db->query("SELECT * FROM {$this->database}.document_category");
		if( $q->num_rows() !== 0 )
		{
			$document_category_map = [];
			// $this->data['departments'] = [];
			foreach($q->result() as $dc)
			{
				$document_category = UUID_TO_BIN(UUIDv4());
				$document_category_map[$dc->dc_id] = $document_category;
				
				$this->upgrade['document_category'][] = [
					'id'   => $document_category,
					'name' => $dc->title,
				];
			}
		}
		else
		{
			exit;
		}

		// $department_count

		// @TABLE subcats
		$q = $this->db->query("SELECT * FROM {$this->database}.subcats WHERE `type` = 'files'");
		if( $q->num_rows() !== 0 )
		{
			$folders_map = [];
			// $this->data['departments'] = [];
			foreach($q->result() as $sc)
			{
				if( ! isset($menus_map[$sc->me_id]) )
					continue;

				$folder_id = UUID_TO_BIN(UUIDv4());
				$folders_map[$sc->su_id] = $folder_id;
				
				$this->upgrade['folders'][] = [
					'folder_id' => $folder_id,
					'menu_id'   => $menus_map[$sc->me_id],
					'order_by'  => $sc->order_by == 'asc' ? 0 : 1,
					'name'      => $sc->title,
				];
			}
		}

		// @TABLE subcats_departments
		// if( count($deparments_in_use) !== 1 && $this->document_author === FALSE )
		if( count($deparments_in_use) !== 1 )
		{
			$q = $this->db->query("SELECT * FROM {$this->database}.subcats_departments WHERE `su_id` IN(
					SELECT `su_id` FROM {$this->database}.subcats_departments GROUP BY `su_id` HAVING COUNT(`su_id`) != ?
				)", [
				$department_count
			]);

			if( $q->num_rows() !== 0 )
			{
				foreach($q->result() as $sd)
				{
					if( ! isset($departments_map[$sd->de_id]) OR ! isset($folders_map[$sd->su_id]) )
						continue;
					
					$this->upgrade['folder_group'][] = [
						'folder_id' => $folders_map[$sd->su_id],
						'group_id'  => $departments_map[$sd->de_id]
					];
				}
			}
		}

		// @TABLE deviation_new
		$q = $this->db->query("SELECT * FROM {$this->database}.deviation_new");
		if( $q->num_rows() !== 0 )
		{
			$deviation_map = [];
			foreach($q->result() as $d)
			{
				$a_id = UUID_TO_BIN(UUIDv4());
				$deviation_map[$d->a_id] = $a_id;
				
				$this->upgrade['deviation'][] = [
					'company_id'     => $company_id,
					'a_id'           => $a_id,
					'reg_date_one'   => $d->reg_date_one,
					'regby_one'      => $this->deviation($d, 'reg_date_one', 'regby_one', $users_map),
					'reg_date_two'   => $d->reg_date_two,
					'regby_two'      => $this->deviation($d, 'reg_date_two', 'regby_two', $users_map),
					'reg_date_three' => $d->reg_date_three,
					'regby_three'    => $this->deviation($d, 'reg_date_three', 'regby_three', $users_map),
					'eventanalysis'  => ( empty($d->eventanalysis) OR ! isset($users_map[$d->eventanalysis]) ) ? NULL : $users_map[$d->eventanalysis],
				];
			}
		}

		// @TABLE deviation_department
		$q = $this->db->query("SELECT * FROM {$this->database}.deviation_department");
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $d)
			{
				if( ! isset($deviation_map[$d->a_id]) OR ! isset($departments_map[$d->de_id]) )
					continue;

				$this->upgrade['deviation_department'][] = [
					'a_id'  => $deviation_map[$d->a_id],
					'de_id' => $departments_map[$d->de_id]
				];
			}
		}

		// @TABLE deviation_fields
		$q = $this->db->query("SELECT * FROM {$this->database}.deviation_fields");
		if( $q->num_rows() !== 0 )
		{
			$deviation_fields_map = [];
			$deviation_radio_map = [];
			$deviation_fields_input_map = [];
			foreach($q->result() as $d)
			{
				if( $d->df_id == 1 )
					$df_id = UUID_TO_BIN('9edbc3cc-2293-4bcf-9004-8e1fdd0e5252');
				else
					$df_id = UUID_TO_BIN(UUIDv4());
				
				$deviation_fields_map[$d->df_id] = $df_id;
				$deviation_fields_input_map[$d->df_id] = $d->input;

				// Convert yes/no checkbox inte radio layout
				if($d->input === 'checkbox')
				{
					$no  = UUID_TO_BIN(UUIDv4());
					$yes = UUID_TO_BIN(UUIDv4());
					$deviation_radio_map[$d->df_id] = [
						1 => $no,
						2 => $yes,
					];

					$this->upgrade['deviation_dropdown'][] = [
						'dd_id' => $no,
						'df_id' => $df_id,
						'order' => '0',
						'title' => 'Nej',
						'default' => 1,
					];

					$this->upgrade['deviation_dropdown'][] = [
						'dd_id' => $yes,
						'df_id' => $df_id,
						'order' => '1',
						'title' => 'Ja',
						'default' => 0,
					];
				}
				
				$this->upgrade['deviation_fields'][] = [
					'df_id'             => $df_id,
					'input'             => $d->input === 'checkbox' ? 'radio' : $d->input,
					'required'          => $d->required,
					'required_kvalprak' => $d->required_kvalprak,
					'search'            => $d->search,
					'list'              => $d->list,
					'page'              => $d->page,
					'order'             => $d->order,
					'title'             => $d->title,
					'description'       => $d->description
				];
			}
		}

		// @TABLE deviation_fields_active
		$q = $this->db->query("SELECT * FROM {$this->database}.deviation_fields_active");
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $d)
			{
				if( ! isset($deviation_map[$d->a_id]) OR ! isset($deviation_fields_map[$d->df_id]) )
					continue;

				$this->upgrade['deviation_fields_active'][] = [
					'a_id'  => $deviation_map[$d->a_id],
					'df_id' => $deviation_fields_map[$d->df_id]
				];
			}
		}

		// @TABLE deviation_dropdown
		$q = $this->db->query("SELECT * FROM {$this->database}.deviation_dropdown");
		if( $q->num_rows() !== 0 )
		{
			$deviation_dropdown_map = [];
			foreach($q->result() as $d)
			{
				if( ! isset($deviation_fields_map[$d->df_id]) )
					continue;

				$dd_id = UUID_TO_BIN(UUIDv4());
				$deviation_dropdown_map[$d->dd_id] = $dd_id;

				$this->upgrade['deviation_dropdown'][] = [
					'dd_id' => $dd_id,
					'df_id' => $deviation_fields_map[$d->df_id],
					'order' => $d->order,
					'title' => $d->title,
					'default' => 0,
				];
			}
		}
		
		// var_dump($this->upgrade['groups'],$this->upgrade['deviation_dropdown']);exit;

		// @TABLE deviation_dropdown
		$q = $this->db->query("SELECT * FROM {$this->database}.deviation_answers");
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $d)
			{
				if( ! isset($deviation_map[$d->a_id]) OR ! isset($deviation_fields_map[$d->df_id]) )
					continue;

				$answer = $d->answer;

				if( $deviation_fields_input_map[$d->df_id] === 'dropdown' )
				{
					if( isset($deviation_dropdown_map[$d->answer]) )
						$answer = BIN_TO_UUID($deviation_dropdown_map[$d->answer]);
					else
						$answer = NULL;
				}

				if( $deviation_fields_input_map[$d->df_id] === 'users' )
				{
					if( isset($users_map[$d->answer]) )
						$answer = BIN_TO_UUID($users_map[$d->answer]);
					else
						$answer = NULL;
				}

				if( $deviation_fields_input_map[$d->df_id] === 'checkbox' )
				{
					if( isset($deviation_radio_map[$d->df_id]) )
						$answer = BIN_TO_UUID($deviation_radio_map[$d->df_id][$d->answer]);
					else
						$answer = NULL;
				}

				if( ! is_null($answer) )
				{
					$this->upgrade['deviation_answers'][] = [
						'a_id' => $deviation_map[$d->a_id],
						'df_id' => $deviation_fields_map[$d->df_id],
						'answer' => $answer,
					];
				}
			}
		}

		// @TABLE deviation_email_default
		$q = $this->db->query("SELECT * FROM {$this->database}.deviation_email_default");
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $d)
			{
				if( empty($d->us_id) )
					continue;

				if( ! isset($users_map[$d->us_id]) OR ! isset($deviation_fields_map[$d->df_id]) OR ! isset($departments_map[$d->department]) )
					continue;

				$this->upgrade['deviation_email_default'][] = [
					'df_id'    => $deviation_fields_map[$d->df_id],
					'user_id'  => $users_map[$d->us_id],
					'group_id' => $departments_map[$d->department],
				];
			}
		}

		// @TABLE deviation_email
		$q = $this->db->query("SELECT * FROM {$this->database}.deviation_email");
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $d)
			{
				if( empty($d->us_id) )
					continue;

				if( ! isset($users_map[$d->us_id]) OR ! isset($deviation_fields_map[$d->df_id]) OR ! isset($deviation_map[$d->a_id]) )
					continue;

				$this->upgrade['deviation_email'][] = [
					'a_id'    => $deviation_map[$d->a_id],
					'df_id'   => $deviation_fields_map[$d->df_id],
					'user_id' => $users_map[$d->us_id],
				];
			}
		}

		// @TABLE eventanalysis
		$q = $this->db->query("SELECT * FROM {$this->database}.eventanalysis");
		if( $q->num_rows() !== 0 )
		{
			$eventanalysis_map = [];
			foreach($q->result() as $e)
			{
				$id = UUID_TO_BIN(UUIDv4());
				$eventanalysis_map[$e->id] = $id;
				
				$this->upgrade['eventanalysis'][$e->id] = [
					'company_id' => $company_id,
					'id'         => $id,
					'name'       => $e->name,
					'us_id'      => ( empty($e->us_id) OR ! isset($users_map[$e->us_id]) ) ? NULL : $users_map[$e->us_id],
					'plan'       => $e->plan,
					'done'       => $e->done,
					'created_date' => date('Y-m-d H:i:s', strtotime("+{$e->id} sec"))
				];
			}
		}

		// @TABLE eventanalysis_actionlist
		$q = $this->db->query("SELECT * FROM {$this->database}.eventanalysis_actionlist");
		if( $q->num_rows() !== 0 )
		{
			$eventanalysis_actionlist_answers = [];
			foreach($q->result() as $e)
			{
				if( ! isset($eventanalysis_map[$e->ea_id]) )
					continue;

				$id = UUID_TO_BIN(UUIDv4());
				$eventanalysis_actionlist_answers[$e->ea_id][] = $id;
				
				$this->upgrade['eventanalysis_actionlist'][] = [
					'id'          => $id,
					'ea_id'       => $eventanalysis_map[$e->ea_id],
					'name'        => $e->name,
					'description' => $e->description,
					'responsible' => ( empty($e->responsible) OR ! isset($users_map[$e->responsible]) ) ? NULL : $users_map[$e->responsible],
					'done'        => $e->done,
				];
			}
		}

		// @TABLE eventanalysis_questions
		$q = $this->db->query("SELECT * FROM {$this->database}.eventanalysis_questions ORDER BY id ASC");
		if( $q->num_rows() !== 0 )
		{
			$eventanalysis_questions_map = [];
			$eventanalysis_questions_empty_answers = [];
			$eventanalysis_questions_answers = [];
			foreach($q->result() as $e)
			{
				if( ! isset($eventanalysis_map[$e->ea_id]) )
					continue;

				$id = UUID_TO_BIN(UUIDv4());
				$eventanalysis_questions_map[$e->id] = $id;
				
				$this->upgrade['eventanalysis_questions'][] = [
					'id'          => $id,
					'ea_id'       => $eventanalysis_map[$e->ea_id],
					'parent'      => ( empty($e->parent) OR ! isset($eventanalysis_questions_map[$e->parent]) ) ? NULL : $eventanalysis_questions_map[$e->parent],
					'question'    => $e->question,
					'answer'      => $e->answer,
					'responsible' => ( empty($e->responsible) OR ! isset($users_map[$e->responsible]) ) ? NULL : $users_map[$e->responsible],
				];

				if(
					empty($e->answer) && 
					isset($users_map[$e->responsible])
				) {
					if( ! isset($eventanalysis_questions_empty_answers[$e->ea_id]) )
						$eventanalysis_questions_empty_answers[$e->ea_id][] = $e->responsible;

					if( ! in_array($e->responsible,$eventanalysis_questions_empty_answers[$e->ea_id]) )
						$eventanalysis_questions_empty_answers[$e->ea_id][] = $e->responsible;
				}

				if( ! empty($e->answer) )
				{
					$eventanalysis_questions_answers[$e->ea_id][] = $id;
				}
			}
		}

		if( ! empty($eventanalysis_map) )
		{
			$date = date('Y-m-d H:i:s');
			foreach($eventanalysis_map as $eventanalysis_id => $eventanalysis_bin)
			{
				if( isset($eventanalysis_questions_empty_answers[$eventanalysis_id]) )
				{
					foreach($eventanalysis_questions_empty_answers[$eventanalysis_id] as $responsible)
					{
						$this->upgrade['user_messages'][] = [
							'user_id'     => $users_map[$responsible],
							'type'        => 'eventanalysis',
							'type_id'     => $eventanalysis_bin,
							'action'      => 'qa',
							'severity'    => 'warning',
							'created_at'  => $date,
							'comment'     => ''
						];
					}
				}

				if(
					isset($eventanalysis_questions_answers[$eventanalysis_id]) &&
					! isset($eventanalysis_questions_empty_answers[$eventanalysis_id]) &&
					! isset($eventanalysis_actionlist_answers[$eventanalysis_id]) &&
					$this->upgrade['eventanalysis'][$eventanalysis_id]['us_id'] !== NULL
				)
				{
					$this->upgrade['user_messages'][] = [
						'user_id'     => $this->upgrade['eventanalysis'][$eventanalysis_id]['us_id'],
						'type'        => 'eventanalysis_actionlist',
						'type_id'     => $eventanalysis_bin,
						'action'      => 'actionlist',
						'severity'    => 'warning',
						'created_at'  => $date,
						'comment'     => ''
					];
				}
			}
		}

		// @TABLE risk_assessments
		$q = $this->db->query("SELECT * FROM {$this->database}.risk_assessments");
		if( $q->num_rows() !== 0 )
		{
			$risk_assessments_map = [];
			foreach($q->result() as $r)
			{
				$ra_id = UUID_TO_BIN(UUIDv4());
				$risk_assessments_map[$r->ra_id] = $ra_id;
				
				$this->upgrade['risk_assessments'][] = [
					'company_id'  => $company_id,
					'ra_id'       => $ra_id,
					'name'        => $r->name,
					'description' => $r->description,
					'us_id'       => ( empty($r->us_id) OR ! isset($users_map[$r->us_id]) ) ? NULL : $users_map[$r->us_id],
					'date'        => $r->date,
				];
			}
		}

		// @TABLE risk_assessments_department
		$q = $this->db->query("SELECT * FROM {$this->database}.risk_assessments_department");
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $r)
			{
				if( ! isset($risk_assessments_map[$r->ra_id]) OR ! isset($departments_map[$r->de_id]) )
					continue;

				$this->upgrade['risk_assessments_department'][] = [
					'ra_id' => $risk_assessments_map[$r->ra_id],
					'de_id' => $departments_map[$r->de_id],
				];
			}
		}

		// @TABLE risk_assessments_risk
		$q = $this->db->query("SELECT * FROM {$this->database}.risk_assessments_risk");
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $r)
			{
				if( ! isset($risk_assessments_map[$r->ra_id]) )
					continue;

				$id = UUID_TO_BIN(UUIDv4());

				$this->upgrade['risk_assessments_risk'][] = [
					'id'          => $id,
					'ra_id'       => $risk_assessments_map[$r->ra_id],
					'risk'        => $r->risk,
					'occurrence'  => $r->occurrence,
					'severity'    => $r->severity,
					'explanation' => $r->explanation,
					'acceptable'  => $r->acceptable,
					'measure'     => $r->measure,
					'responsible' => ( empty($r->responsible) OR ! isset($users_map[$r->responsible]) ) ? NULL : $users_map[$r->responsible],
					'done'        => $r->done,
					'finished'    => $r->finished,
				];
			}
		}

		// @TABLE deviation_eventanalysis
		$q = $this->db->query("SELECT * FROM {$this->database}.deviation_eventanalysis");
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $d)
			{
				if( ! isset($deviation_map[$d->a_id]) OR ! isset($eventanalysis_map[$d->ea_id]) )
					continue;

				$this->upgrade['deviation_eventanalysis'][] = [
					'a_id'  => $deviation_map[$d->a_id],
					'ea_id' => $eventanalysis_map[$d->ea_id],
				];
			}
		}

		// @TABLE deviation_risk_assessments
		$q = $this->db->query("SELECT * FROM {$this->database}.deviation_risk_assessments");
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $d)
			{
				if( ! isset($deviation_map[$d->a_id]) OR ! isset($risk_assessments_map[$d->ra_id]) )
					continue;

				$this->upgrade['deviation_risk_assessments'][] = [
					'a_id'  => $deviation_map[$d->a_id],
					'ra_id' => $risk_assessments_map[$d->ra_id],
				];
			}
		}

		// @TABLE risk_assessments_eventanalysis
		$q = $this->db->query("SELECT * FROM {$this->database}.risk_assessments_eventanalysis");
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $d)
			{
				if( ! isset($eventanalysis_map[$d->ea_id]) OR ! isset($risk_assessments_map[$d->ra_id]) )
					continue;

				$this->upgrade['risk_assessments_eventanalysis'][] = [
					'ea_id' => $eventanalysis_map[$d->ea_id],
					'ra_id' => $risk_assessments_map[$d->ra_id],
				];
			}
		}
		
		// @TABLE files_text_versions
		$q = $this->db->query("SELECT * FROM {$this->database}.files_text_versions ORDER BY updated DESC");
		if( $q->num_rows() !== 0 )
		{
			$files_content = [];
			foreach($q->result() as $f)
			{
				if( ! isset($files_content[$f->file_number]) )
					$files_content[$f->file_number] = $f->content;
			}
		}
		
		// var_dump($files_content); exit;
		
		// @TABLE subcat_files
		$q = $this->db->query("SELECT * FROM {$this->database}.subcat_files");
		if( $q->num_rows() !== 0 )
		{
			$subcat_files = [];
			foreach($q->result() as $f)
			{
				if( ! isset($subcat_files[$f->file_number]) )
					$subcat_files[$f->file_number] = $f->su_id;
			}
		}
		
		// @TABLE files
		$q = $this->db->query("SELECT * FROM {$this->database}.files WHERE state IN('active','archived')");
		if( $q->num_rows() !== 0 )
		{
			$files_map = [];
			$files_rev_map = [];
			foreach($q->result() as $f)
			{
				$file_number = UUID_TO_BIN(UUIDv4());
				$files_map[$f->file_number] = $file_number;
				if( ! empty($f->ts_rev) && $f->ts_rev !== '0000-00-00' )
					$files_rev_map[$f->file_number] = $f->ts_rev;
				
				$null = UUID_TO_BIN($this->null);
				
				$this->upgrade['documents'][$f->file_number] = [
					'document_id'       => $file_number,
					'parent_id'         => NULL,
					'folder_id'         => ( isset($subcat_files[$f->file_number]) && isset($folders_map[$subcat_files[$f->file_number]]) ) ? $folders_map[$subcat_files[$f->file_number]] : NULL, // subcat files
					'created_by'        => ( empty($f->created_by) OR ! isset($users_map[$f->created_by]) ) ? $null : $users_map[$f->created_by],
					'created_date'      => $f->ts_created1 . ' 00:00:00',
					'edited_by'         => ( empty($f->edited_by) OR ! isset($users_map[$f->edited_by]) ) ? $null : $users_map[$f->edited_by],
					'edited_date'       => $f->ts_edit . ' 00:00:00',
					'edited_reason'     => NULL,
					'name'              => $f->title,
					'description'       => $f->description,
					'created'           => $f->ts_created2,
					'accepted_by'       => NULL,
					'document_type'     => $f->document_type,
					'document_category' => isset($document_category_map[$f->category]) ? $document_category_map[$f->category] : NULL,
					'last_revised'      => $f->ts_rev,
					'valid_until'       => $f->ts_valid,
					'reminder'          => $f->ts_update,
					// 'content'           => ( isset($files_content[$f->file_number]) ) ? $files_content[$f->file_number] : NULL,
					'content'           => NULL,
					// 'content_clean'     => ( isset($files_content[$f->file_number]) ) ? $this->document_clean($files_content[$f->file_number]) : NULL,
					'content_clean'     => NULL,
					'owner'             => ( empty($f->owner) OR ! isset($users_map[$f->owner]) ) ? $null : $users_map[$f->owner],
					'status'            => ( $f->state === 'active' ) ? 'published' : 'unpublished',
				];
			}
		}

		// @TABLE document_attachment
		$q = $this->db->query("SELECT * FROM {$this->database}.files_files");
		if( $q->num_rows() !== 0 )
		{
			$dir_exists = TRUE;
			$upload_base_path = realpath(APPPATH . '..' . DIRECTORY_SEPARATOR . 'uploads' . DIRECTORY_SEPARATOR . 'documents');
			$upload_path = $upload_base_path . DIRECTORY_SEPARATOR . BIN_TO_UUID($company_id);
			if( !is_dir($upload_path) )
				$dir_exists = mkdir($upload_path, 0777);
			
			if( $dir_exists )
			{
				$convert_base_path = realpath(APPPATH . '..' . DIRECTORY_SEPARATOR . 'kiv7');

				$attachment_map = [];
				
				foreach($q->result() as $f)
				{
					if( ! isset($files_map[$f->file_number]) )
						continue;
					
					$uploaded_file = $convert_base_path . DIRECTORY_SEPARATOR . 'file_' . $f->ff_id . '_1';
					
					if( ! file_exists($uploaded_file) )
						continue;
					
					$attachment_id = UUIDv4();
					
					$file_ext = explode('.',$f->filename);
					$file_ext = '.' . array_pop($file_ext);
					
					$new_file = $upload_path . DIRECTORY_SEPARATOR . $attachment_id . $file_ext;
					
					copy($uploaded_file, $new_file);

					$attachment_map[$f->ff_id] = $attachment_id;
						
					$this->upgrade['document_attachment'][] = [
						'document_id'   => $files_map[$f->file_number],
						'attachment_id' => UUID_TO_BIN($attachment_id),
						'uploaded_on'   => $f->created,
						'file_name'     => $f->filename,
						'file_ext'      => $file_ext
					];
				}
			}
		}
		
		if( ! empty($this->upgrade['documents']) )
		{
			foreach($this->upgrade['documents'] as $tmp_document_id => $document)
			{
				if( ! isset($files_content[$tmp_document_id]) )
					continue;
				
				$replaced = FALSE;
				
				$re = '@src="(/uploads/images/)(.+?)"@m';
				preg_match_all($re, $files_content[$tmp_document_id], $images, PREG_SET_ORDER, 0);
				
				if( ! empty($images) )
				{
					$files_content[$tmp_document_id] = str_replace('src="/uploads/images/','src="/resources/uploads/', $files_content[$tmp_document_id]);
					$replaced = TRUE;
				}
				
				$re = '@<a.+?href="(.+?view_subcat_content.+?file_number=([0-9]+))"(?:.+?")?>(.+?)</a>@';
				preg_match_all($re, $files_content[$tmp_document_id], $matches, PREG_SET_ORDER, 0);
				
				if( ! empty($matches) )
				{
					foreach($matches as $match)
					{
						if( count($match) !== 4)
							continue;
						
						if( ! ctype_digit($match[2]) )
							continue;
						
						if( isset($files_map[$match[2]]) )
						{
							$replace_with = '/documents/' . BIN_TO_UUID($files_map[$match[2]]);
							$files_content[$tmp_document_id] = str_replace($match[1],$replace_with, $files_content[$tmp_document_id]);
						}
						else
						{
							$files_content[$tmp_document_id] = str_replace($match[0],$match[3], $files_content[$tmp_document_id]);
						}
						
						$replaced = TRUE;
					}
				}
				
				$re = '@<a.+?href="(.+?get_file.+?file=([0-9]+))"(?:.+?")?>(.+?)</a>@';
				preg_match_all($re, $files_content[$tmp_document_id], $matches, PREG_SET_ORDER, 0);
				
				if( ! empty($matches) )
				{
					foreach($matches as $match)
					{
						if( count($match) !== 4)
							continue;
						
						if( ! ctype_digit($match[2]) )
							continue;
						
						if( isset($attachment_map[$match[2]]) )
						{
							$replace_with = '/documents/download/' . $attachment_map[$match[2]];
							$files_content[$tmp_document_id] = str_replace($match[1],$replace_with, $files_content[$tmp_document_id]);
						}
						else
						{
							$files_content[$tmp_document_id] = str_replace($match[0],$match[3], $files_content[$tmp_document_id]);
						}
						
						$replaced = TRUE;
					}
				}
				
				// if( $replaced )
				// {
					$this->upgrade['documents'][$tmp_document_id]['content'] = $files_content[$tmp_document_id];
					$this->upgrade['documents'][$tmp_document_id]['content_clean'] = $this->document_clean($files_content[$tmp_document_id]);					
				// }
			}
		}
		
		// var_dump($this->upgrade['documents']);exit;
		
		// @TABLE files_department
		// if( count($deparments_in_use) !== 1 && $this->document_author === FALSE )
		if( count($deparments_in_use) !== 1 )
		{
			$q = $this->db->query("SELECT * FROM {$this->database}.files_department WHERE `file_number` IN(
					SELECT `file_number` FROM {$this->database}.files_department GROUP BY `file_number` HAVING COUNT(`file_number`) != ?
				)", [
				$department_count
			]);

			if( $q->num_rows() !== 0 )
			{
				foreach($q->result() as $f)
				{
					if( ! isset($departments_map[$f->de_id]) OR ! isset($files_map[$f->file_number]) )
						continue;
					
					$this->upgrade['document_group'][] = [
						'document_id' => $files_map[$f->file_number],
						'group_id'    => $departments_map[$f->de_id]
					];
				}
			}
		}
		
		// @TABLE education_groups
		$q = $this->db->query("SELECT * FROM {$this->database}.education_groups");
		if( $q->num_rows() !== 0 )
		{
			$education_groups_map = [];
			foreach($q->result() as $e)
			{
				$group_id = UUID_TO_BIN(UUIDv4());
				$education_groups_map[$e->group_id] = $group_id;

				$this->upgrade['groups'][] = [
					'company_id' => $company_id,
					'group_id'   => $group_id,
					'type'       => 'education',
					'name'       => $e->name
				];
			}
		}
		
		// @TABLE education_groups_files
		$q = $this->db->query("SELECT * FROM {$this->database}.education_groups_files");
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $e)
			{
				// if( ! isset($education_groups_map[$e->group_id]) OR ! isset($files_map[$e->file_id]) )
					// continue;
				
				if( ! isset($education_groups_map[$e->group_id]) OR ! isset($files_map[$e->file_id]) OR ! isset($files_rev_map[$e->file_id]) )
					continue;
					
				$this->upgrade['document_education'][] = [
					'user_id'     => UUID_TO_BIN($this->null),
					'group_id'    => $education_groups_map[$e->group_id],
					'document_id' => $files_map[$e->file_id],
					'version'     => $files_rev_map[$e->file_id],
				];
			}
		}
		
		// @TABLE education_groups_groups
		$q = $this->db->query("SELECT * FROM {$this->database}.education_groups_groups");
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $e)
			{
				if( ! isset($education_groups_map[$e->ed_group_id]) OR ! isset($groups_map[$e->group_id]) )
					continue;
					
				$this->upgrade['document_education_group'][] = [
					'education_id' => $education_groups_map[$e->ed_group_id],
					'group_id'     => $groups_map[$e->group_id],
				];
			}
		}
		
		// @TABLE education_groups_users
		$q = $this->db->query("SELECT * FROM {$this->database}.education_groups_users");
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $e)
			{
				if( ! isset($education_groups_map[$e->group_id]) OR ! isset($users_map[$e->user_id]) )
					continue;
					
				$this->upgrade['user_group'][] = [
					'user_id'  => $users_map[$e->user_id],
					'group_id' => $education_groups_map[$e->group_id]
				];
			}
		}
		
		// @TABLE education - education_done
		$q = $this->db->query("SELECT * FROM {$this->database}.education WHERE checked = 1");
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $e)
			{
				if( ! isset($users_map[$e->us_id]) OR ! isset($files_map[$e->file_number]) OR ! isset($files_rev_map[$e->file_number]) )
					continue;
				
				$date = substr($e->timestamp,0,10);
				
				if( $date < $files_rev_map[$e->file_number] )
					continue;
					
				$this->upgrade['document_education_done'][] = [
					'user_id'     => $users_map[$e->us_id],
					'document_id' => $files_map[$e->file_number],
					'version'     => $files_rev_map[$e->file_number],
					'done'        => $e->timestamp,
				];
			}
		}
		
		// @TABLE education - document_education
		$q = $this->db->query("SELECT * FROM {$this->database}.education WHERE checked = 0");
		if( $q->num_rows() !== 0 )
		{
			foreach($q->result() as $e)
			{
				if( ! isset($users_map[$e->us_id]) OR ! isset($files_map[$e->file_number]) OR ! isset($files_rev_map[$e->file_number]) )
					continue;
					
				$this->upgrade['document_education'][] = [
					'user_id'     => $users_map[$e->us_id],
					'group_id'    => UUID_TO_BIN($this->null),
					'document_id' => $files_map[$e->file_number],
					'version'     => $files_rev_map[$e->file_number],
				];
			}
		}
		
		if( $answers_department !== NULL )
		{
			// @TABLE form_page_questions
			$q = $this->db->query("SELECT * FROM `form_page_questions` ORDER BY `form_page_questions`.`page_id` ASC");
			if( $q->num_rows() !== 0 )
			{
				$form_page_questions = [];
				foreach($q->result() as $p)
				{
					$p->page_id     = BIN_TO_UUID($p->page_id);
					$p->question_id = BIN_TO_UUID($p->question_id);
					$p->parent_id   = BIN_TO_UUID($p->parent_id);
					
					$form_page_questions[$p->question_id_old] = $p;
				}
			}
			
			// @TABLE form_page_question_options
			$q = $this->db->query("SELECT * FROM `form_page_question_options` ORDER BY `question_id` ASC");
			if( $q->num_rows() !== 0 )
			{
				$form_page_question_options = [];
				foreach($q->result() as $p)
				{
					$p->question_id = BIN_TO_UUID($p->question_id);
					$p->option_id   = BIN_TO_UUID($p->option_id);
					
					$form_page_question_options[$p->question_id][$p->name] = $p->option_id;
				}
			}
			
			// @TABLE answers
			$q = $this->db->query("SELECT * FROM {$this->database}.answers WHERE department = ?",[
				$answers_department
			]);
			if( $q->num_rows() !== 0 )
			{
				foreach($q->result() as $a)
				{
					if( ! isset($form_page_questions[$a->question_id]) )
						continue;
					
					if( in_array($form_page_questions[$a->question_id]->field, ['radio','dropdown']) )
					{
						if( ! isset($form_page_question_options[$form_page_questions[$a->question_id]->question_id][$a->answer]) )
						{
							continue;
						}
						
						$a->answer = $form_page_question_options[$form_page_questions[$a->question_id]->question_id][$a->answer];
					}
					
					if( empty($a->answer) )
					{
						continue;
					}
					
					$this->upgrade['form_survey_' . $this->survey_id][] = [
						'company_id'      => $company_id,
						'page_id'         => UUID_TO_BIN($form_page_questions[$a->question_id]->page_id),
						'question_id'     => UUID_TO_BIN($form_page_questions[$a->question_id]->question_id),
						'answer'          => $a->answer,
					];
				}
			}
			
			$q = $this->db->query("SELECT * FROM {$this->database}.answers_upload WHERE link = 0");
			if( $q->num_rows() !== 0 )
			{
				$dir_exists = TRUE;
				$upload_base_path = realpath(APPPATH . '..' . DIRECTORY_SEPARATOR . 'uploads' . DIRECTORY_SEPARATOR . 'qa');
				$upload_path = $upload_base_path . DIRECTORY_SEPARATOR . BIN_TO_UUID($company_id);
				if( !is_dir($upload_path) )
					$dir_exists = mkdir($upload_path, 0777);
				
				if( $dir_exists )
				{
					$convert_base_path = realpath(APPPATH . '..' . DIRECTORY_SEPARATOR . 'kiv7' . DIRECTORY_SEPARATOR . 'qa');
					
					foreach($q->result() as $f)
					{
						if( ! isset($form_page_questions[$f->question_id]) )
							continue;
						
						if( ! empty($f->file_number) && ! isset($files_map[$f->file_number]) )
							continue;
						
						$uploaded_file = $convert_base_path . DIRECTORY_SEPARATOR . 'file_' . $f->au_id;
						
						if( ! file_exists($uploaded_file) )
							continue;
						
						$attachment_id = UUIDv4();
						
						$file_ext = explode('.',$f->filename);
						$file_ext = '.' . array_pop($file_ext);
						
						$new_file = $upload_path . DIRECTORY_SEPARATOR . $attachment_id . $file_ext;
						
						copy($uploaded_file, $new_file);
							
						$this->upgrade['form_survey_' . $this->survey_id . '_attachment'][] = [
							'question_id'   => UUID_TO_BIN($form_page_questions[$f->question_id]->question_id),
							'attachment_id' => UUID_TO_BIN($attachment_id),
							'document_id'   => ( ! empty($f->file_number) ) ? $files_map[$f->file_number] : NULL,
							'uploaded_on'   => $f->ts_created,
							'file_name'     => $f->filename,
							'file_ext'      => $file_ext
						];
					}
				}
			}
			
			$q = $this->db->query("SELECT * FROM {$this->database}.answers_upload WHERE link = 1");
			if( $q->num_rows() !== 0 )
			{
				$date = date('Y-m-d H:i:s');
				foreach($q->result() as $f)
				{
					if( ! isset($form_page_questions[$f->question_id]) )
						continue;
					
					if( ! empty($f->file_number) && ! isset($files_map[$f->file_number]) )
						continue;
					
					$attachment_id = UUIDv4();
					
					$this->upgrade['form_survey_' . $this->survey_id . '_attachment'][] = [
						'question_id'   => UUID_TO_BIN($form_page_questions[$f->question_id]->question_id),
						'attachment_id' => UUID_TO_BIN($attachment_id),
						'document_id'   => ( ! empty($f->file_number) ) ? $files_map[$f->file_number] : NULL,
						'uploaded_on'   => $date,
						'file_name'     => '',
						'file_ext'      => ''
					];
				}
			}
			
			// var_dump($this->upgrade['form_survey_' . $this->survey_id . '_attachment']);exit;
		}
		
		// exit;
		
		$q = $this->db->query("SELECT * FROM {$this->database}.deviation_upload");
		if( $q->num_rows() !== 0 )
		{
			$dir_exists = TRUE;
			$upload_base_path = realpath(APPPATH . '..' . DIRECTORY_SEPARATOR . 'uploads' . DIRECTORY_SEPARATOR . 'deviation');
			$upload_path = $upload_base_path . DIRECTORY_SEPARATOR . BIN_TO_UUID($company_id);
			if( !is_dir($upload_path) )
				$dir_exists = mkdir($upload_path, 0777);
			
			if( $dir_exists )
			{
				$convert_base_path = realpath(APPPATH . '..' . DIRECTORY_SEPARATOR . 'kiv7' . DIRECTORY_SEPARATOR . 'deviation');
				
				foreach($q->result() as $f)
				{
					if( ! isset($deviation_map[$f->a_id]) )
						continue;
					
					$uploaded_file = $convert_base_path . DIRECTORY_SEPARATOR . 'file_' . $f->au_id;
					
					if( ! file_exists($uploaded_file) )
						continue;
					
					$attachment_id = UUIDv4();
					
					$file_ext = explode('.',$f->filename);
					$file_ext = '.' . array_pop($file_ext);
					
					$new_file = $upload_path . DIRECTORY_SEPARATOR . $attachment_id . $file_ext;
					
					copy($uploaded_file, $new_file);
						
					$this->upgrade['deviation_attachment'][] = [
						'a_id'          => $deviation_map[$f->a_id],
						'attachment_id' => UUID_TO_BIN($attachment_id),
						'uploaded_on'   => $f->ts_created,
						'file_name'     => $f->filename,
						'file_ext'      => $file_ext
					];
				}
			}
		}
		
		// @TODO: move uploaded images
		
		var_dump(array_keys($this->upgrade));
		
		$this->db->insert('companies', $this->upgrade['companies']);
		$this->db->insert_batch('groups', $this->upgrade['groups']);
		$this->db->insert_batch('users', $this->upgrade['users']);
		$this->db->insert_batch('user_group', $this->upgrade['user_group']);
		$this->db->insert_batch('acl_users_and_groups', $this->upgrade['acl']);
		$this->db->insert_batch('menus', $this->upgrade['menus']);
		$this->db->insert_batch('menu_group', $this->upgrade['menu_group']);
		$this->db->insert_batch('categories', $this->upgrade['categories']);
		$this->db->insert_batch('category_group', $this->upgrade['category_group']);
		$this->db->insert_batch('pages', $this->upgrade['pages']);
		$this->db->insert_batch('page_category', $this->upgrade['page_category']);
		
		if( ! empty($this->upgrade['posts']) )
			$this->db->insert_batch('posts', $this->upgrade['posts']);
		
		if( ! empty($this->upgrade['post_category']) )
			$this->db->insert_batch('post_category', $this->upgrade['post_category']);
		
		if( ! empty($this->upgrade['post_comment']) )
			$this->db->insert_batch('post_comment', $this->upgrade['post_comment']);
		
		if( ! empty($this->upgrade['post_read']) )
			$this->db->insert_batch('post_read', $this->upgrade['post_read']);
		
		$this->db->insert_batch('document_category', $this->upgrade['document_category']);
		$this->db->insert_batch('folders', $this->upgrade['folders']);
		
		if( ! empty($this->upgrade['folder_group']) )
			$this->db->insert_batch('folder_group', $this->upgrade['folder_group']);
		
		if( ! empty($this->upgrade['deviation']) )
			$this->db->insert_batch('deviation', $this->upgrade['deviation']);
		
		if( ! empty($this->upgrade['deviation_department']) )
			$this->db->insert_batch('deviation_department', $this->upgrade['deviation_department']);
		
		$this->db->insert_batch('deviation_fields', $this->upgrade['deviation_fields']);
		$this->db->insert_batch('deviation_dropdown', $this->upgrade['deviation_dropdown']);
		
		if( ! empty($this->upgrade['deviation_fields_active']) )
			$this->db->insert_batch('deviation_fields_active', $this->upgrade['deviation_fields_active']);
		
		if( ! empty($this->upgrade['deviation_answers']) )
			$this->db->insert_batch('deviation_answers', $this->upgrade['deviation_answers']);
		
		if( ! empty($this->upgrade['deviation_email']) )
			$this->db->insert_batch('deviation_email', $this->upgrade['deviation_email']);
		
		if( ! empty($this->upgrade['deviation_email_default']) )
			$this->db->insert_batch('deviation_email_default', $this->upgrade['deviation_email_default']);
		
		if( ! empty($this->upgrade['eventanalysis']) )
			$this->db->insert_batch('eventanalysis', $this->upgrade['eventanalysis']);
		
		if( ! empty($this->upgrade['eventanalysis_actionlist']) )
			$this->db->insert_batch('eventanalysis_actionlist', $this->upgrade['eventanalysis_actionlist']);
		
		if( ! empty($this->upgrade['eventanalysis_questions']) )
			$this->db->insert_batch('eventanalysis_questions', $this->upgrade['eventanalysis_questions']);
		
		if( ! empty($this->upgrade['user_messages']) )
			$this->db->insert_batch('user_messages', $this->upgrade['user_messages']);
		
		if( ! empty($this->upgrade['risk_assessments']) )
			$this->db->insert_batch('risk_assessments', $this->upgrade['risk_assessments']);
		
		if( ! empty($this->upgrade['risk_assessments_department']) )
			$this->db->insert_batch('risk_assessments_department', $this->upgrade['risk_assessments_department']);
		
		if( ! empty($this->upgrade['risk_assessments_risk']) )
			$this->db->insert_batch('risk_assessments_risk', $this->upgrade['risk_assessments_risk']);
		
		if( ! empty($this->upgrade['deviation_eventanalysis']) )
			$this->db->insert_batch('deviation_eventanalysis', $this->upgrade['deviation_eventanalysis']);
		
		if( ! empty($this->upgrade['deviation_risk_assessments']) )
			$this->db->insert_batch('deviation_risk_assessments', $this->upgrade['deviation_risk_assessments']);
		
		if( ! empty($this->upgrade['risk_assessments_eventanalysis']) )
			$this->db->insert_batch('risk_assessments_eventanalysis', $this->upgrade['risk_assessments_eventanalysis']);
		
		if( ! empty($this->upgrade['documents']) )
			$this->db->insert_batch('documents', $this->upgrade['documents'], NULL, 25);
		
		if( ! empty($this->upgrade['document_group']) )
			$this->db->insert_batch('document_group', $this->upgrade['document_group']);
		
		if( ! empty($this->upgrade['document_education']) )
			$this->db->insert_batch('document_education', $this->upgrade['document_education']);
		
		if( ! empty($this->upgrade['document_education_group']) )
			$this->db->insert_batch('document_education_group', $this->upgrade['document_education_group']);
		
		if( ! empty($this->upgrade['document_education_done']) )
			$this->db->insert_batch('document_education_done', $this->upgrade['document_education_done']);
		
		if( ! empty($this->upgrade['form_survey_f703ae4ebf']) )
			$this->db->insert_batch('form_survey_f703ae4ebf', $this->upgrade['form_survey_f703ae4ebf']);
		
		if( ! empty($this->upgrade['document_group']) )
			$this->db->insert_batch('document_group', $this->upgrade['document_group']);
		
		if( ! empty($this->upgrade['document_attachment']) )
			$this->db->insert_batch('document_attachment', $this->upgrade['document_attachment']);
		
		if( ! empty($this->upgrade['deviation_attachment']) )
			$this->db->insert_batch('deviation_attachment', $this->upgrade['deviation_attachment']);
		
		if( ! empty($this->upgrade['form_survey_' . $this->survey_id . '_attachment']) )
			$this->db->insert_batch('form_survey_' . $this->survey_id . '_attachment', $this->upgrade['form_survey_' . $this->survey_id . '_attachment']);
	}
}