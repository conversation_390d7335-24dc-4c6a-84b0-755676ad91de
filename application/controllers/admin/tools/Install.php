<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Install extends MY_Controller
{
	public function index()
	{
		$user_id    = UUID_TO_BIN(UUIDv4());
		$company_id = UUID_TO_BIN(UUIDv4());

		$this->db->update('acl_users_and_groups', ['user_id' => $user_id]);
		$this->db->update('categories', ['company_id' => $company_id]);
		$this->db->update('companies', ['company_id' => $company_id]);
		$this->db->update('groups', ['company_id' => $company_id]);
		$this->db->update('menus', ['company_id' => $company_id, 'owner' => $user_id]);
		$this->db->update('pages', ['company_id' => $company_id, 'created_by' => $user_id, 'edited_by' => $user_id]);
		$this->db->update('users', ['company_id' => $company_id, 'user_id' => $user_id]);
		$this->db->update('user_group', ['user_id' => $user_id]);

		redirect('/');
	}
}