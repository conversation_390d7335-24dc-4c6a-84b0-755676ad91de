<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Deviation extends MY_Controller
{
	/**
	 * Searches for deviations created before this fix.
	 */
	public function index()
	{
		$q = $this->db->query("SELECT a_id FROM `deviation` WHERE `reg_date_one` < '2019-04-04' AND `reg_date_two` IS NOT NULL AND `reg_date_two` > '2019-03-10'");
		if( $q->num_rows() !== 0 )
		{
			$deviation = [];
			foreach($q->result() as $d)
			{
				$deviation[] = $d->a_id;
			}
		}

		if( empty($deviation) )
		{
			die('Nothing to fix! Great!');
		}

		$q = $this->db->query("SELECT df_id FROM `deviation_fields` WHERE `page` != 0");
		if( $q->num_rows() !== 0 )
		{
			$fields = [];
			foreach($q->result() as $f)
			{
				$fields[] = $f->df_id;
			}
		}

		if( empty($fields) )
		{
			die('Fields empty! Can\'t continue...');
		}

		$deviation_fields_active = [];
		foreach($deviation as $a_id)
		{
			foreach($fields as $df_id)
			{
				$deviation_fields_active[] = [
					'a_id'  => $a_id,
					'df_id' => $df_id
				];
			}
		}

		if( empty($deviation_fields_active) )
		{
			die('Array empty?! Can\'t even be possible...');
		}

		if( $this->db->insert_batch('deviation_fields_active',$deviation_fields_active) )
		{
			die('Everything fixed! Life is good.');
		}
		else
		{
			die('Nothing to fix. Praise the lord!');
		}
	}
}