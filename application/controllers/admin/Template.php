<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Template extends Admin_Controller
{
	private $settingsPath = CI_UPLOAD_PATH . 'template.json';

	public function __construct()
	{
		parent::__construct();
		$this->data['body_class'] .= 'skin-white fixed';
	}

	public function index()
	{
		$this->load->helper('form');

		$settings = [];
		$settings['other'] = [];

		if(file_exists($this->settingsPath))
		{
			if(($json = file_get_contents($this->settingsPath)) !== FALSE)
			{
				if(($array = json_decode($json, true)) !== NULL)
				{
					$settings = $array;
					if (!array_key_exists('other', $settings)) $settings['other'] = [];
				}
			}
		}

		$this->data['settings'] = $settings;
		if($this->input->server('REQUEST_METHOD') == 'POST')
		{
			foreach( array('docx', 'xlsx', 'pptx') as $ext )
			{
				if( ! empty($_FILES[$ext]['name']) )
				{
					if( is_dir(CI_UPLOAD_PATH) )
					{
						// Generate a unique ID for attached file
						$attachment_id = UUIDv4();

						// File upload configuration
						$config['upload_path']   = CI_UPLOAD_PATH;
						$config['allowed_types'] = [$ext];
						$config['max_size']      = 4096;
						$config['file_name']     = $attachment_id;

						// Load and initialize upload library
						$this->load->library('upload', $config);
						$this->upload->initialize($config);

						// Upload file to server
						if($this->upload->do_upload($ext))
						{
              if (array_key_exists($ext, $settings)) {
                unlink($settings[$ext]);
              }
							$settings[$ext] = $attachment_id . '.' . $ext;
              $settings[$ext . '_name'] = $_FILES[$ext]['name'];
						}
					}
				}
			}

			foreach( $_POST as $key => $value) {
				if ($value == 'delete') {
					$GLOBALS['template_key'] = str_replace("_", ".", $key);
					$settings['other'] = array_filter($settings['other'],  function($record, $record_key) {
						return $record['file'] != $GLOBALS['template_key'];
					}, ARRAY_FILTER_USE_BOTH);
				}
			}

			foreach( $_FILES as $name => $file) {
				if ( !empty($file['name']) && !in_array($name, array('docx', 'xlsx', 'pptx'))) {
					// Generate a unique ID for attached file
					$attachment_id = UUIDv4();

					// File upload configuration
					$config['upload_path']   = CI_UPLOAD_PATH;
					$config['allowed_types'] = array('docx', 'xlsx', 'pptx');
					$config['max_size']      = 4096;
					$config['file_name']     = $attachment_id;

					// Load and initialize upload library
					$this->load->library('upload', $config);
					$this->upload->initialize($config);
					$ext = substr($file['name'], -4);
					// Upload file to server
					if($this->upload->do_upload($name))
					{
						$settings['other'][] = [
							'file' => $attachment_id . '.' . $ext, '',
							'name' => substr($_FILES[$name]['name'], 0, strlen($_FILES[$name]['name']) - 5)
						];
					} else {
						$this->session->set_flashdata('error', 'Ogiltiga filtillägg');
						redirect('/admin/template');
						return;
					}
				}
			}
			if(($json = json_encode($settings)) !== FALSE)
			{
				file_put_contents($this->settingsPath, $json);
			}
			$this->session->set_flashdata('success', 'Data har sparats framgångsrikt!');
			redirect('/admin/template');
		}
		else
		{
			$this->load->view('admin/template/index', $this->data);
		}
	}

	public function delete()
	{
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');

		$validation_rules = [
			[
				'field' => 'confirm_delete',
				'label' => lang('confirm_delete'),
				'rules' => [
					'required'
				]
			],
		];

		$this->form_validation->set_rules($validation_rules);
		if( $this->form_validation->run() === TRUE )
		{
			if(file_exists($this->settingsPath)) {
        if(($json = file_get_contents($this->settingsPath)) !== FALSE)
        {
          if(($array = json_decode($json, true)) !== NULL)
          {
            $settings = $array;
            foreach( array('docx', 'xlsx', 'pptx') as $ext )
            {
              if (array_key_exists($ext, $settings)) {
                unlink($settings[$ext]);
              }
            }
          }
        }
				unlink($this->settingsPath);
      }

			redirect('admin/template');
		}
		else
		{
			$this->load->view('admin/template/delete', $this->data);
		}
	}
}