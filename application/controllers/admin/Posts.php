<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Posts extends Admin_Controller
{
	public function __construct()
	{
		parent::__construct();
		$this->data['body_class'] .= 'skin-white fixed';
		$this->load->model('posts_model');
	}

	public static $types = array(
		'posts'      => 0,
		'categories' => 1,
	);

	private function _get_category_sticky_data()
	{
		$this->data['posts_category_sticky'] = array(
			-1 => lang('posts_category_sticky_top'),
			0  => lang('posts_category_sticky_no'),
			1  => lang('posts_category_sticky_bottom'),
		);
	}

	public function display( $type = 'posts', $id = NULL )
	{
		in_array($type,array_keys(Posts::$types),TRUE) OR show_404();
		$this->VALID_UUIDv4($id,FALSE);

		$this->data['id']       = $id;
		$this->data['type']     = $type;
		$this->data['callback'] = $callback = 'display_' . $type;

		if( in_array($type,['posts']) )
		{
			$this->data['categories'] = $this->posts_model->get_posts_categories($id);

			if( ! empty($this->data['categories']) )
				$this->data['posts'] = $this->posts_model->get_all_posts( array_keys($this->data['categories']), FALSE );
		}

		if( in_array($type,['categories']) )
		{
			$this->data['categories'] = $this->posts_model->get_categories( $id );
			if( $id !== NULL )
				$this->data['category'] = $this->posts_model->get_category( $id );
		}

		$this->data['id'] = $id;
		$this->load->view('admin/posts/' . $this->data['callback'], $this->data);
	}
	// @STEP2: Support hyperlinks, ignore content
	public function create( $type = 'posts' )
	{
		in_array($type,array_keys(Posts::$types),TRUE) OR show_404();

		$this->data['type']     = $type;
		$this->data['callback'] = $callback = 'create_' . $type;
		$this->data['rules']    = '_get_rules_' . $type;

		$this->load->model(['group_model']);
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$this->_get_category_sticky_data();

		if( in_array($type,['posts']) )
		{
			// Get id from POST, so that attachments and text are added to the same document.
			// @STEP2: Store it in $_SESSION and match it for security
			if( $this->input->method(TRUE) === 'POST' )
			{
				if( $id = $this->input->post('uuid_kvalprak') )
				{
					if( $this->VALID_UUIDv4($id) )
					{
						$this->data['uuid_kvalprak'] = $id;
						// $this->data['attachments'] = $this->posts_model->get_attachments($id);
					}
				}
			}
			else
			{
				$this->data['uuid_kvalprak'] = $id = UUIDv4();
			}

			// @TODO: Check if you got ACL to categories
			$this->data['categories'] = $this->posts_model->get_all_categories();
			if( empty($this->data['categories']) ) { redirect('admin/posts/display/categories'); exit; }
			$this->data['categories_groups'] = $this->group_model->get_relationships( 'category_group', 'category_id', array_keys($this->data['categories']['all']) );
			if( !empty($this->data['categories_groups']) )
			{
				foreach($this->data['categories_groups'] as $unset_category_id => $categories_groups)
				{
					if( !acl_group_permits('menu.read', $categories_groups) )
					{
						if( $parent_id = $this->data['categories']['map'][$unset_category_id] )
						{
							unset($this->data['categories'][$parent_id][$unset_category_id]);
							if( empty($this->data['categories'][$parent_id]) )
							{
								unset($this->data['categories']['all'][$parent_id]);
							}
						}
						unset($this->data['categories']['all'][$unset_category_id]);
					}
				}

				if( empty($this->data['categories']['all']) ) { redirect('admin/posts/display/categories'); exit; }
			}
		}

		$validation_rules = $this->{$this->data['rules']}();

		if( in_array($type,['categories']) )
		{
			$this->data['posts_category_parent'] = $this->posts_model->get_category_parents();
			if( ! empty($this->data['posts_category_parent']) )
				$validation_rules = array_merge($validation_rules, $this->_get_rules_parent_id());
		}

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			// Categories
			if( ! isset($id) )
			{
				$id     = UUIDv4();
				$object = NULL;
			}
			// Posts
			else
			{
				$object               = new stdClass();
				$object->post_id      = $id;
				$object->created_by   = $this->auth_user_id;
				$object->created_date = date('Y-m-d H:i:s');
			}

			if( $this->posts_model->create( $id, $type, $callback, $object ) === TRUE )
			{
				redirect('admin/posts/display/' . $type); exit;
			}

			// @STEP2: Give error
			redirect('admin/posts/display/' . $type); exit;
		}
		else
		{
			$this->load->view('admin/posts/' . $this->data['callback'], $this->data);
		}
	}

	public function update( $type = 'posts', $id = NULL )
	{
		in_array($type,array_keys(Posts::$types),TRUE) OR show_404();
		$this->VALID_UUIDv4($id);

		$this->data['type']      = $type;
		$this->data['callback']  = $callback = 'update_' . $type;
		$this->data['rules']     = '_get_rules_' . $type;
		$this->data['parent_id'] = NULL;

		$this->load->model(['group_model']);
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$this->_get_category_sticky_data();

		if( in_array($type,['posts']) )
		{
			$this->data['categories'] = $this->posts_model->get_all_categories();
			if( empty($this->data['categories']) ) { redirect('admin/posts/display/categories'); exit; }

			$this->data['uuid_kvalprak'] = $id;

			$this->data['post'] = $object = $this->posts_model->get_post( $id, FALSE );
			if( empty($this->data['post']) ) { show_404(); }

			$this->data['categories_checked'] = $this->posts_model->get_post_categories($id);
			if( empty($this->data['categories_checked']) ) { show_404(); }

			$this->data['attachments'] = $this->posts_model->get_attachments($id);
			// @TODO: Check if your ACL have removed a category that already have been selected
			$this->data['categories_groups'] = $this->group_model->get_relationships( 'category_group', 'category_id', array_keys($this->data['categories']['all']) );
			if( !empty($this->data['categories_groups']) )
			{
				foreach($this->data['categories_groups'] as $unset_category_id => $categories_groups)
				{
					// @TODO: Change into create instead
					if( !acl_group_permits('menu.read', $categories_groups) )
					{
						if( $parent_id = $this->data['categories']['map'][$unset_category_id] )
						{
							unset($this->data['categories'][$parent_id][$unset_category_id]);
							if( empty($this->data['categories'][$parent_id]) )
							{
								unset($this->data['categories']['all'][$parent_id]);
							}
						}
						unset($this->data['categories']['all'][$unset_category_id]);
					}
				}

				if( empty($this->data['categories']['all']) ) { redirect('admin/posts/display/categories'); exit; }
			}
		}

		$validation_rules = $this->{$this->data['rules']}();

		if( in_array($type,['categories']) )
		{
			$this->data['category'] = $this->posts_model->get_category( $id );
			if( empty($this->data['category']) ) { show_404(); }
			$this->data['categories'] = $this->posts_model->get_categories( $id );

			$this->data['parent_id'] = $this->data['category']->parent_id;

			if( empty($this->data['categories']) )
			{
				$this->data['posts_category_parent'] = $this->posts_model->get_category_parents();
				if( ! empty($this->data['posts_category_parent']) )
				{
					unset($this->data['posts_category_parent'][$this->data['category']->category_id]);
					$validation_rules = array_merge($validation_rules, $this->_get_rules_parent_id());
				}
			}
		}

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			// Categories
			if( in_array($type,['categories']) )
			{
				$object = NULL;
			}
			// Posts
			else
			{
				$object->parent_id = $id;
			}

			if( $this->posts_model->update( $id, $type, $callback, $object ) === TRUE )
			{
				if( $this->data['parent_id'] )
				{
					redirect('admin/posts/display/' . $type . '/' . $this->data['parent_id']); exit;
				}
				else
				{
					redirect('admin/posts/display/' . $type); exit;
				}
			}
		}
		else
		{
			$this->load->view('admin/posts/' . $this->data['callback'], $this->data);
		}
	}

	public function groups( $type = 'categories', $id = NULL )
	{
		in_array($type,array_keys(Posts::$types),TRUE) OR show_404();
		$this->VALID_UUIDv4($id);

		$this->load->model(['group_model']);
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');

		$this->data['category'] = $this->posts_model->get_category( $id );
		if( empty($this->data['category']) ) { show_404(); }

		$this->data['groups'] = $this->group_model->get_all();
		$this->data['groups_checked'] = $this->group_model->get_all_by_relationship( 'category_group', 'category_id', $id, FALSE);

		$config = array(
			array(
				'field' => 'posts_category[]',
				'label' => lang('groups_type_position'),
				'rules' => array(
					'trim',
					'in_list['.implode(',',array_keys($this->data['groups']['position'])).']'
				),
			),
		);

		$this->form_validation->set_rules( $config );
		if( $this->form_validation->run() === TRUE )
		{
			$posts_category = $this->posts_model->_get_posts_category();
			if( $this->group_model->save( 'category_group', 'category_id', $id, $posts_category ) !== FALSE )
			{

			}

			redirect('admin/posts/display/categories/' . $this->data['category']->parent_id); exit;
		}
		else
		{
			$this->load->view('admin/posts/groups_categories', $this->data);
		}
	}

	public function upload()
	{
		if( ! $this->input->is_ajax_request() && $this->input->method(TRUE) !== 'POST' )
			exit;

		// @STEP2: Store it in $_SESSION and match it for security
		if( $post_id = $this->input->post('uuid_kvalprak') )
		{
			if( ! VALID_UUIDv4($post_id) )
			{
				$this->output
						->set_output( 'Okänt fel' )
						->set_status_header(400)
						->_display();
				exit;
			}
			// Upload files
			$upload_base_path = CI_UPLOAD_PATH . 'posts';
			$dir_exists       = TRUE;
			$upload_data      = array();
			if( !empty($_FILES['file']['name']) && $upload_base_path !== FALSE )
			{
				// Create a folder for your company in case it dosen't exists
				$upload_path = $upload_base_path . DIRECTORY_SEPARATOR . $post_id;
				if( !is_dir($upload_path) )
					$dir_exists = mkdir($upload_path, 0777);

				if( $dir_exists )
				{
					// Generate a unique ID for attached file
					$attachment_id = UUIDv4();

					// File upload configuration
					$config['upload_path'] = $upload_path;
					$config['allowed_types'] = $this->config->item('allowed_types');
					$config['file_name'] = $attachment_id;

					// Load and initialize upload library
					$this->load->library('upload', $config);
					$this->upload->initialize($config);

					// Upload file to server
					if( ! $this->upload->do_upload('file') ){
						$this->output
								->set_output( $this->upload->display_errors('','') )
								->set_status_header(400)
								->_display();
						exit;
					}
					else
					{
						// Uploaded file data
						$file_data = $this->upload->data();
						$upload_data['post_id']       = UUID_TO_BIN($post_id);
						$upload_data['attachment_id'] = UUID_TO_BIN($attachment_id);
						$upload_data['file_name']     = $file_data['client_name'] !== '' ? $file_data['client_name'] : $file_data['orig_name'];
						$upload_data['file_ext']      = $file_data['file_ext'];
						$upload_data['uploaded_on']   = date("Y-m-d H:i:s");

						if( ! empty($upload_data) )
						{
							// Insert files data into the database
							if( $this->posts_model->save_attachments($upload_data) )
							{
								$this->output
										->set_content_type('application/json', 'utf-8')
										->set_output( json_encode([
											'file_name'     => $upload_data['file_name'],
											'uploaded_on'   => $upload_data['uploaded_on'],
											'attachment_id' => $attachment_id,
											'response'      => 'OK'
										], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES) )
										->set_status_header(200)
										->_display();
								exit;
							}
							else
							{
								$this->output
										->set_output( 'Kunde inte spara filen' )
										->set_status_header(400)
										->_display();
								exit;
								// @STEP2: Delete file from DB
							}
						}
					}
				}
			}
		}

		$this->output
				->set_output( 'Okänt fel' )
				->set_status_header(400)
				->_display();
		exit;
	}
	// @STEP2: Secure get_post
	public function download( $attachment_id )
	{
		$this->VALID_UUIDv4($attachment_id);
		$attachment = $this->posts_model->get_attachment( $attachment_id );
		if( empty($attachment) ) { show_404(); }

		$post = $this->posts_model->get_post( $attachment->post_id, FALSE );
		if( empty($post) ) { show_404(); }

		$upload_base_path = CI_UPLOAD_PATH . 'posts';
		$upload_path = $upload_base_path . DIRECTORY_SEPARATOR . $attachment->post_id;
		$upload_file = $upload_path . DIRECTORY_SEPARATOR . $attachment->attachment_id . $attachment->file_ext;

		if( ! file_exists($upload_file) )
			show_404();

		if($attachment->file_ext === '.pdf')
		{
			$this->load->helper('pdf');
			pdf($attachment, $upload_file);
		}
		else
		{
			$this->load->helper('download');
			force_download($attachment->file_name, file_get_contents($upload_file), TRUE);
		}
	}

	// @TODO: If deleting a category, move all posts to a diffrent one.
	public function delete( $type = 'posts', $id = NULL )
	{
		in_array($type,['posts','categories','attachments'],TRUE) OR show_404();
		$this->VALID_UUIDv4($id,FALSE);

		$callback = 'delete_' . $type;
		$this->{$callback}($id);
	}

	// @STEP2: Secure delete
	public function delete_attachments()
	{
		if( ! $this->input->is_ajax_request() && $this->input->method(TRUE) !== 'POST' )
			exit;

		$attachment_id = $this->input->post('id');
		$attachment    = $this->posts_model->get_attachment( $attachment_id );
		if( ! empty($attachment) )
		{
			if( $attachment->post_id === $this->input->post('uuid_kvalprak') )
			{
				$upload_base_path = CI_UPLOAD_PATH . 'posts';
				$upload_path = $upload_base_path . DIRECTORY_SEPARATOR . $attachment->post_id;
				$upload_file = $upload_path . DIRECTORY_SEPARATOR . $attachment->attachment_id . $attachment->file_ext;

				if( file_exists($upload_file) )
				{
					if( $this->posts_model->delete_attachments( $attachment->attachment_id ) )
					{
						if( unlink($upload_file) )
						{
							$this->output
									->set_status_header(200)
									->set_content_type('application/json', 'utf-8')
									->set_output(json_encode(['result' => TRUE], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
									->_display();
							exit;
						}
					}
				}
			}
		}

		$this->output
				->set_status_header(404)
				->set_content_type('application/json', 'utf-8')
				->set_output(json_encode(['result' => FALSE], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
				->_display();
		exit;
	}

	public function elfinder()
	{
		// Delete cache
		$cache = CI_UPLOAD_PATH . 'elfinder' . DIRECTORY_SEPARATOR . '.cache';

		if(file_exists($cache))
			unlink($cache);

		// elFinder autoload
		require APPPATH .'/libraries/elfinder/autoload.php';

		$opts = array(
			'debug' => true,
			'roots' => array(
				// Items volume
				array(
					'driver'        => 'LocalFileSystem',                               // driver for accessing file system (REQUIRED)
					'path'          => CI_UPLOAD_PATH . 'elfinder/',                    // path to files (REQUIRED)
					'URL'           => '/resources/uploads/',                           // URL to files (REQUIRED)
					'trashHash'     => 't1_Lw',                                         // elFinder's hash of trash folder
					'winHashFix'    => DIRECTORY_SEPARATOR !== '/',                     // to make hash same to Linux one on windows too
					'uploadDeny'    => array('all'),                                    // All Mimetypes not allowed to upload
					'uploadAllow'   => array('image', 'text/plain', 'application/pdf'), // Mimetype `image` and `text/plain` allowed to upload
					'uploadOrder'   => array('deny', 'allow'),                          // allowed Mimetype `image` and `text/plain` only
					'accessControl' => array($this, 'elfinderAccess'),                  // disable and hide dot starting files (OPTIONAL)
					'uploadOverwrite' => false,
				),
				// Trash volume
				array(
					'id'            => '1',
					'driver'        => 'Trash',
					'path'          => CI_UPLOAD_PATH . 'elfinder/.trash/',
					'tmbURL'        => base_url('/resources/uploads/.trash/.tmb/'),
					'winHashFix'    => DIRECTORY_SEPARATOR !== '/',                     // to make hash same to Linux one on windows too
					'uploadDeny'    => array('all'),                                    // Recomend the same settings as the original volume that uses the trash
					'uploadAllow'   => array('image', 'text/plain', 'application/pdf'), // Same as above
					'uploadOrder'   => array('deny', 'allow'),                          // Same as above
					'accessControl' => array($this, 'elfinderAccess'),                  // Same as above
				)
			)
		);

		$connector = new elFinderConnector(new elFinder($opts));
		$connector->run();
	}

	/**
	 * Simple function to demonstrate how to control file access using "accessControl" callback.
	 * This method will disable accessing files/folders starting from '.' (dot)
	 *
	 * @param  string    $attr    attribute name (read|write|locked|hidden)
	 * @param  string    $path    absolute file path
	 * @param  string    $data    value of volume option `accessControlData`
	 * @param  object    $volume  elFinder volume driver object
	 * @param  bool|null $isDir   path is directory (true: directory, false: file, null: unknown)
	 * @param  string    $relpath file path relative to volume root directory started with directory separator
	 * @return bool|null
	 **/
	public function elfinderAccess($attr, $path, $data, $volume, $isDir, $relpath) {
		$basename = basename($path);
		return $basename[0] === '.'                  // if file/folder begins with '.' (dot)
				 && strlen($relpath) !== 1           // but with out volume root
			? !($attr == 'read' || $attr == 'write') // set read+write to false, other (locked+hidden) set to true
			:  null;                                 // else elFinder decide it itself
	}

	private function _get_rules_posts()
	{
		return array(
			array(
				'field' => 'posts_name',
				'label' => lang('posts_name'),
				'rules' => array(
					'trim',
					'required',
					'min_length[3]',
					'max_length[256]', // text
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']',
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'posts_publish',
				'label' => lang('posts_publish'),
				'rules' => array(
					'trim',
					'required',
					'regex_match['.$this->config->item('error_date').']',
				),
				'errors' => array(
					'regex_match' => lang('error_date')
				)
			),
			array(
				'field' => 'posts_unpublish',
				'label' => lang('posts_unpublish'),
				'rules' => array(
					'trim',
					'regex_match['.$this->config->item('error_date').']',
				),
				'errors' => array(
					'regex_match' => lang('error_date')
				)
			),
			array(
				'field' => 'posts_category[]',
				'label' => lang('posts_category'),
				'rules' => array(
					'trim',
					'required',
					'in_list['.implode(',',array_keys($this->data['categories']['all'])).']'
				)
			),
		);
	}

	private function _get_rules_parent_id()
	{
		return array(
			array(
				'field' => 'posts_category_parent',
				'label' => lang('posts_category_parent'),
				'rules' => array(
					'trim',
					'in_list['.implode(',',array_keys($this->data['posts_category_parent'])).']'
				),
			),
		);
	}

	private function _get_rules_categories()
	{
		return array(
			array(
				'field' => 'posts_name',
				'label' => lang('posts_name'),
				'rules' => array(
					'trim',
					'required',
					'min_length[3]',
					'max_length[64]', // 64
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']',
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'posts_category_slug',
				'label' => lang('posts_category_slug'),
				'rules' => array(
					'trim',
					'required',
					'min_length[3]',
					'max_length[64]', // 64
					'regex_match['.$this->config->item('error_alpha_numeric_dash').']',
					array('is_unique', array( $this, '_is_unique_category_slug' ) )
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_dash')
				)
			),
			array(
				'field' => 'posts_category_sticky',
				'label' => lang('posts_category_sticky'),
				'rules' => array(
					'trim',
					'in_list['.implode(',',array_keys($this->data['posts_category_sticky'])).']'
				)
			),
		);
	}

	public function _is_unique_category_slug()
	{
		$category = ! empty($this->data['category']) ? $this->data['category'] : NULL;
		return $this->posts_model->is_unique_category_slug( $category );
	}

}