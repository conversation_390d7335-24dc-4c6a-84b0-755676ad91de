<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Education extends Admin_Controller
{
	public function __construct()
	{
		parent::__construct();
		$this->data['body_class'] .= 'skin-white fixed';
		$this->load->model(['group_model','menu_model','folder_model','document_model','education_model']);
	}
	
    public function index()
	{
		$this->view();
    }
	
	public function view( )
	{
		$this->data['groups'] = $this->group_model->get_all([],'education');
		$this->load->view('admin/education/view',$this->data);
	}
	
    public function create()
	{
		$this->data['groups_type'] = array(
			'education'   => lang('groups_type_education'),
		);
		
		$this->data['move']['menu'] = $this->menu_model->get_all();
		// var_dump($this->data['move']['menu']);exit;
		$this->data['move']['folder'] = $this->folder_model->get_all($this->data['move']['menu']['id'], TRUE);
		// var_dump($this->data['move']['folder']);exit;
		$this->data['move']['document'] = $this->document_model->get_all_documents_in_folder(array_keys($this->data['move']['folder']['id']));
		if( empty($this->data['move']['document']) ) { redirect('admin/education'); exit; }
		
		$keep = [];
		foreach($this->data['move']['folder']['id'] AS $folder_id => $menu_id)
		{
			if(
				in_array($folder_id, $this->data['move']['document']['folders']) && 
				isset($this->data['move']['menu']['parent'][$menu_id]) )
			{
				$parent_id = $this->data['move']['menu']['parent'][$menu_id];
				if( isset($this->data['move']['menu'][$parent_id]) )
				{
					$keep[] = $menu_id;
					$keep[] = $parent_id;
				}
				
				if(isset($this->data['move']['menu']['parent'][$parent_id]))
				{
					$top_menu = $this->data['move']['menu']['parent'][$parent_id];
					if( isset($this->data['move']['menu'][$top_menu]) )
					{
						$keep[] = $top_menu;
					}
				}
			}
		}
		
		$this->data['move']['menu']['loop'] = $keep;
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$validation_rules = $this->_get_rules();

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			$group_id = UUIDv4();
			
			if ( $this->group_model->create( $group_id ) === TRUE )
			{
				if( $this->education_model->save( 'group_id', $group_id ) )
				{
					
				}
			}
			
			redirect('admin/education'); exit;
		}
		else
		{
			$this->load->view('admin/education/create',$this->data);
		}
	}
	
    public function update( $group_id )
	{
		$this->VALID_UUIDv4($group_id);
		
		$this->data['group'] = $this->group_model->get($group_id);
		if( empty($this->data['group']) ) { show_404(); }
		if( $this->data['group']->company_id !== $this->auth_company_id ) { show_404(); }
		
		$this->data['move']['menu'] = $this->menu_model->get_all();
		$this->data['move']['folder'] = $this->folder_model->get_all($this->data['move']['menu']['id'], TRUE);
		$this->data['move']['document'] = $this->document_model->get_all_documents_in_folder(array_keys($this->data['move']['folder']['id']));
		
		$keep = [];
		foreach($this->data['move']['folder']['id'] AS $folder_id => $menu_id)
		{
			if(
				in_array($folder_id, $this->data['move']['document']['folders']) && 
				isset($this->data['move']['menu']['parent'][$menu_id]) )
			{
				$parent_id = $this->data['move']['menu']['parent'][$menu_id];
				if( isset($this->data['move']['menu'][$parent_id]) )
				{
					$keep[] = $menu_id;
					$keep[] = $parent_id;
				}
				
				if(isset($this->data['move']['menu']['parent'][$parent_id]))
				{
					$top_menu = $this->data['move']['menu']['parent'][$parent_id];
					if( isset($this->data['move']['menu'][$top_menu]) )
					{
						$keep[] = $top_menu;
					}
				}
			}
		}
		
		$this->data['move']['menu']['loop'] = $keep;
		
		// Load selected
		$this->data['document']['selected'] = $this->education_model->get_relationships( 'group_id', $group_id );
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$validation_rules = $this->_get_rules_update();
		
		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			if ( $this->group_model->update( $this->data['group'] ) === TRUE )
			{
				if( $this->education_model->save( 'group_id', $group_id ) )
				{
					
				}
			}
			
			redirect('admin/education'); exit;
		}
		else
		{
			$this->load->view('admin/education/update',$this->data);
		}
	}
	public function groups( $group_id )
	{
		$this->VALID_UUIDv4($group_id);
		
		$this->data['group'] = $this->group_model->get($group_id);
		if( empty($this->data['group']) ) { show_404(); }
		if( $this->data['group']->company_id !== $this->auth_company_id ) { show_404(); }
		
		$this->data['groups'] = $this->group_model->get_all([],'position');
		$this->data['groups_checked'] = $this->group_model->get_all_by_relationship( 'document_education_group', 'education_id', $group_id, FALSE);
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$validation_rules = $this->_get_rules_groups();
		
		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			if( $this->group_model->save( 'document_education_group', 'education_id', $group_id, $this->education_model->_get_education_position() ) !== FALSE )
			{
				
			}
			
			redirect('admin/education'); exit;
		}
		else
		{
			$this->load->view('admin/education/groups',$this->data);
		}
	}
	public function users()
	{
		// $this->data['users'] = $this->user_model->get_all();
		$this->load->view('admin/education/users',$this->data);
		// var_dump($this->groups['types']);
	}
	public function individual( $user_id )
	{
		$this->VALID_UUIDv4($user_id);

		$this->data['user'] = $this->user_model->get($user_id);
		if( empty($this->data['user']) ) { show_404(); }
		unset($this->data['user']->passwd);
		if( $this->data['user']->company_id !== $this->auth_company_id ) { show_404(); }
		
		$this->data['groups'] = $this->group_model->get_all([],'education');
		$this->data['groups_checked'] = $this->group_model->get_all_by_relationship( 'user_group', 'user_id', $user_id, FALSE);
		
		$this->data['move']['menu'] = $this->menu_model->get_all();
		$this->data['move']['folder'] = $this->folder_model->get_all($this->data['move']['menu']['id'], TRUE);
		$this->data['move']['document'] = $this->document_model->get_all_documents_in_folder(array_keys($this->data['move']['folder']['id']));
		if( empty($this->data['move']['document']) ) { redirect('admin/education/users'); exit; }
		
		$keep = [];
		foreach($this->data['move']['folder']['id'] AS $folder_id => $menu_id)
		{
			if(
				in_array($folder_id, $this->data['move']['document']['folders']) && 
				isset($this->data['move']['menu']['parent'][$menu_id]) )
			{
				$parent_id = $this->data['move']['menu']['parent'][$menu_id];
				if( isset($this->data['move']['menu'][$parent_id]) )
				{
					$keep[] = $menu_id;
					$keep[] = $parent_id;
				}
				
				if(isset($this->data['move']['menu']['parent'][$parent_id]))
				{
					$top_menu = $this->data['move']['menu']['parent'][$parent_id];
					if( isset($this->data['move']['menu'][$top_menu]) )
					{
						$keep[] = $top_menu;
					}
				}
			}
		}
		
		$this->data['move']['menu']['loop'] = $keep;
		
		// Load selected
		$this->data['document']['selected'] = $this->education_model->get_relationships( 'user_id', $user_id );
		
		// var_dump($this->groups['types']);exit;
		// var_dump($this->data['groups']);
		// var_dump($this->data['groups_checked']);
		// var_dump($this->data['move']['document']);
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$validation_rules = $this->_get_rules_individual();

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			// Save user groups
			if( ! empty(array_filter($this->data['groups'])) )
			{
				$groups = $this->education_model->_get_education_position();
				$this->group_model->save( 'user_group', 'user_id', $user_id, $groups, array_keys($this->data['groups']['education']) );
			}
			
			if( $this->education_model->save( 'user_id', $user_id ) )
			{
				
			}
			
			redirect('admin/education/users'); exit;
		}
		else
		{
			$this->load->view('admin/education/individual',$this->data);
		}
	}
	
    public function delete( $group_id )
	{
		$this->VALID_UUIDv4($group_id);
		
		$this->data['group'] = $this->group_model->get($group_id);
		if( empty($this->data['group']) ) { show_404(); }
		if( $this->data['group']->company_id !== $this->auth_company_id ) { show_404(); }
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$validation_rules = $this->_get_rules_delete();
		
		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			$this->education_model->delete($group_id);
			
			redirect('admin/education'); exit;
		}
		else
		{
			$this->load->view('admin/education/delete',$this->data);
		}
	}
	
	public function _is_unique()
	{
		return $this->group_model->is_unique();
	}
	
	public function _is_unique_update()
	{
		return $this->group_model->is_unique_update( $this->data['group'] );
	}

	public function unmark($document_id) {
		$this->VALID_UUIDv4($document_id);
		$this->load->model('education_model');
		$version = $this->education_model->unmark( $document_id );
		redirect('reports/education#document');
	}
	
	private function _get_rules()
	{
		return array(
			array(
				'field' => 'groups_name',
				'label' => lang('groups_name'),
				'rules' => array(
					'trim',
					'required',
					'min_length[3]',
					'max_length[80]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']',
					array('is_unique', array( $this, '_is_unique' ) )
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'groups_type',
				'label' => lang('groups_type'),
				'rules' => array(
					'trim',
					'required',
					'in_list['.implode(',',array_keys($this->data['groups_type'])).']'
				)
			),
			array(
				'field' => 'tree_documents[]',
				'label' => lang('documents_document'),
				'rules' => array(
					'trim',
					'required',
					'in_list['.implode(',',$this->data['move']['document']['version']).']'
				)
			)
		);
	}
	
	private function _get_rules_update()
	{
		return array(
			array(
				'field' => 'groups_name',
				'label' => lang('groups_name'),
				'rules' => array(
					'trim',
					'required',
					'min_length[3]',
					'max_length[80]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']',
					array('is_unique', array( $this, '_is_unique_update' ) )
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'tree_documents[]',
				'label' => lang('documents_document'),
				'rules' => array(
					'trim',
					'required',
					'in_list['.implode(',',$this->data['move']['document']['version']).']'
				)
			)
		);
	}
	
	private function _get_rules_groups()
	{
		return array(
			array(
				'field' => 'education_position[]',
				'label' => lang('education_type_position'),
				'rules' => array(
					'in_list['.implode(',',array_keys($this->data['groups']['position'])).']'
				)
			),
		);
	}
	
	private function _get_rules_delete()
	{
		return array(
			array(
				'field' => 'confirm_delete',
				'label' => lang('confirm_delete'),
				'rules' => array(
					'required'
				)
			),
		);
	}
	
	private function _get_rules_individual()
	{
		return array(
			array(
				'field' => 'tree_documents[]',
				'label' => lang('documents_document'),
				'rules' => array(
					'trim',
					'in_list['.implode(',',$this->data['move']['document']['version']).']'
				)
			),
			array(
				'field' => 'education_position[]',
				'label' => lang('education_type_position'),
				'rules' => array(
					'in_list['.implode(',',array_keys($this->data['groups']['education'])).']'
				)
			),
		);
	}
}
