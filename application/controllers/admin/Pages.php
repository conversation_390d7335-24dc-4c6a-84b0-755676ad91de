<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Pages extends Admin_Controller
{
	public function __construct()
	{
		parent::__construct();
		$this->data['body_class'] .= 'skin-white fixed';
		$this->load->model('posts_model');
	}

	private function _get_layout_data()
	{
		$this->data['layout'] = array(
			'folder' => lang('pages_layout_folder'),
			'tags'   => lang('pages_layout_tags'),
		);
	}

	private function _get_status_data()
	{
		$this->data['status'] = array(
			'inactive' => lang('pages_inactive'),
			'private'  => lang('pages_private'),
		);

		if( $this->auth_god )
		{
			$this->data['status'] = array_merge($this->data['status'],[
				'public'   => lang('pages_public'),
			]);
		}
	}

	private function _get_position_data()
	{
		$this->data['position'] = array(
			0 => lang('pages_position_no_link'),
			1 => lang('pages_position_apps'),
			2 => lang('pages_position_homepage'),
			3 => lang('pages_position_notice_board'),
		);
	}

	public function display( $id = NULL )
	{
		$this->VALID_UUIDv4($id,FALSE);
		$this->_get_layout_data();
		$this->_get_status_data();
		$this->_get_position_data();

		$this->data['categories'] = $this->posts_model->get_pages_categories($id);
		$this->data['pages'] = $this->posts_model->get_all_pages();

		$this->data['id'] = $id;
		$this->load->view('admin/pages/display', $this->data);
	}

	public function create()
	{
		$this->load->model(['group_model']);
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$this->_get_layout_data();
		$this->_get_status_data();
		$this->_get_position_data();

		// Get id from POST, so that attachments and text are added to the same document.
		// @STEP2: Store it in $_SESSION and match it for security
		if( $this->input->method(TRUE) === 'POST' )
		{
			if( $id = $this->input->post('uuid_kvalprak') )
			{
				if( $this->VALID_UUIDv4($id) )
				{
					$this->data['uuid_kvalprak'] = $id;
					$this->data['attachments'] = $this->posts_model->get_attachments($id, 'page');
				}
			}
		}
		else
		{
			$this->data['uuid_kvalprak'] = $id = UUIDv4();
		}

		$this->data['categories'] = $this->posts_model->get_all_categories();
		if( empty($this->data['categories']) ) { redirect('admin/posts/display/categories'); exit; }

		$this->data['categories_groups'] = $this->group_model->get_relationships( 'category_group', 'category_id', array_keys($this->data['categories']['all']) );
		if( !empty($this->data['categories_groups']) )
		{
			foreach($this->data['categories_groups'] as $unset_category_id => $categories_groups)
			{
				if( !acl_group_permits('menu.read', $categories_groups) )
				{
					if( $parent_id = $this->data['categories']['map'][$unset_category_id] )
					{
						unset($this->data['categories'][$parent_id][$unset_category_id]);
						if( empty($this->data['categories'][$parent_id]) )
						{
							unset($this->data['categories']['all'][$parent_id]);
						}
					}
					unset($this->data['categories']['all'][$unset_category_id]);
				}
			}

			if( empty($this->data['categories']['all']) ) { redirect('admin/posts/display/categories'); exit; }
		}

		$validation_rules = $this->_get_rules_pages();

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			$object               = new stdClass();
			$object->page_id      = $id;
			$object->created_by   = $this->auth_user_id;
			$object->created_date = date('Y-m-d H:i:s');

			if( $this->posts_model->save_page( $id, $object ) === TRUE )
			{
				redirect('admin/pages'); exit;
			}

			// @STEP2: Give error
			redirect('admin/pages'); exit;
		}
		else
		{
			$this->load->view('admin/pages/create', $this->data);
		}
	}

	public function update( $id = NULL )
	{
		$this->VALID_UUIDv4($id);

		$this->load->model(['group_model']);
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$this->_get_layout_data();
		$this->_get_status_data();
		$this->_get_position_data();

		$this->data['categories'] = $this->posts_model->get_all_categories();
		if( empty($this->data['categories']) ) { redirect('admin/posts/display/categories'); exit; }
		// @TODO: Check if your ACL have removed a category that already have been selected
		$this->data['categories_groups'] = $this->group_model->get_relationships( 'category_group', 'category_id', array_keys($this->data['categories']['all']) );
		if( !empty($this->data['categories_groups']) )
		{
			foreach($this->data['categories_groups'] as $unset_category_id => $categories_groups)
			{
				// @TODO: Change into create instead
				if( !acl_group_permits('menu.read', $categories_groups) )
				{
					if( $parent_id = $this->data['categories']['map'][$unset_category_id] )
					{
						unset($this->data['categories'][$parent_id][$unset_category_id]);
						if( empty($this->data['categories'][$parent_id]) )
						{
							unset($this->data['categories']['all'][$parent_id]);
						}
					}
					unset($this->data['categories']['all'][$unset_category_id]);
				}
			}

			if( empty($this->data['categories']['all']) ) { redirect('admin/posts/display/categories'); exit; }
		}

		$this->data['uuid_kvalprak'] = $id;

		$this->data['page'] = $object = $this->posts_model->get_page( $id, FALSE );
		if( empty($this->data['page']) ) { show_404(); }

		$this->data['categories_checked'] = $this->posts_model->get_page_categories($id);
		$this->data['attachments'] = $this->posts_model->get_attachments($id, 'page');

		$validation_rules = $this->_get_rules_pages();

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			$object->parent_id = $id;

			if( $this->posts_model->save_page( $id, $object ) === TRUE )
			{
				redirect('admin/pages'); exit;
			}
		}
		else
		{
			$this->load->view('admin/pages/update', $this->data);
		}
	}

	public function upload()
	{
		if( ! $this->input->is_ajax_request() && $this->input->method(TRUE) !== 'POST' )
			exit;

		// @STEP2: Store it in $_SESSION and match it for security
		if( $page_id = $this->input->post('uuid_kvalprak') )
		{
			if( ! VALID_UUIDv4($page_id) )
			{
				$this->output
						->set_output( 'Okänt fel' )
						->set_status_header(400)
						->_display();
				exit;
			}
			// Upload files
			$upload_base_path = CI_UPLOAD_PATH . 'pages';
			$dir_exists       = TRUE;
			$upload_data      = array();
			if( !empty($_FILES['file']['name']) && $upload_base_path !== FALSE )
			{
				// Create a folder for your company in case it dosen't exists
				$upload_path = $upload_base_path . DIRECTORY_SEPARATOR . $page_id;
				if( !is_dir($upload_path) )
					$dir_exists = mkdir($upload_path, 0777);

				if( $dir_exists )
				{
					// Generate a unique ID for attached file
					$attachment_id = UUIDv4();

					// File upload configuration
					$config['upload_path'] = $upload_path;
					$config['allowed_types'] = $this->config->item('allowed_types');
					$config['file_name'] = $attachment_id;

					// Load and initialize upload library
					$this->load->library('upload', $config);
					$this->upload->initialize($config);

					// Upload file to server
					if( ! $this->upload->do_upload('file') ){
						$this->output
								->set_output( $this->upload->display_errors('','') )
								->set_status_header(400)
								->_display();
						exit;
					}
					else
					{
						// Uploaded file data
						$file_data = $this->upload->data();
						$upload_data['page_id']       = UUID_TO_BIN($page_id);
						$upload_data['attachment_id'] = UUID_TO_BIN($attachment_id);
						$upload_data['file_name']     = $file_data['client_name'] !== '' ? $file_data['client_name'] : $file_data['orig_name'];
						$upload_data['file_ext']      = $file_data['file_ext'];
						$upload_data['uploaded_on']   = date("Y-m-d H:i:s");

						if( ! empty($upload_data) )
						{
							// Insert files data into the database
							if( $this->posts_model->save_attachments($upload_data, 'page') )
							{
								$this->output
										->set_content_type('application/json', 'utf-8')
										->set_output( json_encode([
											'file_name'     => $upload_data['file_name'],
											'uploaded_on'   => $upload_data['uploaded_on'],
											'attachment_id' => $attachment_id,
											'response'      => 'OK'
										], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES) )
										->set_status_header(200)
										->_display();
								exit;
							}
							else
							{
								$this->output
										->set_output( 'Kunde inte spara filen' )
										->set_status_header(400)
										->_display();
								exit;
								// @STEP2: Delete file from DB
							}
						}
					}
				}
			}
		}

		$this->output
				->set_output( 'Okänt fel' )
				->set_status_header(400)
				->_display();
		exit;
	}

	public function download( $attachment_id )
	{
		$this->VALID_UUIDv4($attachment_id);
		$attachment = $this->posts_model->get_attachment( $attachment_id, 'page' );
		if( empty($attachment) ) { show_404(); }

		$page = $this->posts_model->get_page($attachment->page_id);
		if( empty($page) ) { show_404(); }
		if( $page->company_id !== $this->auth_company_id ) { show_404(); }

		$upload_base_path = CI_UPLOAD_PATH . 'pages';
		$upload_path = $upload_base_path . DIRECTORY_SEPARATOR . $attachment->page_id;
		$upload_file = $upload_path . DIRECTORY_SEPARATOR . $attachment->attachment_id . $attachment->file_ext;

		if( ! file_exists($upload_file) )
			show_404();

		if($attachment->file_ext === '.pdf')
		{
			$this->load->helper('pdf');
			pdf($attachment, $upload_file);
		}
		else
		{
			$this->load->helper('download');
			force_download($attachment->file_name, file_get_contents($upload_file), TRUE);
		}
	}

	// @TODO: If deleting a category, move all posts to a diffrent one.
	public function delete( $type = 'pages', $id = NULL )
	{
		in_array($type,['pages','attachments'],TRUE) OR show_404();
		$this->VALID_UUIDv4($id,FALSE);

		$callback = 'delete_' . $type;
		$this->{$callback}($id);
	}

	// @STEP2: Secure delete
	public function delete_attachments()
	{
		if( ! $this->input->is_ajax_request() && $this->input->method(TRUE) !== 'POST' )
			exit;

		$attachment_id = $this->input->post('id');
		$attachment    = $this->posts_model->get_attachment( $attachment_id, 'page' );
		if( ! empty($attachment) )
		{
			if( $attachment->page_id === $this->input->post('uuid_kvalprak') )
			{
				$upload_base_path = CI_UPLOAD_PATH . 'pages';
				$upload_path = $upload_base_path . DIRECTORY_SEPARATOR . $attachment->page_id;
				$upload_file = $upload_path . DIRECTORY_SEPARATOR . $attachment->attachment_id . $attachment->file_ext;

				if( file_exists($upload_file) )
				{
					if( $this->posts_model->delete_attachments( $attachment->attachment_id, 'page' ) === TRUE )
					{
						if( unlink($upload_file) )
						{
							$this->output
									->set_status_header(200)
									->set_content_type('application/json', 'utf-8')
									->set_output(json_encode(['result' => TRUE], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
									->_display();
							exit;
						}
					}
				}
			}
		}

		$this->output
				->set_status_header(404)
				->set_content_type('application/json', 'utf-8')
				->set_output(json_encode(['result' => FALSE], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
				->_display();
		exit;
	}

	private function _get_rules_pages()
	{
		return array(
			array(
				'field' => 'posts_name',
				'label' => lang('posts_name'),
				'rules' => array(
					'trim',
					'required',
					'min_length[3]',
					'max_length[256]', // text
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']',
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'pages_pageicon',
				'label' => lang('pages_pageicon'),
				'rules' => array(
					'trim',
					'required',
					'regex_match['.$this->config->item('error_alpha_numeric_dash').']',
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_dash')
				)
			),
			array(
				'field' => 'pages_href',
				'label' => lang('pages_href'),
				'rules' => array(
					'trim'
				)
			),
			array(
				'field' => 'pages_position',
				'label' => lang('pages_position'),
				'rules' => array(
					'trim',
					'required',
					'in_list['.implode(',',array_keys($this->data['position'])).']'
				)
			),
			array(
				'field' => 'pages_layout',
				'label' => lang('pages_layout'),
				'rules' => array(
					'trim',
					'required',
					'in_list['.implode(',',array_keys($this->data['layout'])).']'
				)
			),
			array(
				'field' => 'pages_status',
				'label' => lang('pages_status'),
				'rules' => array(
					'trim',
					'required',
					'in_list['.implode(',',array_keys($this->data['status'])).']'
				)
			),
			array(
				'field' => 'posts_category[]',
				'label' => lang('posts_category'),
				'rules' => array(
					'trim',
					'in_list['.implode(',',array_keys($this->data['categories']['all'])).']'
				)
			),
		);
	}

}