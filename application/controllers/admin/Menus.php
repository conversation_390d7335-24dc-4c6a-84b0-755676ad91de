<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Menus extends Admin_Controller
{
	function __construct()
	{
		parent::__construct();
		
		// Load dependencies
		$this->load->model('menu_model');
		$this->load->model('folder_model');
		$this->load->model('group_model');
		$this->data['body_class']   .= 'skin-white fixed';
		$this->data['menu']          = NULL;
		$this->data['menu_id']       = NULL;
		$this->data['folder']        = NULL;
		$this->data['folder_id']     = NULL;
		$this->data['sub_folder']    = NULL;
		$this->data['sub_folder_id'] = NULL;
		$this->data['folders']       = [];
	}
	
	public static $types = array(
		'menu'   => 0,
		'folder' => 1,
		'link'   => 2,
		'app'    => 3
	);
	
	private function _get_multiple_folders_data( $folder_id = NULL, $sub_folder_id = NULL, $menu_id = NULL )
	{
		if( $folder_id )
		{
			$this->data['folder'] = $this->menu_model->get($folder_id);
			if( empty($this->data['folder']) ) { show_404(); }
			if( $this->data['folder']->company_id !== $this->auth_company_id ) { show_404(); }
		}
		
		if( $sub_folder_id )
		{
			$this->data['sub_folder'] = $this->menu_model->get($sub_folder_id);
			if( empty($this->data['sub_folder']) ) { show_404(); }
			if( $this->data['sub_folder']->company_id !== $this->auth_company_id ) { show_404(); }
		}
		
		if( $menu_id )
		{
			$this->data['menu'] = $this->menu_model->get($menu_id);
			if( empty($this->data['menu']) ) { show_404(); }
			if( $this->data['menu']->company_id !== $this->auth_company_id ) { show_404(); }
		}
		
		if( $this->data['folder'] )
			$this->data['folder_id']     = $this->data['folder']->menu_id;
		if( $this->data['sub_folder'] )
			$this->data['sub_folder_id'] = $this->data['sub_folder']->menu_id;
		if( $this->data['menu'] )
			$this->data['menu_id']       = $this->data['menu']->menu_id;
	}
	
	private function _get_sticky_data()
	{
		$this->data['menus_sticky'] = array(
			-1 => lang('menus_sticky_top'),
			0  => lang('menus_sticky_no'),
			1  => lang('menus_sticky_bottom'),
		);
	}
	
	private function view_default()
	{
		$this->data['buttons'] = array(
			'folder'
		);

		$this->data['menus'] = $this->menu_model->get_all_by_parent_id();
		$this->load->view('admin/menus/view',$this->data);
	}
	
    public function index()
	{
		$this->view_default();
    }
	
	public function view( $folder_id = NULL, $sub_folder_id = NULL )
	{
		$this->VALID_UUIDv4($folder_id);
		$this->VALID_UUIDv4($sub_folder_id,FALSE);
		$this->_get_multiple_folders_data($folder_id,$sub_folder_id);
		
		$this->data['buttons'] = array(
			'menu',
			'folder',
			'link',
			// 'app'
		);
		
		if( $sub_folder_id )
			$this->data['menus'] = $this->menu_model->get_all_by_parent_id($this->data['sub_folder_id']);
		else
			$this->data['menus'] = $this->menu_model->get_all_by_parent_id($this->data['folder_id']);
		
		if( ! empty($this->data['menus']) ) {
			$this->data['folders'] = $this->folder_model->get_all(array_keys($this->data['menus']));
		}
		
		$this->load->view('admin/menus/view',$this->data);
	}
	
    public function create( $type = 'folder', $folder_id = NULL, $sub_folder_id = NULL )
	{
		in_array($type,array_keys(Menus::$types),TRUE) OR show_404();
		
		$this->VALID_UUIDv4($folder_id,FALSE);
		$this->VALID_UUIDv4($sub_folder_id,FALSE);
		$this->_get_multiple_folders_data($folder_id,$sub_folder_id);
		$this->_get_sticky_data();
		
		$parent_id = NULL;
		
		if( $this->data['folder_id'] )
			$parent_id = $this->data['folder_id'];
		
		if( $this->data['sub_folder_id'] )
			$parent_id = $this->data['sub_folder_id'];
		
		$this->data['type']     = $type;
		$this->data['callback'] = $callback = 'create_' . $type;
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$this->load->model('group_model');
		
		$this->data['groups'] = $this->group_model->get_all();

		if( $this->config->item('document_author') && $type === 'menu' )
		{
			$this->load->model('acl_model');
			$acl_menu = $this->acl_model->get_like( 'menu' );
			if( empty($acl_menu) ) { show_404(); }

			unset($acl_menu['create']);
			unset($acl_menu['update']);
			unset($acl_menu['delete']);
			unset($acl_menu['folder']);
			unset($acl_menu['all']);

			// @TODO: What user have access? Just show them instead.
			$users_available = [];
			foreach($this->users as $user_id => $user)
			{
				$users_available[$user_id] = $this->users[$user_id]->name;
			}
			$this->data['users'] = $users_available;
		}
		
		$validation_rules = array_merge($this->_get_rules(), $this->{$this->data['callback']}());

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			$menu_id = UUIDv4();
			
			if ( $this->menu_model->create( $menu_id, $parent_id, $type, $callback ) === TRUE )
			{
				if( $this->config->item('document_author') && $type === 'menu' )
				{
					// Empty data set
					$data = NULL;
				
					// Grab ACL field
					$users = $this->input->post('acl[]');
					
					if( ! empty($users) )
					{
						// Validate users. Show 404 in case a user have changed the values.
						if( !empty(array_diff($users,array_keys($this->users))) ) { show_404(); }
						
						// Make an ACL array so that we can just save it
						$data = $this->acl_model->_get_data_object($acl_menu, $menu_id, $users);				
					}
					
					// Save ACL
					if ( $this->acl_model->save_object( $acl_menu, $menu_id, $data ) !== FALSE )
					{
						
					}
				}

				$menus_groups = array_merge( $this->menu_model->_get_menus_position(), $this->menu_model->_get_menus_department() );
				if( $this->group_model->save( 'menu_group', 'menu_id', $menu_id, $menus_groups ) !== FALSE )
				{
					$url = 'admin/menus/view';

					if( $type === 'menu' )
					{
						$url = 'admin/menus/owner/' . $menu_id;
					}

					if ( $this->data['folder_id'] )
					{
						$url .= '/' . $this->data['folder_id'];
					}

					if( $this->data['sub_folder_id'] )
					{
						$url .= '/' . $this->data['sub_folder_id'];
					}

					if( $this->data['folder_id'] === NULL )
					{
						redirect('admin/menus'); exit;
					}
					
					redirect($url); exit;
				}
			}
		}
		else
		{
			$this->load->view('admin/menus/create',$this->data);
		}
	}
	
	private function create_menu() { return array(); }
	
	private function create_folder() { return array(); }
	
	private function create_link()
	{
		return array(
			array(
				'field' => 'menus_href',
				'label' => lang('menus_href'),
				'rules' => array(
					'trim',
					'required',
					'prep_url',
					'valid_url'
				)
			),
		);
	}
	
	private function create_app() { return array(); }
	
    public function update( $type = 'folder', $menu_id = NULL, $folder_id = NULL, $sub_folder_id = NULL )
	{
		in_array($type,array_keys(Menus::$types),TRUE) OR show_404();
		
		$this->VALID_UUIDv4($menu_id);
		$this->VALID_UUIDv4($folder_id,FALSE);
		$this->VALID_UUIDv4($sub_folder_id,FALSE);
		$this->_get_multiple_folders_data($folder_id,$sub_folder_id,$menu_id);
		$this->_get_sticky_data();
		
		$parent_id = NULL;
		
		if( $this->data['folder_id'] )
			$parent_id = $this->data['folder_id'];
		
		if( $this->data['sub_folder_id'] )
			$parent_id = $this->data['sub_folder_id'];
		
		$this->data['type']     = $type;
		$this->data['callback'] = $callback = 'update_' . $type;
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$this->load->model('group_model');
		
		$this->data['groups'] = $this->group_model->get_all();
		$this->data['groups_checked'] = $this->group_model->get_all_by_relationship( 'menu_group', 'menu_id', $menu_id, FALSE);

		if( $this->config->item('document_author') && $type === 'menu' )
		{
			$this->load->model('acl_model');
			$acl_menu = $this->acl_model->get_like( 'menu' );
			if( empty($acl_menu) ) { show_404(); }

			unset($acl_menu['create']);
			unset($acl_menu['read']);
			unset($acl_menu['update']);
			unset($acl_menu['delete']);
			unset($acl_menu['folder']);
			unset($acl_menu['all']);

			$this->data['acl_users'] = [];
			$acl_menu_checked = $this->acl_model->get_object_checked_like($acl_menu, NULL, $menu_id);
			if( ! empty($acl_menu_checked) )
			{
				$this->data['acl_users'] = array_keys($acl_menu_checked);
			}
			
			// @TODO: What user have access? Just show them instead.
			$users_available = [];
			foreach($this->users as $user_id => $user)
			{
				$users_available[$user_id] = $this->users[$user_id]->name;
			}
			$this->data['users'] = $users_available;
		}
		
		$validation_rules = array_merge($this->_get_rules(), $this->{$this->data['callback']}());

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			if ( $this->menu_model->update( $menu_id, $callback ) === TRUE )
			{
				if( $this->config->item('document_author') && $type === 'menu' )
				{
					// Empty data set
					$data = NULL;
				
					// Grab ACL field
					$users = $this->input->post('acl[]');
					
					if( ! empty($users) )
					{
						// Validate users. Show 404 in case a user have changed the values.
						if( !empty(array_diff($users,array_keys($this->users))) ) { show_404(); }
						
						// Make an ACL array so that we can just save it
						$data = $this->acl_model->_get_data_object($acl_menu, $menu_id, $users);				
					}

					// Save ACL
					if ( $this->acl_model->save_object( $acl_menu, $menu_id, $data ) !== FALSE )
					{
						
					}
				}

				$menus_groups = array_merge( $this->menu_model->_get_menus_position(), $this->menu_model->_get_menus_department() );
				if( $this->group_model->save( 'menu_group', 'menu_id', $menu_id, $menus_groups ) !== FALSE )
				{
					if( $this->data['sub_folder_id'] )
					{
						redirect('admin/menus/view/' . $this->data['folder_id'] . '/' . $this->data['sub_folder_id']); exit;
					}
					else if ( $this->data['folder_id'] )
					{
						redirect('admin/menus/view/' . $this->data['folder_id']); exit;
					}
					else
					{
						redirect('admin/menus'); exit;
					}	
				}
			}
		}
		else
		{
			$this->load->view('admin/menus/update',$this->data);
		}
	}
	
	private function update_menu() { return array(); }
	
	private function update_folder() { return array(); }
	
	private function update_link()
	{
		return array(
			array(
				'field' => 'menus_href',
				'label' => lang('menus_href'),
				'rules' => array(
					'trim',
					'required',
					'prep_url',
					'valid_url'
				)
			),
		);
	}
	
	private function update_app() { return array(); }
	
	public function owner( $menu_id = NULL, $folder_id = NULL, $sub_folder_id = NULL)
	{
		$this->VALID_UUIDv4($menu_id);
		$this->VALID_UUIDv4($folder_id,FALSE);
		$this->VALID_UUIDv4($sub_folder_id,FALSE);
		$this->_get_multiple_folders_data($folder_id,$sub_folder_id,$menu_id);
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$this->load->model('group_model');
		$this->load->model('user_model');
		
		$this->data['groups'] = $this->group_model->get_all_by_relationship( 'menu_group', 'menu_id', $menu_id, FALSE, TRUE );
		$this->data['users']['owner']['available'] = [
			NULL => ' - ' . lang('menus_owner') . ' - '
		];
		if( !empty($this->data['groups']) && isset($this->data['groups']['position']) && isset($this->data['groups']['department']) )
			$this->data['users']['owner']['available'] = array_merge($this->data['users']['owner']['available'],$this->user_model->get_users_by_groups($this->data['groups']['position'],$this->data['groups']['department']));
		
		// @TODO: Check if user are a document owner of a menu, so that you can't delete his/her rights to that menu/group
		
		$this->form_validation->set_rules( $this->_get_rules_owner() );
		if( $this->form_validation->run() === TRUE )
		{
			if ( $this->menu_model->owner( $menu_id ) === TRUE )
			{
				if( $this->data['sub_folder_id'] )
				{
					redirect('admin/menus/view/' . $this->data['folder_id'] . '/' . $this->data['sub_folder_id']); exit;
				}
				else if ( $this->data['folder_id'] )
				{
					redirect('admin/menus/view/' . $this->data['folder_id']); exit;
				}
				else
				{
					redirect('admin/menus'); exit;
				}
			}
		}
		else
		{
			$this->load->view('admin/menus/owner',$this->data);
		}
	}
	
    public function delete( $menu_id = NULL, $folder_id = NULL, $sub_folder_id = NULL )
	{
		$this->VALID_UUIDv4($menu_id);
		$this->VALID_UUIDv4($folder_id,FALSE);
		$this->VALID_UUIDv4($sub_folder_id,FALSE);
		$this->_get_multiple_folders_data($folder_id,$sub_folder_id,$menu_id);
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$validation_rules = $this->_get_rules_delete();
		
		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			if ( $this->menu_model->delete( $menu_id ) === TRUE )
			{
				
			}
			if( $sub_folder_id )
			{
				redirect('admin/menus/view/' . $folder_id . '/' . $sub_folder_id); exit;
			}
			else if ( $folder_id )
			{
				redirect('admin/menus/view/' . $folder_id); exit;
			}
			else
			{
				redirect('admin/menus'); exit;
			}	
		}
		else
		{
			$this->load->view('admin/menus/delete',$this->data);
		}
	}
	
    public function move( $menu_id = NULL, $folder_id = NULL, $sub_folder_id = NULL )
	{
		$this->VALID_UUIDv4($menu_id);
		$this->VALID_UUIDv4($folder_id,FALSE);
		$this->VALID_UUIDv4($sub_folder_id,FALSE);
		$this->_get_multiple_folders_data($folder_id,$sub_folder_id,$menu_id);
		
		$this->data['move']['menu'] = $this->menu_model->get_all();
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		
		if($this->data['menu']->type == 1)
		{
			$validation = array_merge(
				$this->data['move']['menu']['id'],
				['00000000-0000-0000-0000-000000000000']
			);
		}
		else
		{
			$validation = $this->data['move']['menu']['id'];
		}
		
		$config = array(
			array(
				'field' => 'move_menu',
				'label' => lang('folder_folder'),
				'rules' => array(
					'trim',
					'required',
					'in_list['.implode(',',$validation).']'
				),
			),
		);
		
		$this->form_validation->set_rules( $config );
		if( $this->form_validation->run() === TRUE )
		{
			$new_position = $this->menu_model->move( $menu_id );
			
			if ( $new_position !== '00000000-0000-0000-0000-000000000000' )
			{
				redirect('admin/menus/view/' . $new_position); exit;
			}
			else
			{
				redirect('admin/menus'); exit;
			}
		}
		else
		{
			$this->load->view('admin/menus/move',$this->data);
		}
	}
	
	public function rights( $group_id = NULL )
	{
		$this->VALID_UUIDv4($group_id);
	
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$this->load->model('group_model');
		
		$this->data['group'] = $this->group_model->get($group_id);
		if( empty($this->data['group']) ) { show_404(); }
		if( $this->data['group']->company_id !== $this->auth_company_id ) { show_404(); }
		$this->data['groups_checked'] = $this->group_model->get_all_by_relationship( 'menu_group', 'group_id', $group_id, FALSE, FALSE, 'menu_id');
		
		$this->data['move']['menu'] = $this->menu_model->get_all();
		
		$config = array(
			array(
				'field' => 'rights_menu[]',
				'label' => lang('groups_groups'),
				'rules' => array(
					'trim',
					'in_list['.implode(',',$this->data['move']['menu']['id']).']'
				),
			),
		);

		$this->form_validation->set_rules( $config );
		if( $this->form_validation->run() === TRUE )
		{
			$menus_groups = $this->menu_model->_get_rights_menu();
			if( $this->group_model->save_reverse( 'menu_group', 'menu_id', $group_id, $menus_groups, $this->data['move']['menu']['id'] ) !== FALSE )
			{
				
			}
			
			redirect('admin/groups'); exit;
		}
		else
		{
			$this->load->view('admin/menus/rights',$this->data);
		}
	}
	
	public function users()
	{
		$this->data['users'] = $this->user_model->get_all();
		$this->load->view('admin/menus/users',$this->data);
	}
	
	public function owners($user_id = NULL)
	{
		$this->VALID_UUIDv4($user_id);
	
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$this->load->model('user_model');
		
		$this->data['user'] = $this->user_model->get($user_id);
		if( empty($this->data['user']) ) { show_404(); }
		if( $this->data['user']->company_id !== $this->auth_company_id ) { show_404(); }
		
		$this->data['move']['menu'] = $this->menu_model->get_all();
		
		$config = array(
			array(
				'field' => 'owner_menu[]',
				'label' => lang('menus_owner'),
				'rules' => array(
					'trim',
					'in_list['.implode(',',$this->data['move']['menu']['id']).']'
				),
			),
		);

		$this->form_validation->set_rules( $config );
		if( $this->form_validation->run() === TRUE )
		{
			$new_owner  = $this->input->post('owner_menu[]');
			$menu_owner = $this->menu_model->get_owner( $user_id );

			if( $new_owner === NULL )
				$new_owner = [];

			$new    = array_diff($new_owner, $menu_owner);
			$delete = array_diff($menu_owner, $new_owner);
			$owner  = UUID_TO_BIN($user_id);
			$menus_update = [];

			if( ! empty($new) )
			{
				foreach($new as $menu_id)
				{
					$menus_update[] = [
						'owner'   => $owner,
						'menu_id' => UUID_TO_BIN($menu_id),
					];
				}
			}

			if( ! empty($delete) )
			{
				foreach($delete as $menu_id)
				{
					$menus_update[] = [
						'owner'   => NULL,
						'menu_id' => UUID_TO_BIN($menu_id),
					];
				}
			}

			if( ! empty($menus_update) )
			{
				$this->menu_model->update_batch($menus_update, 'menu_id');
			}

			redirect('admin/menus/owners/' . $user_id); exit;
		}
		else
		{
			$this->load->view('admin/menus/owners',$this->data);
		}
	}
	
	private function _get_rules()
	{
		return array(
			array(
				'field' => 'menus_name',
				'label' => lang('menus_name'),
				'rules' => array(
					'trim',
					'required',
					'min_length[2]',
					'max_length[64]', // 64
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']',
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'menus_description',
				'label' => lang('menus_description'),
				'rules' => array(
					'trim',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']',
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'menus_sticky',
				'label' => lang('menus_sticky'),
				'rules' => array(
					'trim',
					'in_list['.implode(',',array_keys($this->data['menus_sticky'])).']'
				)
			),
			array(
				'field' => 'menus_position[]',
				'label' => lang('groups_type_position'),
				'rules' => array(
					'trim',
					'required',
					'in_list['.implode(',',array_keys($this->data['groups']['position'])).']'
				)
			),
			array(
				'field' => 'menus_department[]',
				'label' => lang('groups_type_department'),
				'rules' => array(
					'trim',
					'required',
					'in_list['.implode(',',array_keys($this->data['groups']['department'])).']'
				)
			),
		);
	}
	
	private function _get_rules_owner()
	{
		return array(
			array(
				'field' => 'menus_owner',
				'label' => lang('menus_owner'),
				'rules' => array(
					'trim',
					'in_list['.implode(',',array_keys($this->data['users']['owner']['available'])).']'
				)
			),
		);
	}
	
	private function _get_rules_delete()
	{
		return array(
			array(
				'field' => 'confirm_delete',
				'label' => lang('confirm_delete'),
				'rules' => array(
					'required'
				)
			),
		);
	}
}
