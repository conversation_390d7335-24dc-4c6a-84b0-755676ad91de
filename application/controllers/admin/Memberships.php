<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Memberships extends Admin_Controller
{
	public function __construct()
	{
		parent::__construct();
		$this->data['body_class'] .= 'skin-white fixed';
		$this->load->model('membership_model');
	}
	
    public function index()
	{
		$this->view();
    }
	
	public function view( $limit = NULL, $offset = NULL )
	{
		$this->data['memberships'] = $this->membership_model->get_all($limit,$offset);
		$this->load->view('admin/memberships/view',$this->data);
	}
	
    public function create()
	{
		$this->data['membership'] = NULL;
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$validation_rules = $this->_get_rules();

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			$membership_id = UUIDv4();
			
			if ( $this->membership_model->create( $membership_id ) === TRUE )
			{
				redirect('admin/memberships'); exit;
			}
		}
		else
		{
			$this->load->view('admin/memberships/create',$this->data);
		}
	}
	
    public function update( $membership_id )
	{
		$this->VALID_UUIDv4($membership_id);
		
		$this->data['membership'] = $this->membership_model->get($membership_id);
		if( empty($this->data['membership']) ) { show_404(); }
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$validation_rules = $this->_get_rules();
		
		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			if ( $this->membership_model->update( $this->data['membership'] ) === TRUE )
			{
				redirect('admin/memberships'); exit;
			}
		}
		else
		{
			$this->load->view('admin/memberships/update',$this->data);
		}
	}
	
	public function _is_unique()
	{
		return $this->membership_model->is_unique( $this->data['membership'] );
	}
	
	private function _get_rules()
	{
		return array(
			array(
				'field' => 'memberships_name',
				'label' => lang('memberships_name'),
				'rules' => array(
					'trim',
					'required',
					'min_length[3]',
					'max_length[25]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']',
					array('is_unique', array( $this, '_is_unique' ) )
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'memberships_default',
				'label' => lang('memberships_default'),
				'rules' => array(
					'trim',
					'required',
					'in_list[0,1]'
				)
			)
		);
	}
}