<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Appearance extends Admin_Controller
{
	private $settingsPath = CI_UPLOAD_PATH . 'settings.json';

	public function __construct()
	{
		parent::__construct();
		$this->data['body_class'] .= 'skin-white fixed';
	}

	public function index()
	{
		$this->load->helper('form');
		$this->load->library('form_validation');

		$settings = [
			'vertical'   => 0,
			'horizontal' => 1,
			'buttons_vertical' => 0,
			'widget_vertical' => 1,
			'size'       => null,
			'logo'       => []
		];

		if(file_exists($this->settingsPath))
		{
			if(($json = file_get_contents($this->settingsPath)) !== FALSE)
			{
				if(($array = json_decode($json, true)) !== NULL)
				{
					$settings = $array;
					if (!array_key_exists('widget_vertical', $settings)) {
						$settings['widget_vertical'] = 1;
					}
					if (!isset($settings['widgets']) && isset($settings['widget']) && !empty($settings['widget'])) {
						$settings['widgets'] = [$settings['widget']];
					}
					if (!array_key_exists('buttons_vertical', $settings)) {
						$settings['buttons_vertical'] = 0;
					}
				}
			}
		}

		$this->data['settings'] = $settings;

		$this->load->model(['task_model']);
		$this->data['tasktype_list'] = $this->task_model->get_task_type_data();

		$validation_rules = [
			[
				'field' => 'appearance_logo_vertical',
				'label' => lang('appearance_logo_position'),
				'rules' => [
					'integer',
					'in_list[0,1]'
				],
			],
			[
				'field' => 'appearance_logo_horizontal',
				'label' => lang('appearance_logo_position'),
				'rules' => [
					'integer',
					'in_list[0,1,2]'
				],
			],
			[
				'field' => 'appearance_logo_size',
				'label' => lang('appearance_logo_size'),
				'rules' => [
					'integer',
					'min_length[3]',
					'max_length[4]',
				],
			]
		];

		$this->form_validation->set_rules($validation_rules);
		if( $this->form_validation->run() === TRUE )
		{
			$settings['vertical']   = $this->input->post('appearance_logo_vertical');
			$settings['buttons_vertical']   = $this->input->post('appearance_buttons_position');
			$settings['horizontal'] = $this->input->post('appearance_logo_horizontal');
			$settings['size']       = $this->input->post('appearance_logo_size');
			$settings['tasktypes'] = $this->input->post('appearance_tasktype');

			$settings['widget_vertical']   = $this->input->post('appearance_widget_vertical');
			if ($this->input->post('appearance_widget') !== null)
				$settings['widget'] = $this->input->post('appearance_widget');

			if ($this->input->post('appearance_widgets') !== null) {
				$settings['widget'] = "";
				$settings['widgets'] = json_decode($this->input->post('appearance_widgets'), true);
			}

			if( ! empty($_FILES['file']['name']) )
			{
				if( is_dir(CI_UPLOAD_PATH) )
				{
					// Generate a unique ID for attached file
					$attachment_id = UUIDv4();

					// File upload configuration
					$config['upload_path']   = CI_UPLOAD_PATH;
					$config['allowed_types'] = ['jpg' ,'jpeg' ,'png'];
					$config['max_size']      = 4096;
					$config['file_name']     = $attachment_id;

					// Load and initialize upload library
					$this->load->library('upload', $config);
					$this->upload->initialize($config);

					// Upload file to server
					if($this->upload->do_upload('file'))
					{
						// Uploaded file data
						$file = $this->upload->data();
						$logo = [];

						if($file['is_image'])
						{
							$logo['attachment_id'] = $attachment_id;
							$logo['file_name']     = $file['client_name'] !== '' ? $file['client_name'] : $file['orig_name'];
							$logo['file_type']     = $file['file_type'];
							$logo['file_ext']      = $file['file_ext'];
							$logo['uploaded_on']   = time();
						}

						if( ! empty($logo) )
						{
							$attachment = CI_UPLOAD_PATH . $logo['attachment_id'] . $logo['file_ext'];

							if(file_exists($attachment))
							{
								if(($data = file_get_contents($attachment)) !== FALSE)
								{
									$logo['data']     = 'data:' . $logo['file_type'] . ';base64,' . base64_encode($data);
									$settings['logo'] = $logo;
								}

								// Remove tmp file
								unlink($attachment);
							}
						}
					}
				}
			}

			// Logo style
			$align = 'center';
			$size  = '100%';

			switch($settings['horizontal'])
			{
				case '0':
					$align = 'left';
					break;
				case '2':
					$align = 'right';
					break;
			}

			if( ! empty($settings['size']) )
				$size = (int) $settings['size'] . 'px';

			$settings['logo']['align'] = 'text-align:' . $align;
			$settings['logo']['size']  = 'max-width:' . $size;
			// END: Logo style

			if(($json = json_encode($settings)) !== FALSE)
			{
				file_put_contents($this->settingsPath, $json);
			}

			redirect('/admin/appearance');
		}
		else
		{
			$this->load->view('admin/appearance/index', $this->data);
		}
	}

	public function delete()
	{
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');

		$validation_rules = [
			[
				'field' => 'confirm_delete',
				'label' => lang('confirm_delete'),
				'rules' => [
					'required'
				]
			],
		];

		$this->form_validation->set_rules($validation_rules);
		if( $this->form_validation->run() === TRUE )
		{
			if(file_exists($this->settingsPath))
				unlink($this->settingsPath);

			redirect('admin/appearance');
		}
		else
		{
			$this->load->view('admin/appearance/delete', $this->data);
		}
	}
}