<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Groups extends Admin_Controller
{
	public function __construct()
	{
		parent::__construct();
		$this->data['body_class'] .= 'skin-white fixed';
		$this->load->model('group_model');
	}
	
    public function index()
	{
		$this->view();
    }
	
	public function view( )
	{
		$this->data['groups'] = $this->group_model->get_all();
		$this->data['user_group'] = $this->group_model->in_use('user_group');
		$this->data['deviation_department'] = $this->group_model->in_use('deviation_department','de_id');
		$this->data['risk_assessments_department'] = $this->group_model->in_use('risk_assessments_department','de_id');
		$this->load->view('admin/groups/view',$this->data);
	}
	
    public function create()
	{
		$this->data['groups_type'] = array(
			'position'   => lang('groups_type_position'),
			'department' => lang('groups_type_department'),
		);

		if( $this->auth_god )
		{
			$this->data['groups_type']['security'] = lang('groups_type_security');
		}
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$validation_rules = $this->_get_rules();

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			$group_id = UUIDv4();
			
			if ( $this->group_model->create( $group_id ) === TRUE )
			{
				// redirect('admin/groups/update/' . $group_id); exit;
			}

			redirect('admin/groups'); exit;
		}
		else
		{
			$this->load->view('admin/groups/create',$this->data);
		}
	}
	
    public function update( $group_id )
	{
		$this->VALID_UUIDv4($group_id);
		
		$this->data['group'] = $this->group_model->get($group_id);
		if( empty($this->data['group']) ) { show_404(); }
		if( $this->data['group']->company_id !== $this->auth_company_id ) { show_404(); }
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$validation_rules = $this->_get_rules_update();
		
		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			if ( $this->group_model->update( $this->data['group'] ) === TRUE )
			{
				redirect('admin/groups'); exit;
			}
		}
		else
		{
			$this->load->view('admin/groups/update',$this->data);
		}
	}
	// @TODO: Remove groups
    public function delete( $group_id )
	{
		$this->VALID_UUIDv4($group_id);
		
		$this->data['group'] = $this->group_model->get($group_id);
		if( empty($this->data['group']) ) { show_404(); }
		if( $this->data['group']->company_id !== $this->auth_company_id ) { show_404(); }

		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$validation_rules = $this->_get_rules_delete();

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			if ( $this->group_model->delete( $group_id ) !== TRUE )
			{
				
			}
			redirect('admin/groups'); exit;
		}
		else
		{
			$this->load->view('admin/groups/delete',$this->data);
		}
	}
	
	public function _is_unique()
	{
		return $this->group_model->is_unique();
	}
	
	public function _is_unique_update()
	{
		return $this->group_model->is_unique_update( $this->data['group'] );
	}
	
	private function _get_rules()
	{
		return array(
			array(
				'field' => 'groups_name',
				'label' => lang('groups_name'),
				'rules' => array(
					'trim',
					'required',
					'min_length[2]',
					'max_length[80]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']',
					array('is_unique', array( $this, '_is_unique' ) )
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'groups_type',
				'label' => lang('groups_type'),
				'rules' => array(
					'trim',
					'required',
					'in_list['.implode(',',array_keys($this->data['groups_type'])).']'
				)
			)
		);
	}
	
	private function _get_rules_update()
	{
		return array(
			array(
				'field' => 'groups_name',
				'label' => lang('groups_name'),
				'rules' => array(
					'trim',
					'required',
					'min_length[2]',
					'max_length[80]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']',
					array('is_unique', array( $this, '_is_unique_update' ) )
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			)
		);
	}

	private function _get_rules_delete()
	{
		return array(
			array(
				'field' => 'confirm_delete',
				'label' => lang('confirm_delete'),
				'rules' => array(
					'required'
				)
			),
		);
	}
}
