<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Forms extends Form_Controller
{
	// @STEP2: Rules, rules, rules
	function __construct()
	{
		parent::__construct();
		if( ! is_role('Systemadministratör') )
			show_error('You don\'t have permission to access this page',403,'403 Access Denied');
		
		// Load dependencies
		$this->load->model('form_model');
		$this->load->model('folder_model');
		$this->load->model('group_model');
		$this->data['body_class']        .= 'skin-white fixed';
		$this->data['form']               = NULL;
		$this->data['form_id']            = NULL;
		$this->data['page']               = NULL;
		$this->data['page_id']            = NULL;
		$this->data['sub_page']           = NULL;
		$this->data['sub_page_id']        = NULL;
		$this->data['page_current']       = NULL;
		$this->data['page_current_id']    = NULL;
		$this->data['question']           = NULL;
		$this->data['question_id']        = NULL;
		$this->data['question_parent']    = NULL;
		$this->data['question_parent_id'] = NULL;
		
		$this->data['sidebar']['admin']  = TRUE;
		$this->data['sidebar']['active'] = TRUE;
	}
	
	private function view_default()
	{
		$this->data['buttons'] = array(
			'form'
		);

		$this->data['page_url'] = [];

		$this->data['forms'] = $this->form_model->get_all();
		$this->load->view('admin/forms/view',$this->data);
	}
	
    public function index()
	{
		$this->view_default();
    }
	// @STEP2: "checklist" go directly to page
	public function view( $form_id = NULL, $page_id = NULL, $sub_page_id = NULL )
	{
		$this->VALID_UUIDv4($form_id);
		$this->VALID_UUIDv4($page_id,FALSE);
		$this->VALID_UUIDv4($sub_page_id,FALSE);
		$this->_get_multiple_pages_data($form_id,$page_id,$sub_page_id);
		$this->_get_urls(NULL,NULL,$form_id,$page_id,$sub_page_id);
		
		$this->data['buttons'] = array(
			'page',
			'question',
		);

		if( $page_id === NULL )
		{
			$this->data['buttons'] = array(
				'page',
			);
		}
		
		if( $sub_page_id )
			$this->data['forms'] = $this->form_model->get_form_pages_by_parent($this->data['sub_page_id']);
		else if( $page_id )
			$this->data['forms'] = $this->form_model->get_form_pages_by_parent($this->data['page_id']);
		else
			$this->data['forms'] = $this->form_model->get_form_pages_by_form($this->data['form_id']);

		if( $this->data['form']->type === 'checklist' && $page_id !== NULL )
		{
			$this->data['buttons'] = array(
				'question',
			);
		}

		if( $this->data['form']->type === 'checklist' && $page_id === NULL && count($this->data['forms']) >= 1 )
		{
			$this->data['buttons'] = array();
		}

		$this->load->view('admin/forms/view',$this->data);
	}
	
	public function questions( $current_id = NULL, $form_id = NULL, $page_id = NULL, $sub_page_id = NULL )
	{
		$this->VALID_UUIDv4($current_id);
		$this->VALID_UUIDv4($form_id);
		$this->VALID_UUIDv4($page_id,FALSE);
		$this->VALID_UUIDv4($sub_page_id,FALSE);
		$this->_get_multiple_pages_data($form_id,$page_id,$sub_page_id,$current_id);
		$this->_get_urls(NULL,$current_id,$form_id,$page_id,$sub_page_id);
		$this->_get_field_types();
		
		$this->data['buttons'] = array(
			'question',
		);
		
		$this->data['questions'] = $this->form_model->get_page_questions_by_page_id($this->data['page_current']->page_id);
		if( ! empty($this->data['questions']) )
		{
			$this->data['questions_children'] = $this->form_model->get_page_questions_children_by_page_id(array_keys($this->data['questions']));
		}
		
		$this->load->view('admin/forms/question_view',$this->data);
	}
	
	public function options( $question_id = NULL, $current_id = NULL, $form_id = NULL, $page_id = NULL, $sub_page_id = NULL )
	{
		$this->VALID_UUIDv4($question_id);
		$this->VALID_UUIDv4($current_id);
		$this->VALID_UUIDv4($form_id);
		$this->VALID_UUIDv4($page_id,FALSE);
		$this->VALID_UUIDv4($sub_page_id,FALSE);
		$this->_get_multiple_pages_data($form_id,$page_id,$sub_page_id,$current_id,$question_id);
		$this->_get_urls($question_id,$current_id,$form_id,$page_id,$sub_page_id);
		$this->_get_field_types();
		
		$this->data['buttons'] = array(
			'question',
		);
		
		$this->data['options'] = $this->form_model->get_question_options_by_question_id($this->data['question']->question_id);
		$this->load->view('admin/forms/option_view',$this->data);
	}
	
	public function option( $type = 'create', $option_id = NULL, $question_id = NULL, $current_id = NULL, $form_id = NULL, $page_id = NULL, $sub_page_id = NULL )
	{
		$func = 'option_' . $type;
		if( !method_exists($this,$func) ) { show_404(); }
		$this->{$func}( $option_id, $question_id, $current_id, $form_id, $page_id, $sub_page_id );
	}
	
	private function option_create( $option_id, $question_id, $current_id, $form_id, $page_id, $sub_page_id )
	{
		if( $option_id !== '00000000-0000-0000-0000-000000000000' ) { show_404(); }
		$this->VALID_UUIDv4($question_id);
		$this->VALID_UUIDv4($current_id);
		$this->VALID_UUIDv4($form_id);
		$this->VALID_UUIDv4($page_id,FALSE);
		$this->VALID_UUIDv4($sub_page_id,FALSE);
		$this->_get_multiple_pages_data($form_id,$page_id,$sub_page_id,$current_id,$question_id);
		$this->_get_urls($question_id,$current_id,$form_id,$page_id,$sub_page_id);
			
		// $this->data['question_parents'] = $this->form_model->get_page_questions_parents($this->data['page_current']->page_id);
		// var_dump($this->data['question_parents']);exit;
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		
		$validation_rules = $this->_get_question_create_rules();

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			$create_id = UUIDv4();
			
			if ( $this->form_model->create_option( $question_id, $create_id ) === TRUE )
			{
				$url = implode('/', $this->data['complete_url']);
				redirect('admin/forms/options/' . $url); exit;
			}
		}
		else
		{
			$this->load->view('admin/forms/option_create',$this->data);
		}
	}
	
	private function option_edit( $option_id, $question_id, $current_id, $form_id, $page_id, $sub_page_id )
	{
		$this->VALID_UUIDv4($option_id);
		$this->VALID_UUIDv4($question_id);
		$this->VALID_UUIDv4($current_id);
		$this->VALID_UUIDv4($form_id);
		$this->VALID_UUIDv4($page_id,FALSE);
		$this->VALID_UUIDv4($sub_page_id,FALSE);
		$this->_get_multiple_pages_data($form_id,$page_id,$sub_page_id,$current_id,$question_id,$option_id);
		$this->_get_urls($question_id,$current_id,$form_id,$page_id,$sub_page_id);
			
		// $this->data['question_parents'] = $this->form_model->get_page_questions_parents($this->data['page_current']->page_id);
		// var_dump($this->data['question_parents']);exit;
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		
		$validation_rules = $this->_get_question_create_rules();

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			if ( $this->form_model->edit_option( $this->data['option'] ) === TRUE )
			{
				$url = implode('/', $this->data['complete_url']);
				redirect('admin/forms/options/' . $url); exit;
			}
		}
		else
		{
			$this->load->view('admin/forms/option_edit',$this->data);
		}
	}
	
	private function option_delete( $option_id, $question_id, $current_id, $form_id, $page_id, $sub_page_id )
	{
		$this->VALID_UUIDv4($option_id);
		$this->VALID_UUIDv4($question_id);
		$this->VALID_UUIDv4($current_id);
		$this->VALID_UUIDv4($form_id);
		$this->VALID_UUIDv4($page_id,FALSE);
		$this->VALID_UUIDv4($sub_page_id,FALSE);
		$this->_get_multiple_pages_data($form_id,$page_id,$sub_page_id,$current_id,$question_id,$option_id);
		$this->_get_urls($question_id,$current_id,$form_id,$page_id,$sub_page_id);
			
		// $this->data['question_parents'] = $this->form_model->get_page_questions_parents($this->data['page_current']->page_id);
		// var_dump($this->data['question_parents']);exit;
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		
		$validation_rules = $this->_get_rules_delete();

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			if ( $this->form_model->delete_option( $this->data['option'], $this->data['question'], $this->data['form'] ) === TRUE )
			{
				$url = implode('/', $this->data['complete_url']);
				redirect('admin/forms/options/' . $url); exit;
			}
		}
		else
		{
			$this->load->view('admin/forms/option_delete',$this->data);
		}
	}
	
	public function question( $type = 'create', $question_id = NULL, $current_id = NULL, $form_id = NULL, $page_id = NULL, $sub_page_id = NULL )
	{
		$func = 'question_' . $type;
		if( !method_exists($this,$func) ) { show_404(); }
		$this->{$func}( $question_id, $current_id, $form_id, $page_id, $sub_page_id );
	}
	
	private function question_children( $question_id, $current_id, $form_id, $page_id, $sub_page_id )
	{
		$this->VALID_UUIDv4($question_id);
		$this->VALID_UUIDv4($current_id);
		$this->VALID_UUIDv4($form_id);
		$this->VALID_UUIDv4($page_id,FALSE);
		$this->VALID_UUIDv4($sub_page_id,FALSE);
		$this->_get_multiple_pages_data($form_id,$page_id,$sub_page_id,$current_id,$question_id);
		$this->_get_urls($question_id,$current_id,$form_id,$page_id,$sub_page_id);
		$this->_get_field_types();
		
		$this->data['buttons'] = array(
			'question',
		);
		
		$this->data['question_children'] = $this->form_model->get_page_questions_by_parent_id($this->data['question']->question_id);
		$this->load->view('admin/forms/question_children',$this->data);
		// var_dump($this->data);exit;
	}
	
	private function question_table( $question_id, $current_id, $form_id, $page_id, $sub_page_id )
	{
		$this->VALID_UUIDv4($question_id);
		$this->VALID_UUIDv4($current_id);
		$this->VALID_UUIDv4($form_id);
		$this->VALID_UUIDv4($page_id,FALSE);
		$this->VALID_UUIDv4($sub_page_id,FALSE);
		$this->_get_multiple_pages_data($form_id,$page_id,$sub_page_id,$current_id,$question_id);
		$this->_get_urls($question_id,$current_id,$form_id,$page_id,$sub_page_id);
		// $this->_get_field_types();

		$this->questions = $this->form_model->get_question_structure($this->data['page_current_id']);
		$this->data['questions'] = $this->questions['structure'];
		$this->data['options']   = $this->questions['options'];
		$this->data['page_options'] = ['00000000-0000-0000-0000-000000000000' => NULL];

		if( isset($this->data['questions'][$this->data['question_id']]) )
		{
			foreach($this->data['questions'][$this->data['question_id']] as $id => $question)
			{
				$this->data['page_options'][$id] = $question->name;
			}
		}

		if( $this->input->method(TRUE) === 'POST' )
		{
			$type     = $this->input->post('form_table_type');
			$header   = $this->input->post('form_table_header');
			$body     = $this->input->post('form_table_body');
			$count_t  = count($type);
			$count_h  = count($header);
			$count_b  = count($body);
			$settings = [];

			// Delete
			if( $count_h === 0 )
			{
				$this->data['question']->settings = '{"cols":1,"rows":1,"type":["text"],"header":[""],"body":[""]}';
			}
			else
			{
				// Incorrect amount given
				if( $count_t !== $count_h)
					exit;

				// Incorrect amount given
				if( $count_b % $count_h != 0 )
					exit;

				// How many rows / cols?
				$settings['cols'] = $count_h;
				$settings['rows'] = $count_b / $count_h;

				// Add types
				$settings['type'] = $type;

				// Add headers
				$settings['header'] = $header;

				// Add each row
				if( $count_b !== 0 )
				{
					$loop = $count_b - $count_h;
					for($i = 0; $i <= $loop; $i += $count_h)
					{
						$tmp = [];
						for($ii = 0; $ii < $count_h; $ii++)
						{
							$tmp[] = $body[$i + $ii];
						}
	
						$settings['body'][] = $tmp;
					}
				}
				else
				{
					$tmp = [];
					for($ii = 0; $ii < $count_h; $ii++)
					{
						$tmp[] = NULL;
					}

					$settings['body'][] = $tmp;
				}

				$this->data['question']->settings = json_encode($settings);
			}

			if( $this->form_model->update_table($this->data['question']) === TRUE)
			{
				
			}

			if( $count_h === 0 )
			{
				$url = implode('/', $this->data['complete_url']);
				redirect('admin/forms/question/table/' . $url); exit;
			}
			else
			{
				$url = implode('/', $this->data['question_url']);
				redirect('admin/forms/questions/' . $url); exit;
			}
		}
		else
		{
			$this->load->helper(['form']);
			$this->load->view('admin/forms/question_table',$this->data);
		}
	}
	// Limit amout of fields if table
	private function question_create( $question_id, $current_id, $form_id, $page_id, $sub_page_id )
	{
		if( $question_id !== '00000000-0000-0000-0000-000000000000' )
			$this->VALID_UUIDv4($question_id);
		else
			$question_id = NULL;

		if( $form_id === NULL )
		{
			$form_id = $current_id;
			$current_id = NULL;
		}
		
		$this->VALID_UUIDv4($current_id,FALSE);
		$this->VALID_UUIDv4($form_id);
		$this->VALID_UUIDv4($page_id,FALSE);
		$this->VALID_UUIDv4($sub_page_id,FALSE);
		$this->_get_multiple_pages_data($form_id,$page_id,$sub_page_id,$current_id,$question_id);
		$this->_get_urls($question_id,$current_id,$form_id,$page_id,$sub_page_id);
		$this->_get_field_types();
		
		$this->data['question_parents'] = $this->form_model->get_page_questions_parents($this->data['page_current_id']);
		// var_dump($this->data['question_parents']);exit;
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		
		$validation_rules = $this->_get_question_create_rules();

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			$create_id = UUIDv4();
			
			if ( $this->form_model->create_question( $current_id, $create_id, $question_id ) === TRUE )
			{
				$url = implode('/', $this->data['complete_url']);
				if( $question_id )
				{
					redirect('admin/forms/question/children/' . $url); exit;
				}
				else
				{
					redirect('admin/forms/questions/' . $url); exit;
				}
			}
		}
		else
		{
			$this->load->view('admin/forms/question_create',$this->data);
		}
	}
	
	private function question_edit( $question_id, $current_id, $form_id, $page_id, $sub_page_id )
	{
		$this->VALID_UUIDv4($question_id);
		$this->VALID_UUIDv4($current_id);
		$this->VALID_UUIDv4($form_id);
		$this->VALID_UUIDv4($page_id,FALSE);
		$this->VALID_UUIDv4($sub_page_id,FALSE);
		$this->_get_multiple_pages_data($form_id,$page_id,$sub_page_id,$current_id,$question_id);
		$this->_get_urls($question_id,$current_id,$form_id,$page_id,$sub_page_id);
		$this->_get_field_types();

		$this->data['question_parents'] = $this->form_model->get_page_questions_parents($this->data['page_current']->page_id);
		// var_dump($this->data['question_parents']);exit;
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		
		$validation_rules = $this->_get_question_create_rules();

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			$parent_id = isset($this->data['question']->parent_id) ? $this->data['question']->parent_id : FALSE;
			if ( $this->form_model->update_question( $this->data['question'] ) === TRUE )
			{
				if( $parent_id )
				{
					$url = implode('/', $this->data['question_url']);
					redirect('admin/forms/question/children/' . $parent_id . '/' . $url); exit;
				}
				else
				{
					$url = implode('/', $this->data['question_url']);
					redirect('admin/forms/questions/' . $url); exit;
				}
			}
		}
		else
		{
			$this->load->view('admin/forms/question_update',$this->data);
		}
	}
	
	private function question_delete( $question_id, $current_id, $form_id, $page_id, $sub_page_id )
	{
		$this->VALID_UUIDv4($question_id);
		$this->VALID_UUIDv4($current_id);
		$this->VALID_UUIDv4($form_id);
		$this->VALID_UUIDv4($page_id,FALSE);
		$this->VALID_UUIDv4($sub_page_id,FALSE);
		$this->_get_multiple_pages_data($form_id,$page_id,$sub_page_id,$current_id,$question_id);
		$this->_get_urls($question_id,$current_id,$form_id,$page_id,$sub_page_id);
		$this->_get_field_types();

		$this->data['question_parents'] = $this->form_model->get_page_questions_parents($this->data['page_current']->page_id);
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		
		$validation_rules = $this->_get_rules_delete();

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			$parent_id = isset($this->data['question']->parent_id) ? $this->data['question']->parent_id : FALSE;
			if ( $this->form_model->delete_question( $this->data['question'], $this->data['form'] ) === TRUE )
			{
				if( $parent_id )
				{
					$url = implode('/', $this->data['question_url']);
					redirect('admin/forms/question/children/' . $parent_id . '/' . $url); exit;
				}
				else
				{
					$url = implode('/', $this->data['question_url']);
					redirect('admin/forms/questions/' . $url); exit;
				}
			}
		}
		else
		{
			$this->load->view('admin/forms/question_delete',$this->data);
		}
	}
	// @STEP2: "checklist"-form create a page automatic
    public function create( $type = 'page', $form_id = NULL, $page_id = NULL, $sub_page_id = NULL )
	{
		in_array($type,array_keys(Forms::$types),TRUE) OR show_404();
		
		$this->VALID_UUIDv4($page_id,FALSE);
		$this->VALID_UUIDv4($sub_page_id,FALSE);
		$this->_get_multiple_pages_data($form_id,$page_id,$sub_page_id);
		$this->_get_form_types();
		
		$parent_id = NULL;
		
		if( $this->data['form_id'] )
			$parent_id = $this->data['form_id'];
		
		if( $this->data['page_id'] )
			$parent_id = $this->data['page_id'];
		
		if( $this->data['sub_page_id'] )
			$parent_id = $this->data['sub_page_id'];
		
		$this->data['type']     = $type;
		$this->data['callback'] = $callback = 'create_' . $type;
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		
		$validation_rules = array_merge($this->_get_rules(), $this->{$this->data['callback']}());

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			$create_id = UUIDv4();
			
			if ( $this->form_model->{$this->data['callback']}( $form_id, $create_id, $parent_id ) === TRUE )
			{
				if( $this->data['sub_page_id'] )
				{
					redirect('admin/forms/view/' . $this->data['form_id'] . '/' . $this->data['page_id'] . '/' . $this->data['sub_page_id']); exit;
				}
				else if ( $this->data['page_id'] )
				{
					redirect('admin/forms/view/' . $this->data['form_id'] . '/' . $this->data['page_id']); exit;
				}
				else if ( $this->data['form_id'] )
				{
					redirect('admin/forms/view/' . $this->data['form_id']); exit;
				}
				else
				{
					redirect('admin/forms/view/' . $create_id); exit;
				}
			}
		}
		else
		{
			$this->load->view('admin/forms/create',$this->data);
		}
	}
	
    public function edit( $type = 'page', $current_id = NULL, $form_id = NULL, $page_id = NULL, $sub_page_id = NULL )
	{
		in_array($type,array_keys(Forms::$types),TRUE) OR show_404();

		$this->VALID_UUIDv4($current_id);
		$this->VALID_UUIDv4($form_id,FALSE);
		$this->VALID_UUIDv4($page_id,FALSE);
		$this->VALID_UUIDv4($sub_page_id,FALSE);

		if( $type === 'form' )
		{
			$form_id = $current_id;
			$current_id = NULL;
		}

		$this->_get_multiple_pages_data($form_id,$page_id,$sub_page_id,$current_id);
		$this->_get_urls(NULL,$current_id,$form_id,$page_id,$sub_page_id);
		$this->_get_form_types();
		
		$this->data['type']     = $type;
		$this->data['callback'] = $callback = 'edit_' . $type;
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		
		$validation_rules = array_merge($this->_get_rules(), $this->{$this->data['callback']}());

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			if ( $this->form_model->{$this->data['callback']}( $this->data['form'], $this->data['page_current'] ) === TRUE )
			{
				if( $type !== 'form' )
				{
					$url = implode('/', $this->data['page_url']);
					redirect('admin/forms/view/' . $url); exit;
				}
				else
				{
					redirect('admin/forms'); exit;
				}
			}
		}
		else
		{
			$this->load->view('admin/forms/' . $callback,$this->data);
		}
	}
	
    public function delete( $type = 'page', $current_id = NULL, $form_id = NULL)
	{
		if( $type !== 'checklist') { show_404(); }

		$this->VALID_UUIDv4($current_id);
		$this->VALID_UUIDv4($form_id,FALSE);

		$this->_get_multiple_pages_data($form_id,NULL,NULL,$current_id);
		$this->_get_urls(NULL,$current_id,$form_id);
		
		if( $this->data['form']->type !== 'checklist' ) {
			show_error('You can only delete checklists at the moment.',403,'403 Access Denied');
		}
		
		$this->data['type']     = $type;
		$this->data['callback'] = $callback = 'delete_' . $type;
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		
		$validation_rules = $this->_get_rules_delete();

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			if ( $this->form_model->{$this->data['callback']}( $this->data['form'], $this->data['page_current'] ) === TRUE )
			{
				redirect('admin/forms'); exit;
			}
		}
		else
		{
			$this->load->view('admin/forms/' . $callback,$this->data);
		}
	}

	private function _send_email($users, $name, $date, $id)
	{
		// Send email
		$this->data['date']          = $date;
		$this->data['name']         = $name;
		$this->data['checklist_url'] = site_url('form/checklist/list/' . $id);

		$this->load->library('PHPMailerLib');
		$mail = $this->phpmailerlib->load();
		$mail->Subject = $this->config->item('program_name') . ': Checklista';
		$mail->Body = $this->load->view('general/form/send_email',$this->data,TRUE);

		foreach($users as $user)
		{
			$mail->addAddress($user->email,$user->name);
		}

		$mail->send();
	}

	private function arrays_equal($array1, $array2)
	{
			array_multisort($array1);
			array_multisort($array2);
			return ( serialize($array1) === serialize($array2) );
	}
	
	public function survey( $page_id = NULL, $form_id = NULL )
	{
		// $this->data['sidebar']['active'] = FALSE;
		$this->data['body_class'] .= " sidebar-collapse";
		$this->VALID_UUIDv4($page_id);
		$this->VALID_UUIDv4($form_id);
		
		$this->_get_multiple_pages_data($form_id,$page_id);
		$this->_get_urls(NULL,$page_id,$form_id);
		
		$this->data['groups'] = $this->group_model->get_all([],'position');
		$this->data['groups_checked'] = $this->group_model->get_all_by_relationship( 'survey_group', 'page_id', $page_id, FALSE);
		
		$users = [];
		foreach($this->users as $user_id => $user)
		{
			$users[$user_id] = $this->users[$user_id]->name;
		}
		$this->data['users'] = $users;
		$this->data['users_checked'] = $this->group_model->get_all_by_relationship( 'survey_user', 'page_id', $page_id, FALSE, FALSE, 'user_id');

		$this->data['survey_dates'] = $this->form_model->get_all_survey_dates($page_id);
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');

		$validation_rules = $this->_get_survey_rules();

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			$survey_groups = $this->input->post('survey_position[]');
			if( $this->form_model->save( 'survey_group', 'group_id', $page_id, $survey_groups ) !== FALSE )
			{
				
			}

			$delete = [];
			$survey_dates  = [];
			$flipped_dates = [];
			$survey_dates  = $this->input->post('survey_dates');

			if( ! empty($survey_dates) )
			{
				$survey_dates  = explode(', ',$survey_dates);
				$flipped_dates = array_flip($survey_dates);
			}

			$survey_users = $this->input->post('survey_users[]');
			if( $this->form_model->save( 'survey_user', 'user_id', $page_id, $survey_users ) !== FALSE )
			{
				if (!empty($survey_users) && !empty($survey_dates)) {
					$same_users = $this->arrays_equal($this->data['users_checked'], $survey_users);
					$same_dates = $this->arrays_equal($this->data['survey_dates'], $survey_dates);
					if (isset($survey_users) && (!$same_users || !$same_dates)) {
						$this->load->model('user_model');
						$users = $this->user_model->get_all($survey_users);
						$this->_send_email($users, $this->data['form']->name, $this->input->post('survey_dates'), $page_id);
					}
				}
			}

			if( ! empty($this->data['survey_dates']) )
			{
				foreach($this->data['survey_dates'] as $date)
				{
					if( empty($survey_dates) || !in_array($date, $survey_dates) )
					{
						$delete[] = $date;
					}
	
					if( !empty($survey_dates) && in_array($date, $survey_dates) )
					{
						unset($flipped_dates[$date]);
					}
				}
			}

			if( $this->form_model->save( 'survey_dates', 'date', $page_id, array_keys($flipped_dates), $delete, FALSE ) !== FALSE )
			{
				
			}

			$url = implode('/', $this->data['complete_url']);
			redirect('admin/forms/survey/' . $url); exit;
		}
		else
		{
			$this->load->view('admin/forms/survey',$this->data);
		}
	}
	
	private function create_form() {
		return array(
			array(
				'field' => 'forms_types',
				'label' => lang('forms_types'),
				'rules' => array(
					'trim',
					'in_list['.implode(',',array_keys($this->data['forms_types'])).']'
				)
			),
			array(
				'field' => 'forms_global',
				'label' => lang('forms_global'),
				'rules' => array(
					'trim',
					'in_list[0,1]'
				)
			),
		);
	}
	
	private function create_page() { return array(); }
	
	private function create_folder() { return array(); }

	private function edit_form() { return $this->create_form(); }

	private function edit_page() { return $this->create_page(); }

	private function edit_folder() { return $this->create_folder(); }
	
	private function _get_rules()
	{
		return array(
			array(
				'field' => 'forms_name',
				'label' => lang('forms_name'),
				'rules' => array(
					'trim',
					'required',
					'min_length[3]',
					'max_length[128]', // 256
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']',
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
		);
	}
	
	private function _get_question_create_rules()
	{
		return array(
			array(
				'field' => 'forms_name',
				'label' => lang('forms_name'),
				'rules' => array(
					'trim',
					'required',
					'min_length[2]',
					'max_length[250]',
					// 'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']',
				),
				'errors' => array(
					// 'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
		);
	}
	
	private function _get_form_rules()
	{
		return array();
	}
	
	private function _get_survey_rules()
	{
		return array(
			array(
				'field' => 'survey_position[]',
				'label' => lang('forms_survey_position'),
				'rules' => array(
					'trim',
					'in_list['.implode(',',array_keys($this->data['groups']['position'])).']'
				)
			),
			array(
				'field' => 'survey_users[]',
				'label' => lang('forms_survey_users'),
				'rules' => array(
					'trim',
					'in_list['.implode(',',array_keys($this->data['users'])).']'
				)
			),
		);
	}
	
	private function _get_rules_delete()
	{
		return array(
			array(
				'field' => 'confirm_delete',
				'label' => lang('confirm_delete'),
				'rules' => array(
					'required'
				)
			),
		);
	}
}
