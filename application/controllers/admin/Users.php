<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Users extends Admin_Controller
{
	public function __construct()
	{
		parent::__construct();
		$this->data['body_class'] .= 'skin-white fixed';
		$this->load->model('user_model');
	}
	
	public function index()
	{
		$this->view();
	}

	public function stats()
	{
		$this->data['users'] = $this->user_model->get_all();
		$this->load->model('document_model');
		$this->data['documents'] = $this->document_model->document_report();
		$this->load->model('form_model');
		$this->data['checklists'] = $this->form_model->checklist_stats();
		$this->load->model('deviation_model');
		$this->data['deviations'] = $this->deviation_model->deviation_stats();
		$this->load->view('admin/users/stats',$this->data);
	}
	
	public function view()
	{
		$this->data['users'] = $this->user_model->get_all();
		$this->load->view('admin/users/view',$this->data);
	}
	
    public function create()
	{
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->load->model(['companies/company_model','membership_model']);
		$this->config->load('form_validation');
		
		if( $this->config->item('ldap') === TRUE )
		{
			$validation_rules = array_merge($this->_get_rules(), $this->_get_rules_create_ldap());
		}
		else
		{
			$validation_rules = array_merge($this->_get_rules(), $this->_get_rules_create());
		}
		
		$company = $this->company_model->get($this->auth_company_id);
		if( empty($company) ) { show_404(); }
		
		$membership = $this->membership_model->get($company->membership_id);

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			$user_id = UUIDv4();
			
			if ( $this->user_model->create( $user_id ) === TRUE )
			{
				
				if( $this->user_model->assign_membership( $user_id, ['kiv' => $membership->kiv, 'flex' => $membership->flex]) )
				{
					
				}
				
				redirect('admin/users/groups/' . $user_id); exit;
			}
		}
		else
		{
			$this->load->view('admin/users/create',$this->data);
		}
	}

	public function documents( $user_id )
	{
		$this->VALID_UUIDv4($user_id);
		$this->load->model('acl_model');
		$this->data['user'] = $this->user_model->get($user_id);
		if( empty($this->data['user']) ) { show_404(); }
		unset($this->data['user']->passwd);
		if( $this->data['user']->company_id !== $this->auth_company_id ) { show_404(); }
		$acl = $this->acl_model->get_like( 'menu' );
		if( empty($acl) ) { show_404(); }
		$this->load->model('document_model');
		$this->data['documents'] = $this->document_model->get_accessible_document($user_id);
		$this->load->view('admin/users/documents',$this->data);
	}
	
	public function groups( $user_id )
	{
		// $this->output->enable_profiler(TRUE);
		$this->VALID_UUIDv4($user_id);
		$this->load->model('acl_model');
		$this->load->model('group_model');
		
		$this->data['user'] = $this->user_model->get($user_id);
		if( empty($this->data['user']) ) { show_404(); }
		unset($this->data['user']->passwd);
		if( $this->data['user']->company_id !== $this->auth_company_id ) { show_404(); }
		
		$this->data['acl_menu'] = $this->acl_model->get_like( 'menu' );
		if( empty($this->data['acl_menu']) ) { show_404(); }
		
		unset($this->data['acl_menu']['delete']);
		unset($this->data['acl_menu']['author']);
		if( $this->config->item('document_author') )
		{
			unset($this->data['acl_menu']['create']);
			unset($this->data['acl_menu']['update']);
			unset($this->data['acl_menu']['folder']);
			unset($this->data['acl_menu']['all']);
		}
		
		$this->data['groups'] = $this->group_model->get_all();
		if( empty(array_filter($this->data['groups'])) ) { show_404(); }
		
		$this->data['acl_menu_checked'] = $this->acl_model->get_group_checked_like($this->data['acl_menu'], $user_id);
		// var_dump($this->data['acl_menu_checked']);exit;
		
		// var_dump($this->acl_model->get_all());
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		
		$validation_rules = $this->_get_rules_groups();
		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			// What groups are we expecting
			$groups_available = array_merge(
				array_keys($this->data['groups']['position']),
				array_keys($this->data['groups']['department'])
			);
		
			// Grab multiple ACL fields
			$acl = $this->acl_model->_get_acl(array(
				'acl_position[]',
				'acl_department[]',
			));
			
			// Validate ACL. Show 404 in case a user have changed the values.
			if( $acl === FALSE) { show_404(); }
			
			// Make sure that "read" action are always selected for a group 
			$acl = $this->acl_model->_acl_action_code_required($acl, $this->data['acl_menu'], 'read');
			
			// Validate ACL. Show 404 in case a user have changed the values.
			if( $acl === FALSE) { show_404(); }
			
			// What groups have we received
			$acl_groups = array_keys($acl);
			
			// Validate groups. Show 404 in case a user have changed the values.
			if( !empty(array_diff($acl_groups, $groups_available)) ) { show_404(); }
			
			// Make an ACL array so that we can just save it
			$data = $this->acl_model->_get_data_user_group($user_id, $acl);
			
			// Save user groups & ACL
			if ( $this->group_model->save( 'user_group', 'user_id', $user_id, $acl_groups, $groups_available ) 
				&& $this->acl_model->save_user_group( $this->data['acl_menu'], $user_id, $data ) )
			{
				$completed = $this->data['user']->completed + 1;
				if( $completed != 1 )
				{
					if( $completed == 3 )
					{
						$this->user_model->completed($user_id,TRUE);
						$this->_send_password();
					}
					else
					{
						$this->user_model->completed($user_id);
						redirect('admin/users/deviation/' . $user_id); exit;
					}
				}
				
				redirect('admin/users/groups/' . $user_id); exit;
			}
		}
		else
		{
			$this->load->view('admin/users/groups',$this->data);
		}
	}
	
	public function individual( $user_id = NULL )
	{
		// var_dump($this->acl['object']);exit;
		$this->VALID_UUIDv4($user_id);
		$this->load->model(['acl_model','group_model','folder_model']);
		
		$this->data['user'] = $this->user_model->get($user_id);
		if( empty($this->data['user']) ) { show_404(); }
		unset($this->data['user']->passwd);
		if( $this->data['user']->company_id !== $this->auth_company_id ) { show_404(); }
		
		$this->data['acl_menu'] = $this->acl_model->get_like( 'menu' );
		if( empty($this->data['acl_menu']) ) { show_404(); }
		
		unset($this->data['acl_menu']['delete']);
		if( $this->config->item('document_author') )
		{
			unset($this->data['acl_menu']['read']);
			unset($this->data['acl_menu']['create']);
			unset($this->data['acl_menu']['update']);
			unset($this->data['acl_menu']['folder']);
			unset($this->data['acl_menu']['all']);
		}
		else
		{
			unset($this->data['acl_menu']['author']);
		}
		
		$this->data['groups'] = $this->group_model->get_all();
		if( empty(array_filter($this->data['groups'])) ) { show_404(); }
		
		$this->data['acl_menu_checked'] = $this->acl_model->get_object_checked_like($this->data['acl_menu'], $user_id);
	
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$this->load->model('group_model');
		
		$this->data['move']['menu']   = $this->menu_model->get_all();
		$this->data['move']['folder'] = $this->folder_model->get_all($this->data['move']['menu']['id'], TRUE);
		// var_dump($this->data['move']['menu']);exit;
		// var_dump($this->data['move']['folder']);exit;

		$validation_rules = $this->_get_rules_individual();
		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			// Empty data set
			$data = NULL;
			
			// What groups are we expecting
			if( isset($this->data['move']['folder']['id']) )
				$groups_available = array_merge($this->data['move']['menu']['id'],array_keys($this->data['move']['folder']['id']));
			else
				$groups_available = $this->data['move']['menu']['id'];
			
			// var_dump($groups_available);exit;
		
			// Grab multiple ACL fields
			$acl = $this->acl_model->_get_acl(array(
				'acl[]',
			));
			
			if( ! empty($acl) )
			{
				// Validate ACL. Show 404 in case a user have changed the values.
				if( $acl === FALSE) { show_404(); }
				
				if( $this->config->item('document_author') === FALSE )
				{
					// Make sure that "read" action are always selected for a group 
					$acl = $this->acl_model->_acl_action_code_required($acl, $this->data['acl_menu'], 'read');
					
					// Validate ACL. Show 404 in case a user have changed the values.
					if( $acl === FALSE) { show_404(); }
				}
				
				// What groups have we received
				$acl_groups = array_keys($acl);
				
				// Validate groups. Show 404 in case a user have changed the values.
				if( !empty(array_diff($acl_groups, $groups_available)) ) { show_404(); }
				
				// Make an ACL array so that we can just save it
				$data = $this->acl_model->_get_data_user_object($user_id, $acl);				
			}
			
			// Save ACL
			if ( $this->acl_model->save_user_object( $this->data['acl_menu'], $user_id, $data ) !== FALSE )
			{
			}
			
			redirect('admin/users/individual/' . $user_id); exit;
		}
		else
		{
			$this->load->view('admin/users/individual',$this->data);
		}
	}
	
	public function deviation( $user_id )
	{
		$this->VALID_UUIDv4($user_id);
		$this->load->model('acl_model');
		$this->load->model('group_model');
		
		$this->data['user'] = $this->user_model->get($user_id);
		if( empty($this->data['user']) ) { show_404(); }
		unset($this->data['user']->passwd);
		if( $this->data['user']->company_id !== $this->auth_company_id ) { show_404(); }
		
		$this->data['acl_deviation'] = $this->acl_model->get_like( 'deviation' );
		if( empty($this->data['acl_deviation']) ) { show_404(); }
		
		$this->data['groups'] = $this->group_model->get_all();
		if( empty(array_filter($this->data['groups'])) ) { show_404(); }
		
		$this->data['acl_deviation_checked'] = $this->acl_model->get_group_checked_like($this->data['acl_deviation'], $user_id);
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		
		$validation_rules = $this->_get_rules_deviation();
		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			// What groups are we expecting
			$groups_available = array_keys($this->data['groups']['department']);
		
			// Grab multiple ACL fields
			$acl = $this->acl_model->_get_acl(array(
				'acl_create[]',
				'acl_department[]',
			));
			
			// Validate ACL. Show 404 in case a user have changed the values.
			if( $acl === FALSE) { show_404(); }
			
			// Make sure that "create" action are always selected for a group 
			$acl = $this->acl_model->_acl_action_code_required($acl, $this->data['acl_deviation'], 'create');
			
			// Validate ACL. Show 404 in case a user have changed the values.
			if( $acl === FALSE) { show_404(); }
			
			// What groups have we received
			$acl_groups = array_keys($acl);
			
			// Validate groups. Show 404 in case a user have changed the values.
			if( !empty(array_diff($acl_groups, $groups_available)) ) { show_404(); }
			
			// Make an ACL array so that we can just save it
			$data = $this->acl_model->_get_data_user_group($user_id, $acl);
			
			// Save user groups
			$this->group_model->save( 'user_group', 'user_id', $user_id, $acl_groups, $groups_available );
			
			// Save ACL
			if ( $this->acl_model->save_user_group( $this->data['acl_deviation'], $user_id, $data ) !== FALSE )
			{
				$completed = $this->data['user']->completed + 1;
				if( $completed != 1 )
				{
					if( $completed == 3 )
					{
						$this->user_model->completed($user_id,TRUE);
						$this->_send_password();
					}
					else
					{
						$this->user_model->completed($user_id);
						redirect('admin/users/groups/' . $user_id); exit;
					}
				}
				else
				{
					redirect('admin/users/deviation/' . $user_id); exit;
				}
			}
		}
		else
		{
			$this->load->view('admin/users/deviation',$this->data);
		}
	}
	
    public function update( $user_id )
	{
		$this->VALID_UUIDv4($user_id);
		
		$this->data['user'] = $this->user_model->get($user_id);
		if( empty($this->data['user']) ) { show_404(); }
		if( $this->data['user']->company_id !== $this->auth_company_id ) { show_404(); }
		
		$this->data['admin'] = [];
		if( isset($this->groups['security']['Systemadministratör']) )
		{
			$admins = $this->user_model->get_users_by_all_groups([$this->groups['security']['Systemadministratör']]);
			if( ! empty($admins) )
			{
				$this->data['admin'] = array_keys($admins);
			}
		}
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		
		if( $this->config->item('ldap') === TRUE )
		{
			$validation_rules = array_merge($this->_get_rules(), $this->_get_rules_update_ldap());
		}
		else
		{
			$validation_rules = array_merge($this->_get_rules(), $this->_get_rules_update());
		}
		
		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			if ( $this->user_model->update( $this->data['user'] ) === TRUE )
			{
				redirect('admin/users'); exit;
			}
		}
		else
		{
			$this->load->view('admin/users/update',$this->data);
		}
	}
	
    public function delete( $user_id )
	{
		$this->VALID_UUIDv4($user_id);
		
		$this->data['user'] = $this->user_model->get($user_id);
		if( empty($this->data['user']) ) { show_404(); }
		if( $this->data['user']->company_id !== $this->auth_company_id ) { show_404(); }
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$validation_rules = $this->_get_rules_delete();
		
		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			if ( $this->user_model->delete( $user_id ) === TRUE )
			{
				
			}
			
			redirect('admin/users'); exit;
		}
		else
		{
			$this->load->model('document_model');
			$this->data['documents'] = $this->document_model->get_all_documents_by_owner($user_id);
			$this->load->view('admin/users/delete',$this->data);
		}
	}
	
    public function security( $user_id )
	{
		$this->VALID_UUIDv4($user_id);
		$this->load->model('group_model');
		
		$this->data['user'] = $this->user_model->get($user_id);
		if( empty($this->data['user']) ) { show_404(); }
		unset($this->data['user']->passwd);
		if( $this->data['user']->company_id !== $this->auth_company_id ) { show_404(); }
		
		$this->data['groups'] = $this->group_model->get_all();
		if( empty(array_filter($this->data['groups'])) ) { show_404(); }
		
		$this->data['groups_checked'] = $this->group_model->get_all_by_relationship( 'user_group', 'user_id', $user_id, FALSE);
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		
		$config = array(
			array(
				'field' => 'groups_security[]',
				'label' => lang('groups_type_security'),
				'rules' => array(
					'trim',
					'in_list['.implode(',',array_keys($this->data['groups']['security'])).']'
				),
			),
		);
		
		$this->form_validation->set_rules( $config );
		if( $this->form_validation->run() === TRUE )
		{
			$groups_selected = $this->group_model->_get_groups_type_security();
			$groups_available = array_keys($this->data['groups']['security']);
			$this->group_model->save( 'user_group', 'user_id', $user_id, $groups_selected, $groups_available );
			
			redirect('admin/users'); exit;
		}
		else
		{
			$this->load->view('admin/users/security',$this->data);
		}
	}
	
	public function belongings( )
	{
		$this->load->model('acl_model');
		$this->load->model('group_model');
		
		$this->data['acl_menu'] = $this->acl_model->get_like( 'menu' );
		if( empty($this->data['acl_menu']) ) { show_404(); }
		
		unset($this->data['acl_menu']['delete']);
		unset($this->data['acl_menu']['all']);
		unset($this->data['acl_menu']['author']);
		if( $this->config->item('document_author') )
		{
			unset($this->data['acl_menu']['create']);
			unset($this->data['acl_menu']['update']);
			unset($this->data['acl_menu']['folder']);
		}
		
		$this->data['groups'] = $this->group_model->get_all();
		if( empty(array_filter($this->data['groups'])) ) { show_404(); }
		
		$this->data['acl_menu_checked'] = $this->acl_model->get_group_checked_like($this->data['acl_menu'], array_keys($this->users));
		
		$this->load->view('admin/users/belongings',$this->data);
	}
	
	private function _send_password()
	{
		$recovery_code = bin2hex(random_bytes(36));
		
		// Update user record with recovery code and time
		$this->user_model->update_user_raw_data(
			$this->data['user']->user_id,
			[
				'passwd_recovery_code' => $this->authentication->hash_password($recovery_code),
				'passwd_recovery_date' => date('Y-m-d H:i:s')
			]
		);
		
		$link_uri = 'login/recovery_verification/' . $this->data['user']->user_id . '/' . $recovery_code;
		
		// Send email
		$this->data['user_name'] = $this->data['user']->name;
		$this->data['created_by'] = $this->auth_name;
		$this->data['recover_url'] = site_url($link_uri);
		
		$this->load->library('PHPMailerLib');
		$mail = $this->phpmailerlib->load();
		$mail->Subject = 'Användarkonto till ' . $this->config->item('program_name') . ' ' . $this->config->item('program_desc');
		$mail->Body = $this->load->view('login/set_password',$this->data,TRUE);
		$mail->addAddress($this->data['user']->email, $this->data['user']->name);
		// $mail->SMTPDebug = 3;
		if ( ! $mail->send() )
		{
			$this->load->view('admin/users/email_error',$this->data);
		}
		else
		{
			$this->load->view('admin/users/email_success',$this->data);
		}
	}
	
	public function _is_unique()
	{
		return $this->user_model->is_unique();
	}
	
	public function _is_unique_update()
	{
		return $this->user_model->is_unique_update( $this->data['user'] );
	}
	
	public function _change_password()
	{
		$password_new     = $this->input->post('users_password_new');
		$password_confirm = $this->input->post('users_password_confirm');
		
		if( ! empty($password_new) && ! empty($password_confirm) && $password_new !== $password_confirm )
		{
			$this->form_validation->set_message('change_password', lang('error_password_matches'));				
			return FALSE;
		}
		
		if( (empty($password_new) && ! empty($password_confirm)) OR (! empty($password_new) && empty($password_confirm)) )
		{
			$this->form_validation->set_message('change_password', lang('error_password_matches'));				
			return FALSE;
		}
		
		return TRUE;
	}
	
	private function _get_rules()
	{
		return array(
			array(
				'field' => 'users_name',
				'label' => lang('users_name'),
				'rules' => array(
					'trim',
					'required',
					'min_length[3]',
					'max_length[100]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']'
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'users_position',
				'label' => lang('users_position'),
				'rules' => array(
					'trim',
					'max_length[100]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']'
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'users_HSAID',
				'label' => lang('users_HSAID'),
				'rules' => array(
					'trim',
					'max_length[100]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']'
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'users_phone_private',
				'label' => lang('users_phone_private'),
				'rules' => array(
					'trim',
					'max_length[100]',
					'regex_match['.$this->config->item('error_phone_number').']'
				),
				'errors' => array(
					'regex_match' => lang('error_phone_number')
				)
			),
			array(
				'field' => 'users_mobile_private',
				'label' => lang('users_mobile_private'),
				'rules' => array(
					'trim',
					'max_length[100]',
					'regex_match['.$this->config->item('error_phone_number').']'
				),
				'errors' => array(
					'regex_match' => lang('error_phone_number')
				)
			),
			array(
				'field' => 'users_address',
				'label' => lang('users_address'),
				'rules' => array(
					'trim',
					'max_length[100]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']'
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'users_zip',
				'label' => lang('users_zip'),
				'rules' => array(
					'trim',
					'max_length[10]',
					'regex_match['.$this->config->item('error_zip').']'
				),
				'errors' => array(
					'regex_match' => lang('error_zip')
				)
			),
			array(
				'field' => 'users_city',
				'label' => lang('users_city'),
				'rules' => array(
					'trim',
					'max_length[100]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']'
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'users_phone_private',
				'label' => lang('users_phone_private'),
				'rules' => array(
					'trim',
					'max_length[100]',
					'regex_match['.$this->config->item('error_phone_number').']'
				),
				'errors' => array(
					'regex_match' => lang('error_phone_number')
				)
			),
			array(
				'field' => 'users_mobile',
				'label' => lang('users_mobile_private'),
				'rules' => array(
					'trim',
					'max_length[100]',
					'regex_match['.$this->config->item('error_phone_number').']'
				),
				'errors' => array(
					'regex_match' => lang('error_phone_number')
				)
			),
			array(
				'field' => 'users_email_private',
				'label' => lang('users_email_private'),
				'rules' => array(
					'trim',
					'max_length[80]',
					'valid_email'
				)
			),
			array(
				'field' => 'users_password_new',
				'label' => lang('users_password_new'),
				'rules' => array(
					'trim',
					'min_length[8]',
				)
			),
			array(
				'field' => 'users_password_confirm',
				'label' => lang('users_password_confirm'),
				'rules' => array(
					'trim',
					array('change_password', array( $this, '_change_password' ) )
				)
			),
		);
	}
	
	private function _get_rules_create()
	{
		return array(
			array(
				'field' => 'users_email',
				'label' => lang('users_email'),
				'rules' => array(
					'trim',
					'required',
					'max_length[100]',
					'valid_email',
					array('is_unique', array( $this, '_is_unique' ) )
				)
			)
		);
	}
	
	private function _get_rules_update()
	{
		return array(
			array(
				'field' => 'users_email',
				'label' => lang('users_email'),
				'rules' => array(
					'trim',
					'required',
					'max_length[100]',
					'valid_email',
					array('is_unique', array( $this, '_is_unique_update' ) )
				)
			)
		);
	}

	private function _get_rules_create_ldap()
	{
		return array(
			array(
				'field' => 'users_email',
				'label' => lang('users_email_ldap'),
				'rules' => array(
					'trim',
					'max_length[100]',
					'valid_email',
					array('is_unique', array( $this, '_is_unique' ) )
				)
			),
			array(
				'field' => 'users_username',
				'label' => lang('users_username'),
				'rules' => array(
					'trim',
					'required',
				)
			)
		);
	}

	private function _get_rules_update_ldap()
	{
		return array(
			array(
				'field' => 'users_email',
				'label' => lang('users_email_ldap'),
				'rules' => array(
					'trim',
					'max_length[100]',
					'valid_email',
					array('is_unique', array( $this, '_is_unique_update' ) )
				)
			),
			array(
				'field' => 'users_username',
				'label' => lang('users_username'),
				'rules' => array(
					'trim',
					'required',
				)
			)
		);
	}
	
	private function _get_rules_delete()
	{
		return array(
			array(
				'field' => 'confirm_delete',
				'label' => lang('confirm_delete'),
				'rules' => array(
					'required'
				)
			),
		);
	}
	
	private function _get_rules_groups()
	{
		return array(
			array(
				'field' => 'acl_position[]',
				'label' => lang('groups_type_position'),
				'rules' => array(
					'trim',
					'required',
				)
			),
			array(
				'field' => 'acl_department[]',
				'label' => lang('groups_type_department'),
				'rules' => array(
					'trim',
					'required',
				)
			),
		);
	}
	
	private function _get_rules_individual()
	{
		return array(
			array(
				'field' => 'acl[]',
				'label' => lang('menus_menus'),
				'rules' => array(
					'trim',
				)
			),
		);
	}
	
	private function _get_rules_deviation()
	{
		return array(
			array(
				'field' => 'acl_create[]',
				'label' => lang('deviation_permissions_create'),
				'rules' => array(
					'trim',
					'required',
				)
			),
			array(
				'field' => 'acl_department[]',
				'label' => lang('groups_type_department'),
				'rules' => array(
					'trim',
				)
			),
		);
	}
}
