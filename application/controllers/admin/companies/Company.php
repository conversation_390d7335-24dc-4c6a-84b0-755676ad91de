<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Company extends Admin_Controller
{
	private $memberships = [
		'web'     => '67f7ada9-67c3-4813-81cc-d6da20aed18a',
		'flex'    => 'bdfc18eb-215b-423d-8693-e4c4e6a34c45',
		'manuell' => 'de8ee482-655c-4055-818f-2ab54309fdc2',
	];
	
	public function __construct()
	{
		parent::__construct();
		$this->data['body_class'] .= 'skin-white fixed';
		$this->load->model(array('companies/company_model','membership_model','speciality_model','database_model'));
	}
	
	public function index()
	{
		$this->view();
	}
	
	public function view()
	{
		$this->data['companies']   = $this->company_model->get_all_active_companies();
		$this->data['memberships'] = $this->membership_model->get_all();
		$this->data['users']       = $this->user_model->get_all_active();
		$this->load->view('admin/company/view',$this->data);
	}
	
	public function create()
	{
		$this->data['company']      = NULL;
		$this->data['memberships']  = $this->membership_model->get_all_dropdown();
		$this->data['specialities'] = $this->speciality_model->get_all_dropdown();
		$this->data['databases']    = $this->database_model->get_all_dropdown();
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$validation_rules = $this->_get_rules();

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			$company_id = UUIDv4();
			
			if ( $this->company_model->create( $company_id ) === TRUE )
			{
				redirect('admin/companies/company'); exit;
			}
		}
		else
		{
			$this->load->view('admin/company/create',$this->data);
		}
	}
	
	public function update( $company_id )
	{
		$this->VALID_UUIDv4($company_id);
		
		$this->data['company'] = $this->company_model->get($company_id);
		if( empty($this->data['company']) ) { show_404(); }
		$this->data['memberships']  = $this->membership_model->get_all_dropdown();
		$this->data['specialities'] = $this->speciality_model->get_all_dropdown();
		$this->data['databases']    = $this->database_model->get_all_dropdown();
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$validation_rules = $this->_get_rules();
		$validation_rules[] = array(
			'field' => 'company_active',
			'label' => lang('company_active'),
			'rules' => array(
				'trim',
				'required',
				'in_list[0,1]'
			)
		);
		
		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			if ( $this->company_model->update( $this->data['company'] ) === TRUE )
			{
				redirect('admin/companies/company'); exit;
			}
		}
		else
		{
			$this->load->view('admin/company/update',$this->data);
		}
	}
	
	public function export($format = 'pdf', $type = NULL)
	{
		if( $format !== 'pdf' ) { show_404(); }
		if( ! in_array($type, ['all', 'flex', 'web', 'manuell']) ) { show_404(); }
		
		$func = 'export_' . $format;
		
		switch ($type) {
			case 'flex':
				$filter = $this->memberships['flex'];
			break;
			case 'web':
				$filter = $this->memberships['web'];
			break;
			case 'manuell':
				$filter = $this->memberships['manuell'];
			break;
			default:
				$filter = NULL;
			break;
		}
		
		$this->data['companies'] = $this->company_model->get_all_active_companies($filter, TRUE);
		if( empty($this->data['companies']) ) { show_404(); }
		
		$this->{$func}();
	}
	
	private function export_pdf()
	{
		$size       = 68;
		$start_x	= 7;
		$start_y	= 5;
		$offset_x	= 0;
		$column		= 0;
		$count_page	= 0;
		
		$this->load->library('TCPDFLib');
		
		// create new PDF document
		$pdf = new TCPDFLib(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
		
		// set margins
		$pdf->setMargins(0, 0, 0);
		$pdf->setHeaderMargin(0);
		$pdf->setFooterMargin(0);
		
		// disable
		$pdf->setPrintHeader(false);
		$pdf->setPrintFooter(false);
		$pdf->setAutoPageBreak(false);
		
		$pdf->setDisplayMode('fullpage', 'single');
		$pdf->setTitle('PDF-utskrift');
		$pdf->setAuthor('KVALPRAK AB');
		
		$pdf->setFont('Helvetica', '', 10.0);
		$pdf->AddPage();
		
		foreach($this->data['companies'] as $company)
		{
			$address2_off = 0;
			
			if (strlen($company->address2)) {
				$address2_off = -5;
			}
			
			$pdf->setFont('Helvetica', '', 10.0);
			$pdf->Text($start_x + $column * $size, $start_y + $offset_x + $address2_off, $company->name);
			$pdf->Text($start_x + $column * $size, $start_y + $offset_x + 5 + $address2_off, $company->alias);
			$pdf->Text($start_x + $column * $size, $start_y + $offset_x + 10 + $address2_off, $company->address);
			if (strlen($company->address2)) {
				$pdf->Text($start_x + $column * 68, $start_y + $offset_x + 10, $company->address2);
			}
			
			// City address (strip all but numbers and spaces)
			$pdf->Text($start_x + $column * $size, $start_y + $offset_x + 15, preg_replace('/[^0-9 ]/i', '', $company->zip) . ' ' . $company->city);
			
			// Print type
			$pdf->setFont('Helvetica', '', 7.0);
			
			$type = NULL;
			if( $company->membership_id === $this->memberships['web'] )
				$type = 'W';
			
			if( $company->membership_id === $this->memberships['flex'] )
				$type = 'F';
			
			if( $company->membership_id === $this->memberships['manuell'] )
				$type = 'M';
			
			$pdf->Text($start_x + $column * 68, $start_y + $offset_x + 20, $type);
			
			// Add to count
			$count_page++;
			
			// Check end of page
			if ($count_page == 24) {
				$count_page = 0;
				$column     = 0;
				$offset_x   = 0;
				$pdf->AddPage();
				continue;
			}
			
			// Move
			if ($column == 0) {
				$column = 1;
				continue;
			}
			if ($column == 1) {
				$column = 2;
				continue;
			}
			if ($column == 2) {
				$column = 0;
				$offset_x += 38;
				continue;
			}
		}
		
		$pdf->Close();
		$pdf->Output('unicode.pdf', 'I');
	}
	
	public function delete()
	{
	}

	public function _is_unique()
	{
		return $this->company_model->is_unique( $this->data['company'] );
	}
	
	private function _get_rules()
	{
		return array(
			array(
				'field' => 'company_name',
				'label' => lang('company_name'),
				'rules' => array(
					'trim',
					'required',
					'min_length[3]',
					'max_length[80]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']',
					array('is_unique', array( $this, '_is_unique' ) )
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'company_alias',
				'label' => lang('company_alias'),
				'rules' => array(
					'trim',
					'max_length[80]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']'
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'company_address',
				'label' => lang('company_address'),
				'rules' => array(
					'trim',
					'required',
					'max_length[80]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']'
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'company_address2',
				'label' => lang('company_address2'),
				'rules' => array(
					'trim',
					'max_length[80]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']'
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'company_zip',
				'label' => lang('company_zip'),
				'rules' => array(
					'trim',
					'required',
					'max_length[10]',
					'regex_match['.$this->config->item('error_zip').']'
				),
				'errors' => array(
					'regex_match' => lang('error_zip')
				)
			),
			array(
				'field' => 'company_city',
				'label' => lang('company_city'),
				'rules' => array(
					'trim',
					'required',
					'max_length[50]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']'
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'company_phone',
				'label' => lang('company_phone'),
				'rules' => array(
					'trim',
					'max_length[80]',
					'regex_match['.$this->config->item('error_phone_number').']'
				),
				'errors' => array(
					'regex_match' => lang('error_phone_number')
				)
			),
			array(
				'field' => 'company_fax',
				'label' => lang('company_fax'),
				'rules' => array(
					'trim',
					'max_length[80]',
					'regex_match['.$this->config->item('error_phone_number').']'
				),
				'errors' => array(
					'regex_match' => lang('error_phone_number')
				)
			),
			array(
				'field' => 'company_email',
				'label' => lang('company_email'),
				'rules' => array(
					'trim',
					'max_length[80]',
					'valid_email'
				)
			),
			array(
				'field' => 'company_membership',
				'label' => lang('company_membership'),
				'rules' => array(
					'trim',
					'required',
					'in_list['.implode(',',array_keys($this->data['memberships']['memberships'])).']'
				)
			),
			array(
				'field' => 'company_database',
				'label' => lang('company_database'),
				'rules' => array(
					'trim',
					'required',
					'in_list['.implode(',',array_keys($this->data['databases']['databases'])).']'
				)
			),
		);
	}
}
