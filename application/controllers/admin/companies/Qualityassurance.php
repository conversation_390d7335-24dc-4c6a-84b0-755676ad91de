<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Qualityassurance extends Admin_Controller
{
	public function __construct()
	{
		parent::__construct();
		$this->data['body_class'] .= 'skin-white fixed';
	}

	public function index($fiscal = NULL)
	{
		$this->view($fiscal);
	}

	public function view($fiscal = NULL)
	{
		if(preg_match('@^\d{4}$@', $fiscal))
			$fiscal = intval($fiscal);
		else
			$fiscal = intval(date('Y')) - 1;

		$this->load->model(['companies/company_model', 'companies/qualityassurance_model', 'membership_model']);

		$this->data['fiscal']      = $fiscal;
		$this->data['memberships'] = $this->membership_model->get_all();
		$this->data['users']       = $this->qualityassurance_model->getAllByFiscalYear($fiscal);

		$this->load->view('admin/company/qualityassurance/view',$this->data);
	}

	public function form($fiscal = NULL, $user_id = NULL)
	{
		$this->VALID_UUIDv4($user_id);
		
		if(!preg_match('@^\d{4}$@', $fiscal)) { show_404(); }

		$this->data['fiscal'] = $fiscal;

		$this->load->model(['form_model', 'user_model']);

		$this->data['user'] = $this->user_model->get($user_id);
		if( empty($this->data['user']) ) { show_404(); }

		$form_id = '8d8b7df4-36ae-439c-b6c2-bbfce4226a14';

		$this->data['form'] = $this->form_model->get($form_id);
		if( empty($this->data['form']) ) { show_404(); }

		$this->form = $this->form_model->get_menu_structure($form_id);
		if( empty($this->form) ) { show_404(); }

		$this->load->helper(['form','forms']);

		$ids = array_keys($this->form['all']);

		$this->questions = $this->form_model->get_question_structures($ids);

		$this->data['questions'] = $this->questions['structure'];
		$this->data['options']   = $this->questions['options'];
		$this->data['menu']      = $this->form['structure'];
		$this->data['selected']  = $this->form_model->get_answers_flex_by_fiscal(
			$this->data['form']->survey_id,
			$fiscal,
			$user_id
		);

		$this->load->view('admin/company/qualityassurance/form', $this->data);
	}
}