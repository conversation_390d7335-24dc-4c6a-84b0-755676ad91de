<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Users extends Admin_Controller
{
	public function __construct()
	{
		parent::__construct();
		$this->data['body_class'] .= 'skin-white fixed';
	}
	
	public function view($company_id)
	{
		$this->data['company_id'] = $company_id;
		
		$this->load->model(['companies/company_model']);
		$this->data['company'] = $this->company_model->get($company_id);
		if( empty($this->data['company']) ) { show_404(); }
		$this->data['users'] = $this->user_model->get_all([],FALSE,$company_id);
		$this->load->view('admin/company/users/view',$this->data);
	}
	
    public function create($company_id)
	{
		$this->data['company_id'] = $company_id;
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->load->model(['companies/company_model','membership_model']);
		$this->config->load('form_validation');
		$validation_rules = array_merge($this->_get_rules(), $this->_get_rules_create());
		
		$groups = $this->group_model->get_all([], NULL, [], FALSE, $company_id);
		if( empty($groups['department']) OR empty($groups['position']) ) { show_404(); }
		if( count($groups['department']) !== 1 OR count($groups['position']) !== 1 ) { show_404(); }
		
		$this->data['company'] = $this->company_model->get($company_id);
		if( empty($this->data['company']) ) { show_404(); }
		
		$membership = $this->membership_model->get($this->data['company']->membership_id);
		
		$department = array_keys($groups['department']);
		$department = array_pop($department);
		$position = array_keys($groups['position']);
		$position = array_pop($position);

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			$user_id = UUIDv4();
			
			if ( $this->user_model->create( $user_id, $company_id ) === TRUE )
			{
				
			}
			
			if( $this->user_model->assign_roles($position, $department, $user_id) === TRUE )
			{
				
			}
			
			if( $this->user_model->assign_membership( $user_id, ['kiv' => $membership->kiv, 'flex' => $membership->flex]) )
			{
				
			}
			
			if( count($this->user_model->get_all([], FALSE, $company_id)) === 1 )
			{
				$menus = $this->menu_model->get_all($company_id);
				if( $this->menu_model->assign_roles($menus, $user_id) === TRUE )
				{
					
				}
			}
			
			$this->_send_password($user_id);
		}
		else
		{
			$this->load->view('admin/company/users/create',$this->data);
		}
	}
	
    public function update($company_id, $user_id)
	{
		$this->VALID_UUIDv4($company_id);
		$this->VALID_UUIDv4($user_id);
		
		$this->data['user'] = $this->user_model->get($user_id);
		if( empty($this->data['user']) ) { show_404(); }
		if( $this->data['user']->company_id !== $company_id ) { show_404(); }
		
		$this->load->model(['companies/company_model']);
		$this->data['company'] = $this->company_model->get($company_id);
		if( empty($this->data['company']) ) { show_404(); }
		
		$this->data['company_id'] = $company_id;
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$validation_rules = array_merge($this->_get_rules(), $this->_get_rules_update());
		
		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			if ( $this->user_model->update( $this->data['user'] ) === TRUE )
			{
				redirect('admin/companies/users/view/' . $company_id); exit;
			}
		}
		else
		{
			$this->load->view('admin/company/users/update',$this->data);
		}
	}
	
    public function delete()
	{
	}
	// @TOOD: 
	private function _send_password($user_id)
	{
		$recovery_code = bin2hex(random_bytes(36));
		$user_name  = $this->user_model->_get_users_name();
		$user_email = $this->user_model->_get_users_email();
		
		// Update user record with recovery code and time
		$this->user_model->update_user_raw_data(
			$user_id,
			[
				'passwd_recovery_code' => $this->authentication->hash_password($recovery_code),
				'passwd_recovery_date' => date('Y-m-d H:i:s')
			]
		);
		
		$link_uri = 'login/recovery_verification/' . $user_id . '/' . $recovery_code;
		
		// Send email
		$this->data['user_name']   = $user_name;
		$this->data['recover_url'] = site_url($link_uri);
		
		$this->load->library('PHPMailerLib');
		$mail = $this->phpmailerlib->load();
		$mail->Subject = 'Användarkonto till ' . $this->config->item('program_name') . ' ' . $this->config->item('program_desc');
		$mail->Body = $this->load->view('login/flex_user',$this->data,TRUE);
		$mail->addAddress($user_email, $user_name);
		// $mail->SMTPDebug = 3;
		if ( ! $mail->send() )
		{
			$this->load->view('admin/company/users/email_error',$this->data);
		}
		else
		{
			$this->load->view('admin/company/users/email_success',$this->data);
		}
	}
	
	public function _is_unique()
	{
		return $this->user_model->is_unique();
	}
	
	public function _is_unique_update()
	{
		return $this->user_model->is_unique_update( $this->data['user'] );
	}
	
	public function _change_password()
	{
		$password_new     = $this->input->post('users_password_new');
		$password_confirm = $this->input->post('users_password_confirm');
		
		if( ! empty($password_new) && ! empty($password_confirm) && $password_new !== $password_confirm )
		{
			$this->form_validation->set_message('change_password', lang('error_password_matches'));				
			return FALSE;
		}
		
		if( (empty($password_new) && ! empty($password_confirm)) OR (! empty($password_new) && empty($password_confirm)) )
		{
			$this->form_validation->set_message('change_password', lang('error_password_matches'));				
			return FALSE;
		}
		
		return TRUE;
	}
	
	private function _get_rules()
	{
		return array(
			array(
				'field' => 'users_name',
				'label' => lang('users_name'),
				'rules' => array(
					'trim',
					'required',
					'min_length[3]',
					'max_length[100]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']'
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'users_position',
				'label' => lang('users_position'),
				'rules' => array(
					'trim',
					'max_length[100]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']'
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'users_HSAID',
				'label' => lang('users_HSAID'),
				'rules' => array(
					'trim',
					'max_length[100]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']'
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'users_phone_private',
				'label' => lang('users_phone_private'),
				'rules' => array(
					'trim',
					'max_length[100]',
					'regex_match['.$this->config->item('error_phone_number').']'
				),
				'errors' => array(
					'regex_match' => lang('error_phone_number')
				)
			),
			array(
				'field' => 'users_mobile_private',
				'label' => lang('users_mobile_private'),
				'rules' => array(
					'trim',
					'max_length[100]',
					'regex_match['.$this->config->item('error_phone_number').']'
				),
				'errors' => array(
					'regex_match' => lang('error_phone_number')
				)
			),
			array(
				'field' => 'users_address',
				'label' => lang('users_address'),
				'rules' => array(
					'trim',
					'max_length[100]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']'
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'users_zip',
				'label' => lang('users_zip'),
				'rules' => array(
					'trim',
					'max_length[10]',
					'regex_match['.$this->config->item('error_zip').']'
				),
				'errors' => array(
					'regex_match' => lang('error_zip')
				)
			),
			array(
				'field' => 'users_city',
				'label' => lang('users_city'),
				'rules' => array(
					'trim',
					'max_length[100]',
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']'
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'users_phone_private',
				'label' => lang('users_phone_private'),
				'rules' => array(
					'trim',
					'max_length[100]',
					'regex_match['.$this->config->item('error_phone_number').']'
				),
				'errors' => array(
					'regex_match' => lang('error_phone_number')
				)
			),
			array(
				'field' => 'users_mobile',
				'label' => lang('users_mobile_private'),
				'rules' => array(
					'trim',
					'max_length[100]',
					'regex_match['.$this->config->item('error_phone_number').']'
				),
				'errors' => array(
					'regex_match' => lang('error_phone_number')
				)
			),
			array(
				'field' => 'users_email_private',
				'label' => lang('users_email_private'),
				'rules' => array(
					'trim',
					'max_length[80]',
					'valid_email'
				)
			),
			array(
				'field' => 'users_password_new',
				'label' => lang('users_password_new'),
				'rules' => array(
					'trim',
					'min_length[8]',
				)
			),
			array(
				'field' => 'users_password_confirm',
				'label' => lang('users_password_confirm'),
				'rules' => array(
					'trim',
					array('change_password', array( $this, '_change_password' ) )
				)
			),
		);
	}
	
	private function _get_rules_create()
	{
		return array(
			array(
				'field' => 'users_email',
				'label' => lang('users_email'),
				'rules' => array(
					'trim',
					'required',
					'max_length[100]',
					'valid_email',
					array('is_unique', array( $this, '_is_unique' ) )
				)
			)
		);
	}
	
	private function _get_rules_update()
	{
		return array(
			array(
				'field' => 'users_email',
				'label' => lang('users_email'),
				'rules' => array(
					'trim',
					'required',
					'max_length[100]',
					'valid_email',
					array('is_unique', array( $this, '_is_unique_update' ) )
				)
			)
		);
	}
}
