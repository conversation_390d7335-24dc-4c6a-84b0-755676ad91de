<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Reports extends User_Controller
{
	public $severity = ['warning','critical', 'success'];
	public $type     = ['documents','education','draft','autosave','eventanalysis','eventanalysis_actionlist','checklist','tasks'];

	public function __construct()
	{
		parent::__construct();
		$this->data['body_class'] .= 'skin-white fixed';
		$this->data['sidebar']['module'] = 'document';
		$this->lang->load('date');
	}
	
	public function display()
	{
		$this->load->view('general/reports/display', $this->data);
	}
	
	public function index( $severity = NULL, $type = NULL )
	{
		$this->data['sidebar']['active'] = false;
		if( ! in_array($severity, $this->severity, TRUE) ) { show_404(); }
		if( $type === NULL )
		{
			$this->summary($severity);
		}
		else
		{
			if( ! in_array($type, $this->type, TRUE) ) { show_404(); }
			$type .= '_display';
			$this->{$type}($severity);
		}
	}
	
	// @STEP2:
	private function summary($severity)
	{
		
	}

	public function login()
	{
		$this->load->view('general/reports/login', $this->data);
	}

	public function deviation()
	{
		$this->data['sidebar']['module'] = 'deviation';
		$this->load->model('deviation_model');
		$deviation_reoprt = $this->deviation_model->report_data( $this->acl['deviation']['read'] );
		$deviations = $deviation_reoprt['deviations'];
		$deviation_overview = $deviation_reoprt['overview'];
		$this->data['fields']  = $this->deviation_model->getDeviationFieldsByPage(array(1,2,3));
		$this->data['groups']  = $this->group_model->get_all();
		$this->data['options'] = $this->deviation_model->getDropdown();

		$fields = $this->data['fields'];
		$options = $this->data['options'];

		if( ! empty($fields['type']['department']) )
		{
			foreach($fields['type']['department'] as $department)
			{
				foreach($this->acl['deviation']['create'] as $d)
				{
					if( isset($this->data['groups']['department'][$d]) ) {
						$dropdown = new stdClass();
						$dropdown->option_id = $d;
						$dropdown->name      = $this->data['groups']['department'][$d];
						$options[$department][$d] = $dropdown;
					}
				}
			}
		}

		if( ! empty($fields['type']['users']) )
		{
			foreach($fields['type']['users'] as $users)
			{
				foreach($this->users as $user)
				{
					$dropdown = new stdClass();
					$dropdown->option_id = $user->user_id;
					$dropdown->name      = $user->name;
					$options[$users][$user->user_id] = $dropdown;
				}
			}
		}

		foreach ($deviations as $key => $deviation) {
			foreach ($deviation as $field => $answer) {
				if ($answer->answer && array_key_exists($field, $options)) {
					$is_found = false;
					foreach ( array_values($options[$field]) as $element ) {
						if ( $answer->answer == $element->option_id ) {
							$answer->answer = $element->name;
							$is_found = true;
							break;
						}
					}
					if ($is_found == false) {
						$answer->answer = lang('other');
					}
				}
			}
		}

		$this->data['deviation_overview'] = $deviation_overview;
		$this->data['deviations'] = $deviations;

		$this->load->view('general/reports/deviation', $this->data);
	}

	public function documents_overview()
	{
		$this->load->model('document_model');
		$this->data['documents_overview'] = $this->document_model->document_report();
		$this->load->view('general/reports/documents_overview', $this->data);
	}

	public function documents()
	{
		$extendedSearch = [];
		$this->data['documents_created_start']     = $this->input->get('kcs') ? $this->input->get('kcs') : NULL;
		$this->data['documents_created_end']       = $this->input->get('kce') ? $this->input->get('kce') : NULL;
		$this->data['documents_edited_start']     = $this->input->get('kms') ? $this->input->get('kms') : NULL;
		$this->data['documents_edited_end']       = $this->input->get('kme') ? $this->input->get('kme') : NULL;
		$this->data['documents_valid_until_start'] = $this->input->get('kvs') ? $this->input->get('kvs') : NULL;
		$this->data['documents_valid_until_end']   = $this->input->get('kve') ? $this->input->get('kve') : NULL;
		$this->data['documents_type_search']       = isset($_GET['dtype']) && strlen($_GET['dtype'])>0 ? intval($_GET['dtype']) : NULL;
		$this->data['documents_category_search']   = isset($_GET['dcategory']) && strlen($_GET['dcategory'])>0 ? $_GET['dcategory'] : NULL;
		$this->data['documents_owner']   = isset($_GET['owner']) && strlen($_GET['owner'])>0 ? $_GET['owner'] : NULL;
		$this->data['body_class'] .= " sidebar-collapse";
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$this->_get_document_type_data();
		$this->load->model('document_model');
		$this->data['documents_category'] = array_merge($this->document_model->get_document_category(), 
			array(NULL => ' - ' . lang('documents_category') . ' - ')
		);

		$this->data['documents_type'][NULL] = ' - ' . lang('documents_type') . ' - ';

		$this->data['groups'] = $this->group_model->get_all();
		$this->data['users'] = array_merge(array(
			NULL => ' - ' . lang('documents_owner') . ' - ',
		), $this->user_model->get_users_by_all_groups(array_merge(
			array_keys($this->data['groups']['position']),
			array_keys($this->data['groups']['department'])
		)));
		
		$config = array(
			array(
				'field' => 'kcs',
				'label' => lang('documents_created'),
				'rules' => array(
					'trim',
					'regex_match['.$this->config->item('error_date').']',
				),
				'errors' => array(
					'regex_match' => lang('error_date')
				)
			),
			array(
				'field' => 'kce',
				'label' => lang('documents_created'),
				'rules' => array(
					'trim',
					'regex_match['.$this->config->item('error_date').']',
				),
				'errors' => array(
					'regex_match' => lang('error_date')
				)
			),
			array(
				'field' => 'kms',
				'label' => lang('documents_created'),
				'rules' => array(
					'trim',
					'regex_match['.$this->config->item('error_date').']',
				),
				'errors' => array(
					'regex_match' => lang('error_date')
				)
			),
			array(
				'field' => 'kme',
				'label' => lang('documents_created'),
				'rules' => array(
					'trim',
					'regex_match['.$this->config->item('error_date').']',
				),
				'errors' => array(
					'regex_match' => lang('error_date')
				)
			),
			array(
				'field' => 'kvs',
				'label' => lang('documents_valid_until'),
				'rules' => array(
					'trim',
					'regex_match['.$this->config->item('error_date').']',
				),
				'errors' => array(
					'regex_match' => lang('error_date')
				)
			),
			array(
				'field' => 'kve',
				'label' => lang('documents_valid_until'),
				'rules' => array(
					'trim',
					'regex_match['.$this->config->item('error_date').']',
				),
				'errors' => array(
					'regex_match' => lang('error_date')
				)
			),
			array(
				'field' => 'dtype',
				'label' => lang('documents_type'),
				'rules' => array(
					'trim',
					'in_list['.implode(',', array_keys($this->data['documents_type'])).']'
				)
			),
			array(
				'field' => 'dcategory',
				'label' => lang('documents_category'),
				'rules' => array(
					'trim',
					'in_list['.implode(',', array_keys($this->data['documents_category'])).']'
				)
			),
		);
		
		$this->form_validation->set_data([
			'kcs' => $this->data['documents_created_start'],
			'kce' => $this->data['documents_created_end'],
			'kms' => $this->data['documents_edited_start'],
			'kme' => $this->data['documents_edited_end'],
			'kvs' => $this->data['documents_valid_until_start'],
			'kve' => $this->data['documents_valid_until_end'],
			'dtype' => $this->data['documents_type_search'],
			'dcategory' => $this->data['documents_category_search'],
		]);
		$this->form_validation->set_rules($config);
		if( $this->form_validation->run() === TRUE )
		{
			if( ! empty($this->data['documents_created_start']) )
			{
				$extendedSearch[] = [
					'column' => 'documents.created_date',
					'match' => '>=',
					'find' => $this->data['documents_created_start']
				];
			}
			if( ! empty($this->data['documents_created_end']) )
			{
				$extendedSearch[] = [
					'column' => 'documents.created_date',
					'match' => '<=',
					'find' => $this->data['documents_created_end']
				];
			}
			if( ! empty($this->data['documents_edited_start']) )
			{
				$extendedSearch[] = [
					'column' => 'documents.edited_date',
					'match' => '>=',
					'find' => $this->data['documents_edited_start']
				];
			}
			if( ! empty($this->data['documents_edited_end']) )
			{
				$extendedSearch[] = [
					'column' => 'documents.edited_date',
					'match' => '<=',
					'find' => $this->data['documents_edited_end']
				];
			}
			if( ! empty($this->data['documents_valid_until_start']) )
			{
				$extendedSearch[] = [
					'column' => 'documents.valid_until',
					'match' => '>=',
					'find' => $this->data['documents_valid_until_start']
				];
			}
			if( ! empty($this->data['documents_valid_until_end']) )
			{
				$extendedSearch[] = [
					'column' => 'documents.valid_until',
					'match' => '<=',
					'find' => $this->data['documents_valid_until_end']
				];
			}
			if( $this->data['documents_type_search'] !== NULL )
			{
				$extendedSearch[] = [
					'column' => 'document_type',
					'match' => '=',
					'find' => $this->data['documents_type_search']
				];
			}
			if( $this->data['documents_category_search'] !== NULL )
			{
				$extendedSearch[] = [
					'column' => 'document_category',
					'match' => '=',
					'find' => UUID_TO_BIN($this->data['documents_category_search'])
				];
			}

			if( $this->data['documents_owner'] !== NULL )
			{
				$extendedSearch[] = [
					'column' => 'documents.owner',
					'match' => '=',
					'find' => UUID_TO_BIN($this->data['documents_owner'])
				];
			}
		}

		$this->load->model('document_model');
		$this->data['documents'] = $this->document_model->search(
			NULL,
			array_merge($this->groups['types']['bin']['position'], $this->groups['types']['bin']['department']),
			$this->groups['types']['bin']['position'],
			$this->groups['types']['bin']['department'],
			$extendedSearch
		);

		$this->load->view('general/reports/documents', $this->data);
	}
	
	public function education($filter = NULL)
	{
		$this->load->model('document_model');
		$this->data['education'] = $education_done = $this->report_model->education_done();
		// var_dump($this->data['education']);exit;
		if( ! empty($education_done['document_id']) )
		{
			$this->data['documents'] = $this->document_model->get_all( array_keys($education_done['document_id']) );
		}
		// var_dump($this->data['education'],$this->data['documents'], $education_done['todo'], $education_done['done']);
		// var_dump($this->data['education']);
		$this->load->view('general/reports/education', $this->data);
	}
	
	public function checklists($filter = NULL)
	{
		$this->data['sidebar']['module'] = 'checklist';
		$this->load->model(['user_messages_model','form_model']);
		$this->data['checklist_overview'] = $this->form_model->checklist_report();
		foreach ($this->data['checklist_overview'] as $checklist) {
			$checklist->user = array_key_exists($checklist->user, $this->users) ? $this->users[$checklist->user]->name : null;
		}
		$this->data['checklists'] = $this->user_messages_model->get_all( array_keys($this->users), 'checklist', 'create' );
		if( ! empty($this->data['checklists']['id']['checklist']) )
		{
			$this->data['pages'] = $this->form_model->get_pages( array_keys($this->data['checklists']['id']['checklist']) );
		}
		// var_dump($this->data['checklists'],$this->data['pages']);exit;
		$this->load->view('general/reports/checklists', $this->data);
	}

	private function tasks_display($severity)
	{
		$this->data['report']['severity'] = $severity;
		$this->data['tasks'] = $this->data['report']['messages']['tasks'][$severity];
		$this->load->model('task_model');
		$this->data['tasks'] = $this->task_model->get_all(array_keys($this->data['tasks']), array('draft'));
		$this->load->view('general/reports/tasks_display', $this->data);

	}
	
	private function documents_display($severity)
	{
		$this->data['report']['severity'] = $severity;
		$this->data['documents'] = $this->data['report']['documents'][$severity];
		$this->load->view('general/reports/documents_display', $this->data);
	}
	
	private function education_display($severity)
	{
		$this->load->model('document_model');
		$this->data['report']['severity'] = $severity;
		$this->data['education'] = array_merge($this->education['todo'], $this->education['done']);
		if( empty($this->data['education']) ) { redirect(); exit; }
		$this->data['documents'] = $this->document_model->get_all( array_keys($this->data['education']) );
		// var_dump($this->data['education'],$this->data['documents']);
		$this->load->view('general/reports/education_display', $this->data);
	}
	
	private function draft_display($severity)
	{
		if( $severity == 'warning' || $severity == 'success' )
			$this->data['report']['url'] = 'documents';
		else
			$this->data['report']['url'] = 'documents/update';
		
		$this->load->model('document_model');
		$this->data['report']['severity'] = $severity;
		$this->data['draft'] = $this->data['report']['messages']['documents'][$severity];
		if( empty($this->data['draft']) ) { redirect(); exit; }
		$this->load->model('user_messages_model');
		if ($severity == 'success') {
			$this->data['documents'] = $this->document_model->get_all( array_keys($this->data['draft']), ['published'] );
			$this->user_messages_model->remove_all_actions( 'documents', 'accepted', $this->auth_user_id );
		} else {
			$this->data['documents'] = $this->document_model->get_all( array_keys($this->data['draft']), ['waiting-approval', 'draft'] );
			foreach($this->data['draft'] as $document_id => $d) {
				if (!isset($this->data['documents'][$document_id])) {
					$this->user_messages_model->remove( 'documents', $this->auth_user_id, $document_id );
				}
			}
		}
		// var_dump($this->data['documents'],$this->data['draft']);exit;
		$this->load->view('general/reports/draft', $this->data);
	}
	
	private function autosave_display($severity)
	{
		// if( $severity == 'warning' )
		// else
			// $this->data['report']['url'] = 'documents/update';
		$this->load->helper('form');
		$this->load->model('document_model');
		$this->data['report']['severity'] = $severity;
		$this->data['autosave'] = $this->data['report']['messages']['autosave'][$severity];
		// var_dump($this->data['autosave']);
		if( empty($this->data['autosave']) ) { redirect(); exit; }
		$this->data['documents'] = array_merge($this->document_model->get_all_by_parent_id( array_keys($this->data['autosave']), ['auto-draft'], 'parent_id' ), 
			$this->document_model->get_all( array_keys($this->data['autosave']), [ 'draft'] ));
		// var_dump($this->data['documents']);
		$this->load->model('user_messages_model');
		$this->data['autosave'] = array_filter($this->data['autosave'], function($record, $key) {
			if (empty($this->data['documents'][$key])) {
				$this->user_messages_model->remove('autosave', $this->auth_user_id, $key);
				return false;
			} else {
				return true;
			}
		}, ARRAY_FILTER_USE_BOTH);
		$this->data['documents_category'] = array_merge($this->document_model->get_document_category(), 
			array(NULL => ' - ' . lang('documents_category') . ' - ')
		);
		foreach($this->data['autosave'] as $document_id => $d) {
			$this->data['documents'][$document_id]->is_only_office = $this->document_model->get_main_attachment($this->data['documents'][$document_id]->document_id);
		}
		$this->load->view('general/reports/autosave', $this->data);
	}
	
	private function eventanalysis_display($severity)
	{
		$this->data['report']['severity'] = $severity;
		$this->data['eventanalysis'] = $this->data['report']['messages']['eventanalysis'][$severity];
		$db = [
			'hostname' => $this->db->hostname,
			'database' => $this->db->database,
			'username' => $this->db->username,
			'password' => $this->db->password,
		];
		$this->load->library('eventanalysislib', $db);
		// ['f505d7c9-fd82-434a-a22f-151b1bb837af','d5df382c-aff2-41cc-88e6-d282554024a0']
		$this->data['events'] = $this->eventanalysislib->getAllByID($this->auth_company_id, array_keys($this->data['eventanalysis']));
		$this->load->view('general/reports/eventanalysis_display', $this->data);
	}

	private function eventanalysis_actionlist_display($severity)
	{
		$this->data['report']['severity'] = $severity;
		$this->data['eventanalysis'] = $this->data['report']['messages']['eventanalysis_actionlist'][$severity];
		$db = [
			'hostname' => $this->db->hostname,
			'database' => $this->db->database,
			'username' => $this->db->username,
			'password' => $this->db->password,
		];
		$this->load->library('eventanalysislib', $db);
		// ['f505d7c9-fd82-434a-a22f-151b1bb837af','d5df382c-aff2-41cc-88e6-d282554024a0']
		$this->data['events'] = $this->eventanalysislib->getAllByID($this->auth_company_id, array_keys($this->data['eventanalysis']));
		$this->load->view('general/reports/eventanalysis_actionlist_display', $this->data);
	}

	private function checklist_display($severity)
	{
		$this->data['checklists'] = $this->data['report']['messages']['checklist'][$severity];
		if( empty($this->data['checklists']) ) { redirect(); exit; }

		$checklists = [];
		$this->data['report']['severity'] = $severity;
		$this->load->model('form_model');
		foreach($this->data['checklists'] as $checklist)
		{
			$checklists[$checklist->type_id] = NULL;
		}

		$this->data['pages'] = $this->form_model->get_pages(array_keys($checklists));
		$this->load->model('user_messages_model');
			foreach($this->data['checklists'] as $page_id_survey_date => $checklist_inner) {
				if (!isset($this->data['pages'][$checklist_inner->type_id])) {
					$this->user_messages_model->remove( 'checklist', $this->auth_user_id, $checklist_inner->type_id );
				}
			}

		$this->load->view('general/reports/checklist_display', $this->data);
	}
}
