<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Folder extends Menu_Controller
{
	private function _check_rights($menu_id)
	{
		$menu_groups = $this->group_model->get_relationships( 'menu_group', 'menu_id', [$menu_id] );
		if( empty($menu_groups) ) { show_404(); }
		$menu_groups = array_pop($menu_groups);
		if( empty($menu_groups) ) { show_404(); }
		if(
				// $this->config->item('document_author') true or false
				!is_role('Systemadministratör') and
				(
					! acl_group_permits('menu.folder',$menu_groups) && 
					! acl_object_permits('menu.folder',[$menu_id]) 
				)
			)
		{ show_404(); }
	}
	
	private function _get_order_by_data()
	{
		$this->data['folder_order_by'] = array(
			0  => lang('folder_order_by_asc'),
			1  => lang('folder_order_by_desc'),
		);
	}
	
    public function index( $menu_id = NULL )
	{
		$this->VALID_UUIDv4($menu_id);
		$this->_get_folders_data($menu_id);
		$this->_check_rights($menu_id);
		if( ! empty($this->data['folders']) )
		{
			$this->load->model('document_model');
			$this->data['documents'] = $this->document_model->get_all_documents_in_folder( array_keys($this->data['folders']) );
		}
		$this->load->view('general/folder/view',$this->data);
	}
	
	public function create( $menu_id )
	{
		$this->VALID_UUIDv4($menu_id);
		$this->_get_folders_data($menu_id,FALSE);
		$this->_check_rights($menu_id);
		$this->_get_order_by_data();
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		
		$validation_rules = $this->_get_rules();
		
		$this->data['rights'] = FALSE;
		$position             = FALSE;
		$department           = FALSE;
		if( $this->auth_kiv && is_role('Systemadministratör') && $this->config->item('groups_folder_and_documents') === TRUE )
		{
			$this->data['rights'] = TRUE;
		}
		if( $this->data['rights'] )
		{
			$this->data['groups'] = $this->group_model->get_all_by_relationship( 'menu_group', 'menu_id', $menu_id);
			if( !empty($this->data['groups']['position']) )
			{
				$position         = TRUE;
				$validation_rules = array_merge($validation_rules, $this->_get_rules_position());
			}
			if( !empty($this->data['groups']['department']) )
			{
				$department       = TRUE;
				$validation_rules = array_merge($validation_rules, $this->_get_rules_department());
			}
			
			if( $this->config->item('document_author') )
			{
				$this->load->model('acl_model');
				$acl_menu = $this->acl_model->get_like( 'menu' );
				if( empty($acl_menu) ) { show_404(); }

				unset($acl_menu['create']);
				unset($acl_menu['read']);
				unset($acl_menu['update']);
				unset($acl_menu['delete']);
				unset($acl_menu['folder']);
				unset($acl_menu['all']);

				// @TODO: What user have access? Just show them instead.
				$users_available = [];
				foreach($this->users as $user_id => $user)
				{
					$users_available[$user_id] = $this->users[$user_id]->name;
				}
				$this->data['users'] = $users_available;
			}
		}
		
		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			$folder_id = UUIDv4();
			
			if ( $this->folder_model->create( $folder_id, $menu_id ) === TRUE )
			{
				if( $this->config->item('document_author') )
				{
					// Empty data set
					$data = NULL;
				
					// Grab ACL field
					$users = $this->input->post('acl[]');
					
					if( ! empty($users) )
					{
						// Validate users. Show 404 in case a user have changed the values.
						if( !empty(array_diff($users,array_keys($this->users))) ) { show_404(); }
						
						// Make an ACL array so that we can just save it
						$data = $this->acl_model->_get_data_object($acl_menu, $folder_id, $users);				
					}

					// Save ACL
					if ( $this->acl_model->save_object( $acl_menu, $folder_id, $data ) !== FALSE )
					{
						
					}
				}
				
				$folder_groups = array();
				if( $position )
				{
					$folder_groups = $this->folder_model->_get_folder_position();
				}
				if( $department )
				{
					$folder_groups = array_merge( $folder_groups, $this->folder_model->_get_folder_department() );
				}
				if( !empty($folder_groups) )
				{
					if( $this->group_model->save( 'folder_group', 'folder_id', $folder_id, $folder_groups ) === FALSE )
					{
						// @STEP2: If there are a error
					}
				}
				redirect('folder/' . $menu_id); exit;
			}
		}
		else
		{
			$this->load->view('general/folder/create',$this->data);
		}
	}
	
	public function update( $folder_id )
	{
		$this->VALID_UUIDv4($folder_id);
		$this->_get_folder_data($folder_id);
		$this->_check_rights($this->data['menu']['current']->menu_id);
		$this->_get_order_by_data();
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$this->load->model('group_model');
		
		$validation_rules = $this->_get_rules();
		
		$this->data['rights'] = FALSE;
		$position             = FALSE;
		$department           = FALSE;
		if( $this->auth_kiv && is_role('Systemadministratör') && $this->config->item('groups_folder_and_documents') === TRUE )
		{
			$this->data['rights'] = TRUE;
		}
		if( $this->data['rights'] )
		{
			$this->data['groups']         = $this->group_model->get_all_by_relationship( 'menu_group', 'menu_id', $this->data['menu']['current']->menu_id);
			$this->data['groups_checked'] = $this->group_model->get_all_by_relationship( 'folder_group', 'folder_id', $folder_id, FALSE);
			if( !empty($this->data['groups']['position']) )
			{
				$position         = TRUE;
				$validation_rules = array_merge($validation_rules, $this->_get_rules_position());
			}
			if( !empty($this->data['groups']['department']) )
			{
				$department       = TRUE;
				$validation_rules = array_merge($validation_rules, $this->_get_rules_department());
			}
			
			if( $this->config->item('document_author') )
			{
				$this->load->model('acl_model');
				$acl_menu = $this->acl_model->get_like( 'menu' );
				if( empty($acl_menu) ) { show_404(); }

				unset($acl_menu['create']);
				unset($acl_menu['read']);
				unset($acl_menu['update']);
				unset($acl_menu['delete']);
				unset($acl_menu['folder']);
				unset($acl_menu['all']);

				$this->data['acl_users'] = [];
				$acl_menu_checked = $this->acl_model->get_object_checked_like($acl_menu, NULL, $folder_id);
				if( ! empty($acl_menu_checked) )
				{
					$this->data['acl_users'] = array_keys($acl_menu_checked);
				}
				
				// @TODO: What user have access? Just show them instead.
				$users_available = [];
				foreach($this->users as $user_id => $user)
				{
					$users_available[$user_id] = $this->users[$user_id]->name;
				}
				$this->data['users'] = $users_available;
			}
		}
		
		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			if ( $this->folder_model->update( $this->data['folder'] ) === TRUE )
			{
				if( $this->config->item('document_author') )
				{
					// Empty data set
					$data = NULL;
				
					// Grab ACL field
					$users = $this->input->post('acl[]');
					
					if( ! empty($users) )
					{
						// Validate users. Show 404 in case a user have changed the values.
						if( !empty(array_diff($users,array_keys($this->users))) ) { show_404(); }
						
						// Make an ACL array so that we can just save it
						$data = $this->acl_model->_get_data_object($acl_menu, $folder_id, $users);				
					}

					// Save ACL
					if ( $this->acl_model->save_object( $acl_menu, $folder_id, $data ) !== FALSE )
					{
						
					}
				}
				
				$folder_groups = array();
				if( $position )
				{
					$folder_groups = $this->folder_model->_get_folder_position();
				}
				if( $department )
				{
					$folder_groups = array_merge( $folder_groups, $this->folder_model->_get_folder_department() );
				}
				if( !empty($folder_groups) )
				{
					if( $this->group_model->save( 'folder_group', 'folder_id', $folder_id, $folder_groups ) === FALSE )
					{
						// @STEP2: If there are a error
					}
				}
				redirect('folder/' . $this->data['menu']['current']->menu_id); exit;
			}
		}
		else
		{
			$this->load->view('general/folder/update',$this->data);
		}
	}
	
	public function delete( $id )
	{
		$this->VALID_UUIDv4($id);
		$this->_get_folder_data($id);
		$this->_check_rights($this->data['menu']['current']->menu_id);
		
		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');
		$validation_rules = $this->_get_rules_delete();
		
		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			if ( $this->folder_model->delete( $id ) === TRUE )
			{
				
			}
			redirect('folder/' . $this->data['folder']->menu_id); exit;
		}
		else
		{
			$this->load->model('document_model');
			$this->data['documents'] = $this->document_model->get_all_documents_in_folder($id, 0, ['archived', 'unpublished']);
			$this->load->view('general/folder/delete',$this->data);
		}
	}
	
	private function _get_rules()
	{
		return array(
			array(
				'field' => 'folder_name',
				'label' => lang('folder_name'),
				'rules' => array(
					'trim',
					'required',
					'min_length[2]',
					'max_length[128]', // 255
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']',
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'folder_order_by',
				'label' => lang('folder_order_by'),
				'rules' => array(
					'trim',
					'in_list['.implode(',', array_keys($this->data['folder_order_by'])).']'
				)
			),
		);
	}
	
	private function _get_rules_position()
	{
		return array(
			array(
				'field' => 'folder_position[]',
				'label' => lang('groups_type_position'),
				'rules' => array(
					'trim',
					'in_list['.implode(',', array_keys($this->data['groups']['position'])).']'
				)
			),
		);
	}
	
	private function _get_rules_department()
	{
		return array(
			array(
				'field' => 'folder_department[]',
				'label' => lang('groups_type_department'),
				'rules' => array(
					'trim',
					'in_list['.implode(',', array_keys($this->data['groups']['department'])).']'
				)
			),
		);
	}
	
	private function _get_rules_delete()
	{
		return array(
			array(
				'field' => 'confirm_delete',
				'label' => lang('confirm_delete'),
				'rules' => array(
					'required'
				)
			),
		);
	}
}