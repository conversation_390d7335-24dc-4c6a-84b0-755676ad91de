<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Dashboard extends Menu_Controller
{
	private $settingsPath = CI_UPLOAD_PATH . 'settings.json';

	public function __construct()
	{
		parent::__construct();
		$this->data['body_class'] .= 'skin-white fixed';
	}

	public function documents_overview()
	{
		$this->load->model(['document_model']);
		$documents_overview = $this->document_model->document_report_by_owner();
		$this->data['documents_overview'] = $documents_overview;
		$this->load->view('general/noticeboard/overview', $this->data);
	}

	public function index( $category_id = NULL )
	{
		if( isset($this->menus['structure'][0]) )
		{
			$default = array_slice($this->menus['structure'][0],0,1);
			$default = array_shift($default);
			$this->_get_menu($default->menu_id);
		}

		if( $this->auth_kiv )
		{
			if( isset($this->mainmenu[3]) && count($this->mainmenu[3]) === 1 )
			{
				$page = array_pop($this->mainmenu[3]);
				$this->data['page'] = $this->posts_model->get_page($page->page_id);
				$this->data['categories'] = $this->posts_model->get_page_categories($page->page_id, TRUE);
				$this->data['read'] = $this->posts_model->get_read_post();

				$settings = [];
				if(file_exists($this->settingsPath))
				{
					if(($json = file_get_contents($this->settingsPath)) !== FALSE)
					{
						if(($array = json_decode($json, true)) !== NULL)
						{
							$settings = $array;
						}
					}
				}

				if (!array_key_exists('widget_vertical', $settings)) {
					$settings['widget_vertical'] = 1;
				}
				if (!array_key_exists('buttons_vertical', $settings)) {
					$settings['buttons_vertical'] = 0;
				}

				if (isset($settings['tasktypes'])) {
					$this->load->model(['task_model']);
					$tasks_list = $this->task_model->get_all_by_owner( $this->auth_user_id );
					$tasks = array_key_exists('published', $tasks_list) ? $tasks_list['published'] : array();
					$tasktype_list = $this->task_model->get_task_type_data();
					$charts = array();
					foreach ($settings['tasktypes'] as $tasktype) {
					$key = $tasktype_list[$tasktype];
					$value = [0, 0];
					foreach ($tasks as $taskkey => $task) {
						if ($task->task_type != $tasktype) continue;
						if ($task->target == 0) continue;
						$value[0] += 100*$task->progress/$task->target;
						$value[1] += 100;
					}
					if ($value[1] == 0) continue;
					$percent = 100*$value[0]/$value[1];
					$chart = array(
						'tooltip' => array('show' => true),
						'series' => [array(
						'name' => $key,
						'type' => 'gauge',
						'color' => ($percent > 75) ? '#3ba272' : ( ($percent > 30) ? '#fc8452' : '#ee6666'),
						'detail' => array('valueAnimation' => true),
						'progress' => array("show" => true),
						'data' => [ array("value" => round($percent), 'name' =>  $key) ]
						)]
					);
					$charts[] = $chart;
					}
					$this->data['taskcharts'] = $charts;
				} else {
					$this->data['taskcharts'] = [];
				}

				$this->data['settings'] = $settings;

				$this->data['categories'] = $this->_get_accessible_categories($this->data['categories']);
				$this->_new_documents();
				$this->_get_education();
				$this->_get_event_analysis();
				$this->_get_checklists();
				$this->_get_document_type_data();
				$this->_get_deviations();
				$this->data['documents_category'] = $this->document_model->get_document_category();
				if( empty($this->data['categories']) )
				{
					$this->load->view('general/noticeboard/' . $this->data['page']->layout, $this->data);
				}
				else if( $this->data['page']->layout === 'folder' )
				{
					if( empty($category_id) ) { $category_id = array_keys($this->data['categories'])[0]; }
					$this->data['category_id'] = $category_id;
					$this->data['category'] = $this->data['categories'][$category_id];
					// @STEP2: Get all posts directly from $category_id and check publish date && unpublish date
					$this->data['posts']['categories'] = $this->posts_model->get_posts_categories($category_id);
					if( ! empty($this->data['posts']['categories']) )
						$this->data['posts']['posts'] = $this->posts_model->get_all_posts( array_keys($this->data['posts']['categories']) );

					$this->load->view('general/noticeboard/' . $this->data['page']->layout, $this->data);
				}
				else if( $this->data['page']->layout === 'tags' )
				{

					$categories = in_array($category_id, array_keys($this->data['categories'])) ? $category_id : array_keys($this->data['categories']);

					// @STEP2: Get all posts directly from $category_id and check publish date && unpublish date
					$this->data['posts']['categories'] = $this->posts_model->get_posts_categories($categories);
					if( ! empty($this->data['posts']['categories']) )
						$this->data['posts']['posts'] = $this->posts_model->get_all_posts( array_keys($this->data['posts']['categories']) );

					$this->load->view('general/noticeboard/' . $this->data['page']->layout, $this->data);
				}
			}
			else
			{
				$this->load->view('general/noticeboard/invalid_amout',$this->data);
			}
		}
		else
		{
			$this->data['sidebar']['active'] = FALSE;
			$this->load->view('dashboard',$this->data);
		}
	}
}
