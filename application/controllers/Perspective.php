<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Perspective extends Menu_Controller
{
	private $settingsPath = CI_UPLOAD_PATH . 'template.json';
	
	public function index( $id, $folder_id = NULL )
	{
		$this->VALID_UUIDv4($id);
		$this->VALID_UUIDv4($folder_id,FALSE);
		$this->_get_menu($id);
		$this->load->model('folder_model');
		$this->load->model('document_model');
		$this->_get_document_type_data();
		$this->data['documents_category'] = $this->document_model->get_document_category();
		
		if( $this->data['menu']['current'] === NULL ) { redirect(); exit; }
		
		$this->data['menu_groups'] = $this->group_model->get_relationships( 'menu_group', 'menu_id', [$this->data['menu']['current']->menu_id] );
		if( empty($this->data['menu_groups']) ) { show_404(); }
		$this->data['menu_groups'] = array_pop($this->data['menu_groups']);
		if( empty($this->data['menu_groups']) ) { show_404(); }

		$settings = [];
		$settings['other'] = [];

		if(file_exists($this->settingsPath))
		{
			if(($json = file_get_contents($this->settingsPath)) !== FALSE)
			{
				if(($array = json_decode($json, true)) !== NULL)
				{
					$settings = $array;
					if (!array_key_exists('other', $settings)) $settings['other'] = [];
				}
			}
		}

		$this->data['templates'] = $settings['other'];
		
		if( $this->data['menu']['current']->type == 2)
		{
			$this->load->view('general/perspective/view_href',$this->data);
		}
		else
		{
			// Fetch all folders
			$this->data['folders'] = $this->folder_model->get_all( $this->data['menu']['current']->menu_id );
			if( !empty($this->data['folders']) )
			{
				$this->data['folders_groups'] = $this->group_model->get_relationships( 'folder_group', 'folder_id', array_keys($this->data['folders']) );
				if( ! is_role('Systemadministratör') && $this->data['menu']['current']->owner !== $this->auth_user_id )
				{
					if(
						(
							$this->config->item('document_author') === TRUE
							&& ! empty($this->data['folders_groups'])
						)
						OR 
						( 
							$this->config->item('document_author') === FALSE
							&& ! empty($this->data['folders_groups'])
							&& ! acl_object_permits('menu.read', [$this->data['menu']['current']->menu_id])
						)
					)
					{
						foreach($this->data['folders_groups'] as $unset_folder_id => $folders_groups)
						{
							if( !acl_group_permits('menu.read', $folders_groups) )
							{
								unset($this->data['folders'][$unset_folder_id]);
							}
						}
					}
					// Fix for KIV 7 to KIV 8
					if( $this->config->item('groups_folder_and_documents') === TRUE && ! empty($this->groups['types']['uuid']['department']) )
					{
						foreach($this->groups['types']['uuid']['department'] as $department)
						{
							foreach($this->data['folders_groups'] as $unset_folder_id => $folders_groups)
							{
								foreach($folders_groups as $folder_group_tmp_id => $folder_group)
								{
									if( $folder_group === $department )
									{
										unset($this->data['folders_groups'][$unset_folder_id][$folder_group_tmp_id]);
									}
								}
								
								if( empty($this->data['folders_groups'][$unset_folder_id]) )
								{
									unset($this->data['folders_groups'][$unset_folder_id]);
								}
							}
						}
					}
				}
				
				if( empty($folder_id) && ! empty($this->data['folders']) ) { $folder_id = array_keys($this->data['folders'])[0]; }
				$this->data['folder_group'] = isset($this->data['folders_groups'][$folder_id]) ? $this->data['folders_groups'][$folder_id] : [];
			}

			// Fetch documents from folder
			if( isset($this->data['folders'][$folder_id]) )
			{
				$this->data['folder_id'] = $folder_id;
				$this->data['folder']    = $this->data['folders'][$folder_id];
				$this->data['documents'] = $this->document_model->get_all_documents_in_folder( $folder_id, $this->data['folders'][$folder_id]->order_by,  array('clone', 'published'), TRUE);
				
				if( !empty($this->data['documents']) && ! is_role('Systemadministratör') && $this->data['menu']['current']->owner !== $this->auth_user_id )
				{
					$this->data['documents_groups'] = $this->group_model->get_relationships( 'document_group', 'document_id', array_keys($this->data['documents']) );
					if(
						(
							$this->config->item('document_author') === TRUE
							&& ! empty($this->data['documents_groups'])
						)
						OR 
						( 
							$this->config->item('document_author') === FALSE
							&& ! empty($this->data['documents_groups'])
							&& ! acl_object_permits('menu.read', [$this->data['menu']['current']->menu_id])
						)
					)
					{
						foreach($this->data['documents_groups'] as $unset_document_id => $documents_groups)
						{
							if( !acl_group_permits('menu.read', $documents_groups) )
							{
								unset($this->data['documents'][$unset_document_id]);
							}
						}
					}
				}
			}
			
			$this->load->view('general/perspective/view',$this->data);
		}
    }
}
