<?php
defined('BASEPATH') OR exit('No direct script access allowed');
require_once( dirname(__FILE__) . '/../libraries/onlyoffice/common.php' );
require_once( dirname(__FILE__) . "/../../vendor/autoload.php");

class Page extends Menu_Controller
{
	private $settingsPath = CI_UPLOAD_PATH . 'settings.json';

	public function __construct()
	{
		parent::__construct();
		$this->data['body_class'] .= 'skin-white fixed';
		$this->load->model('posts_model');
	}

	public function index($id = NULL, $category_id = NULL )
	{
		$this->VALID_UUIDv4($id);
		$staticPage = FALSE;

		$this->data['page'] = $this->posts_model->get_page($id);
		if( empty($this->data['page']) ) { redirect(); exit; }
		if( $this->data['page']->status === 'inactive' ) { redirect(); exit; }
		if( $this->data['page']->status === 'private' && $this->data['page']->company_id !== $this->auth_company_id )
		{
			redirect(); exit;
		}

		$settings = [];
				if(file_exists($this->settingsPath))
				{
					if(($json = file_get_contents($this->settingsPath)) !== FALSE)
					{
						if(($array = json_decode($json, true)) !== NULL)
						{
							$settings = $array;
						}
					}
				}

				if (!array_key_exists('widget_vertical', $settings)) {
					$settings['widget_vertical'] = 1;
				}
				if (!array_key_exists('buttons_vertical', $settings)) {
					$settings['buttons_vertical'] = 0;
				}

				if (isset($settings['tasktypes'])) {
					$this->load->model(['task_model']);
					$tasks_list = $this->task_model->get_all_by_owner( $this->auth_user_id );
					$tasks = array_key_exists('published', $tasks_list) ? $tasks_list['published'] : array();
					$tasktype_list = $this->task_model->get_task_type_data();
					$charts = array();
					foreach ($settings['tasktypes'] as $tasktype) {
					$key = $tasktype_list[$tasktype];
					$value = [0, 0];
					foreach ($tasks as $taskkey => $task) {
						if ($task->task_type != $tasktype) continue;
						if ($task->target == 0) continue;
						$value[0] += 100*$task->progress/$task->target;
						$value[1] += 100;
					}
					if ($value[1] == 0) continue;
					$percent = 100*$value[0]/$value[1];
					$chart = array(
						'tooltip' => array('show' => true),
						'series' => [array(
						'name' => $key,
						'type' => 'gauge',
						'color' => ($percent > 75) ? '#3ba272' : ( ($percent > 30) ? '#fc8452' : '#ee6666'),
						'detail' => array('valueAnimation' => true),
						'progress' => array("show" => true),
						'data' => [ array("value" => round($percent), 'name' =>  $key) ]
						)]
					);
					$charts[] = $chart;
					}
					$this->data['taskcharts'] = $charts;
				} else {
					$this->data['taskcharts'] = [];
				}

				$this->data['settings'] = $settings;

		$this->_new_documents();
		$this->_get_education();
		$this->_get_event_analysis();
		$this->_get_checklists();
		$this->_get_document_type_data();
		$this->_get_deviations();
		$this->data['documents_category'] = $this->document_model->get_document_category();

		if( isset($this->menus['structure'][0]) )
		{
			$default = array_slice($this->menus['structure'][0],0,1);
			$default = array_shift($default);
			$this->_get_menu($default->menu_id);
		}

		if( ! $this->auth_kiv ) { $this->data['sidebar']['active'] = FALSE; }

		$this->data['categories'] = $this->posts_model->get_page_categories($id, TRUE);
		$this->data['categories'] = $this->_get_accessible_categories($this->data['categories']);
		if( empty($this->data['categories']) ) { $staticPage = TRUE; }

		$this->data['read'] = $this->posts_model->get_read_post();

		if( $this->data['page']->layout === 'folder' && $staticPage === FALSE )
		{
			if( empty($category_id) ) { $category_id = array_keys($this->data['categories'])[0]; }
			$this->data['category_id'] = $category_id;
			$this->data['category'] = $this->data['categories'][$category_id];

			// @STEP2: Get all posts directly from $category_id
			$this->data['posts']['categories'] = $this->posts_model->get_posts_categories($category_id, FALSE);
			if( ! empty($this->data['posts']['categories']) )
				$this->data['posts']['posts'] = $this->posts_model->get_all_posts( array_keys($this->data['posts']['categories']) );

			if( $this->data['page']->position != 3 )
				$this->load->view('general/page/' . $this->data['page']->layout, $this->data);
			else
				$this->load->view('general/noticeboard/' . $this->data['page']->layout, $this->data);
		}
		else if( $this->data['page']->layout === 'tags' && $staticPage === FALSE )
		{
			$categories = in_array($category_id, array_keys($this->data['categories'])) ? $category_id : array_keys($this->data['categories']);

			// @STEP2: Get all posts directly from $category_id
			$this->data['posts']['categories'] = $this->posts_model->get_posts_categories( $categories, FALSE );
			if( ! empty($this->data['posts']['categories']) )
				$this->data['posts']['posts'] = $this->posts_model->get_all_posts( array_keys($this->data['posts']['categories']) );

			if( $this->data['page']->position != 3 )
				$this->load->view('general/page/' . $this->data['page']->layout, $this->data);
			else
				$this->load->view('general/noticeboard/' . $this->data['page']->layout, $this->data);
		}
		else
		{
			// @TODO: FLEX/KIV
			$this->data['attachments'] = $this->posts_model->get_attachments($this->data['page']->page_id, 'page');
			$this->load->view('dashboard',$this->data);
		}
	}
	// Read
	public function post($page_id = NULL, $post_id = NULL)
	{
		$this->VALID_UUIDv4($page_id);
		$this->VALID_UUIDv4($post_id);

		$this->data['page']['page'] = $this->posts_model->get_page($page_id);
		if( empty($this->data['page']['page']) ) { redirect(); exit; }
		if( $this->data['page']['page']->status === 'inactive' ) { redirect(); exit; }
		if( $this->data['page']['page']->status === 'private' && $this->data['page']['page']->company_id !== $this->auth_company_id )
		{
			redirect(); exit;
		}

		$categories_matches = $this->posts_model->get_post_and_page_with_same_category($page_id, $post_id);
		if( empty($categories_matches) ) { redirect(); exit; }

		$this->data['post']['post'] = $this->posts_model->get_post($post_id);
		if( empty($this->data['post']['post']) ) { redirect(); exit; }

		/****************************************************************/

		$this->posts_model->read_post($post_id);

		$this->data['main_attachment'] = null;
		if (CI_ONLY_OFFICE)
		{
			$upload_base_path = CI_UPLOAD_PATH . 'posts';
			$upload_path      = $upload_base_path . DIRECTORY_SEPARATOR . $this->auth_company_id;
			if (file_exists($upload_path . DIRECTORY_SEPARATOR . $post_id . '.docx')){
				$this->data['main_attachment'] = $post_id . '.docx';
			}
		}

		if( isset($this->menus['structure'][0]) )
		{
			$default = array_slice($this->menus['structure'][0],0,1);
			$default = array_shift($default);
			$this->_get_menu($default->menu_id);
		}

		if( ! $this->auth_kiv ) { $this->data['sidebar']['active'] = FALSE; }

		$this->data['attachments'] = $this->posts_model->get_attachments($this->data['post']['post']->post_id);

		if( $this->data['page']['page']->position != 3 )
		{
			$this->load->view('general/page/post', $this->data);
		}
		// Comments
		else
		{
			// $this->posts_model->get_comments();

			$this->load->helper('form');
			$this->load->library('form_validation');
			$this->config->load('form_validation');

			$config = array(
				array(
					'field' => 'posts_comment',
					'label' => lang('posts_comment'),
					'rules' => array(
						'trim',
						'required',
					),
				),
			);

			$this->form_validation->set_rules( $config );
			if( $this->form_validation->run() === TRUE )
			{
				$object               = new stdClass();
				$object->comment_id   = UUIDv4();
				$object->post_id      = $post_id;
				$object->created_by   = $this->auth_user_id;
				$object->created_date = date('Y-m-d H:i:s');

				if( $this->posts_model->comment( $object, TRUE ) === TRUE )
				{
					redirect('post/' . $page_id . '/' . $post_id); exit;
				}

				// @STEP2: Give error
				redirect('post/' . $page_id . '/' . $post_id); exit;
			}
			else
			{
				$this->data['comments'] = $this->posts_model->get_comments($this->data['post']['post']->post_id);
				$this->load->view('general/page/post', $this->data);
			}
		}
	}

	public function readlog($page_id = NULL, $post_id = NULL)
	{
		$this->VALID_UUIDv4($page_id);
		$this->VALID_UUIDv4($post_id);

		$this->data['page']['page'] = $this->posts_model->get_page($page_id);
		if( empty($this->data['page']['page']) ) { redirect(); exit; }
		if( $this->data['page']['page']->status === 'inactive' ) { redirect(); exit; }
		if( $this->data['page']['page']->status === 'private' && $this->data['page']['page']->company_id !== $this->auth_company_id )
		{
			redirect(); exit;
		}

		$categories_matches = $this->posts_model->get_post_and_page_with_same_category($page_id, $post_id);
		if( empty($categories_matches) ) { redirect(); exit; }

		$this->data['post']['post'] = $this->posts_model->get_post($post_id);
		if( empty($this->data['post']['post']) ) { redirect(); exit; }

		/****************************************************************/

		// $this->posts_model->read_post($post_id);

		if( isset($this->menus['structure'][0]) )
		{
			$default = array_slice($this->menus['structure'][0],0,1);
			$default = array_shift($default);
			$this->_get_menu($default->menu_id);
		}

		if( ! $this->auth_kiv ) { $this->data['sidebar']['active'] = FALSE; }

		// $this->data['attachments'] = $this->posts_model->get_attachments($this->data['post']['post']->post_id);

		$this->data['readlog'] = $this->posts_model->get_readlog_summary($post_id);
		$this->load->view('general/page/readlog',$this->data);
	}

	public function create( $page_id = NULL )
	{
		$this->VALID_UUIDv4($page_id);
		$this->data['page']['page'] = $this->posts_model->get_page($page_id);
		if( empty($this->data['page']['page']) ) { redirect('/'); exit; }
		if( $this->data['page']['page']->company_id !== $this->auth_company_id ) {  redirect(); exit; }

		$this->data['type']     = 'posts';
		$this->data['callback'] = $callback = 'create_' . $this->data['type'];
		$this->data['rules']    = '_get_rules_' . $this->data['type'];

		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');

		// @TODO: Check if you got ACL to categories
		$this->data['categories'] = $this->posts_model->get_page_categories($page_id, TRUE);
		// var_dump($this->data['categories']);exit;
		if( empty($this->data['categories']) ) { redirect(); exit; }

		$this->data['categories_groups'] = $this->group_model->get_relationships( 'category_group', 'category_id', array_keys($this->data['categories']) );
		if( !empty($this->data['categories_groups']) )
		{
			foreach($this->data['categories_groups'] as $unset_category_id => $categories_groups)
			{
				// @TODO: Change into create instead
				if( !acl_group_permits('menu.read', $categories_groups) )
				{
					unset($this->data['categories'][$unset_category_id]);
				}
			}

			if( empty($this->data['categories']) ) { redirect(); exit; }
		}

		$validation_rules = $this->{$this->data['rules']}();

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			$object               = new stdClass();
			$object->post_id      = $this->input->post('uuid_kvalprak');
			$object->created_by   = $this->auth_user_id;
			$object->created_date = date('Y-m-d H:i:s');

			if( $this->posts_model->create( NULL, NULL, $callback, $object ) === TRUE )
			{
				redirect(); exit;
			}

			// @STEP2: Give error
			redirect(); exit;
		}
		else
		{
			$this->data['uuid_kvalprak'] = $id = UUIDv4();
			// @ONLYOFFICE
			if(CI_ONLY_OFFICE)
			{
				$createExt = 'docx';
				$template_path = realpath(APPPATH . '..' . DIRECTORY_SEPARATOR . 'assets/new') . DIRECTORY_SEPARATOR . 'new.' . $createExt;
				if(file_exists(CI_UPLOAD_PATH . 'template.json'))
				{
					if(($json = file_get_contents(CI_UPLOAD_PATH . 'template.json')) !== FALSE)
					{
						if(($array = json_decode($json, true)) !== NULL)
						{
							$settings = $array;
							if (array_key_exists( $createExt, $settings ))
							{
								$template_path = CI_UPLOAD_PATH . $settings[$createExt];
							}
						}
					}
				}
				$upload_base_path = CI_UPLOAD_PATH . 'posts';
				$upload_path      = $upload_base_path . DIRECTORY_SEPARATOR . $this->auth_company_id;
				if( !is_dir($upload_path) )
					mkdir($upload_path, 0777);
				$new_file_path = $upload_path . DIRECTORY_SEPARATOR . $id . '.' . $createExt;

				if(!file_exists($new_file_path))
				{
					if(!@copy($template_path, $new_file_path))
					{
						log_message('error', "Copy file error (posts) from " . $template_path . " to ". $new_file_path);
							//Copy error!!!
					}

					createMeta($new_file_path, $this->auth_user_id, $this->auth_name);
				}
			}
			$this->load->view('general/posts/' . $this->data['callback'], $this->data);
		}
	}

	public function update_edit_date($document_id)
	{
		$this->posts_model->update_edit_date($document_id);
	}

	// @TODO: Admin or created_by
	public function update( $page_id = NULL, $post_id = NULL )
	{
		$this->VALID_UUIDv4($page_id);
		$this->VALID_UUIDv4($post_id);

		$this->data['type']     = 'posts';
		$this->data['callback'] = $callback = 'update_' . $this->data['type'];
		$this->data['rules']    = '_get_rules_' . $this->data['type'];
		$this->data['parent_id'] = NULL;

		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');

		$this->data['post'] = $object = $this->posts_model->get_post( $post_id );
		// var_dump($this->data['post']);exit;
		if( empty($this->data['post']) ) { show_404(); }

		// @TODO: Check if you got ACL to categories
		$this->data['categories'] = $this->posts_model->get_page_categories($page_id, TRUE);
		if( empty($this->data['categories']) ) { redirect(); exit; }

		$this->data['categories_checked'] = $this->posts_model->get_post_categories($post_id);
		if( empty($this->data['categories_checked']) ) { show_404(); }
		// @TODO: Check if your ACL have removed a category that already have been selected
		$this->data['categories_groups'] = $this->group_model->get_relationships( 'category_group', 'category_id', array_keys($this->data['categories']) );
		if( !empty($this->data['categories_groups']) )
		{
			foreach($this->data['categories_groups'] as $unset_category_id => $categories_groups)
			{
				if( !acl_group_permits('menu.read', $categories_groups) )
				{
					unset($this->data['categories'][$unset_category_id]);
				}
			}

			if( empty($this->data['categories']) ) { redirect(); exit; }
		}

		$validation_rules = $this->{$this->data['rules']}();

		$this->form_validation->set_rules( $validation_rules );
		if( $this->form_validation->run() === TRUE )
		{
			$object->parent_id = $post_id;

			if( $this->posts_model->update( $post_id, NULL, $callback, $object ) === TRUE )
			{
				// if( $this->data['parent_id'] )
					// redirect();
				// else
					redirect(); exit;
				// exit;
			}
		}
		else
		{
			$this->data['main_attachment'] = null;
			if(CI_ONLY_OFFICE)
			{
				// @ONLYOFFICE
				$upload_base_path = CI_UPLOAD_PATH . 'posts';
				$upload_path      = $upload_base_path . DIRECTORY_SEPARATOR . $this->auth_company_id;
				
				if (file_exists($upload_path . DIRECTORY_SEPARATOR . $post_id . '.docx'))
					$this->data['main_attachment'] = $post_id . '.docx';
			}
			$this->load->view('general/posts/' . $this->data['callback'], $this->data);
		}
	}
	// @TODO: Admin or created_by
	public function comment( $type = 'update', $comment_id = NULL )
	{
		in_array($type,['update','delete'],TRUE) OR show_404();
		$this->VALID_UUIDv4($comment_id);

		// $this->data['page'] = $this->posts_model->get_page($page_id);
		// $this->data['post'] = $this->posts_model->get_post($post_id);
		$this->data['comment'] = $this->posts_model->get_comment($comment_id);

		$this->load->helper('form');
		$this->load->library('form_validation');
		$this->config->load('form_validation');

		$callback = 'comment_' . $type;
		$this->{$callback}();
	}

	private function comment_update( )
	{
		$config = array(
			array(
				'field' => 'posts_comment',
				'label' => lang('posts_comment'),
				'rules' => array(
					'trim',
					'required',
				),
			),
		);

		$this->form_validation->set_rules( $config );
		if( $this->form_validation->run() === TRUE )
		{
			$object               = new stdClass();
			$object->comment_id   = $this->data['comment']->comment_id;
			$object->post_id      = $this->data['comment']->post_id;
			$object->created_by   = $this->data['comment']->created_by;
			$object->created_date = $this->data['comment']->created_date;

			if( $this->posts_model->comment( $object ) === TRUE )
			{
				redirect('post/' . $this->data['comment']->page_id . '/' . $this->data['comment']->post_id); exit;
			}

			// @STEP2: Give error
			redirect('post/' . $this->data['comment']->page_id . '/' . $this->data['comment']->post_id); exit;
		}
		else
		{
			$this->load->view('general/comment/update', $this->data);
		}
	}

	private function comment_delete()
	{
		$config = array(
			array(
				'field' => 'confirm_delete',
				'label' => lang('confirm_delete'),
				'rules' => array(
					'required'
				)
			),
		);

		$this->form_validation->set_rules( $config );
		if( $this->form_validation->run() === TRUE )
		{
			$this->posts_model->comment_delete($this->data['comment']);

			redirect('post/' . $this->data['comment']->page_id . '/' . $this->data['comment']->post_id); exit;
		}
		else
		{
			$this->load->view('general/comment/delete', $this->data);
		}
	}

	public function download( $type = 'page', $id = NULL )
	{
		in_array($type,['page','post'],TRUE) OR show_404();

		$callback = 'download_' . $type;
		$this->{$callback}($id);
	}
	// @STEP2: Secure get_page
	private function download_page( $attachment_id )
	{
		$this->VALID_UUIDv4($attachment_id);
		$attachment = $this->posts_model->get_attachment( $attachment_id, 'page' );
		if( empty($attachment) ) { show_404(); }

		$page = $this->posts_model->get_page($attachment->page_id);
		if( empty($page) ) { show_404(); }
		if( $page->status === 'inactive' ) { show_404(); }
		if( $page->status === 'private' && $page->company_id !== $this->auth_company_id ) { show_404(); }

		$upload_base_path = CI_UPLOAD_PATH . 'pages';
		$upload_path = $upload_base_path . DIRECTORY_SEPARATOR . $attachment->page_id;
		$upload_file = $upload_path . DIRECTORY_SEPARATOR . $attachment->attachment_id . $attachment->file_ext;

		if( ! file_exists($upload_file) )
			show_404();

		if($attachment->file_ext === '.pdf')
		{
			$this->load->helper('pdf');
			pdf($attachment, $upload_file);
		}
		else
		{
			$this->load->helper('download');
			force_download($attachment->file_name, file_get_contents($upload_file), TRUE);
		}
	}
	// @STEP2: Secure get_post
	private function download_post( $attachment_id )
	{
		$this->VALID_UUIDv4($attachment_id);
		$attachment = $this->posts_model->get_attachment( $attachment_id );
		if( empty($attachment) ) { show_404(); }

		$post = $this->posts_model->get_post($attachment->post_id);
		if( empty($post) ) { show_404(); }
		// if( $page->status === 'inactive' ) { show_404(); }
		// if( $page->status === 'private' && $page->company_id !== $this->auth_company_id ) { show_404(); }

		$upload_base_path = CI_UPLOAD_PATH . 'posts';
		$upload_path = $upload_base_path . DIRECTORY_SEPARATOR . $attachment->post_id;
		$upload_file = $upload_path . DIRECTORY_SEPARATOR . $attachment->attachment_id . $attachment->file_ext;

		if( ! file_exists($upload_file) )
			show_404();

		if($attachment->file_ext === '.pdf')
		{
			$this->load->helper('pdf');
			pdf($attachment, $upload_file);
		}
		else
		{
			$this->load->helper('download');
			force_download($attachment->file_name, file_get_contents($upload_file), TRUE);
		}
	}

	private function _get_rules_posts()
	{
		return array(
			array(
				'field' => 'posts_name',
				'label' => lang('posts_name'),
				'rules' => array(
					'trim',
					'required',
					'min_length[3]',
					'max_length[256]', // text
					'regex_match['.$this->config->item('error_alpha_numeric_spaces_dash_colon').']',
				),
				'errors' => array(
					'regex_match' => lang('error_alpha_numeric_spaces_dash_colon')
				)
			),
			array(
				'field' => 'posts_publish',
				'label' => lang('posts_publish'),
				'rules' => array(
					'trim',
					'required',
					'regex_match['.$this->config->item('error_date').']',
				),
				'errors' => array(
					'regex_match' => lang('error_date')
				)
			),
			array(
				'field' => 'posts_unpublish',
				'label' => lang('posts_unpublish'),
				'rules' => array(
					'trim',
					'regex_match['.$this->config->item('error_date').']',
				),
				'errors' => array(
					'regex_match' => lang('error_date')
				)
			),
			array(
				'field' => 'posts_category[]',
				'label' => lang('posts_category'),
				'rules' => array(
					'trim',
					'required',
					'in_list['.implode(',', array_keys($this->data['categories'])).']'
				)
			),
		);
	}
}
