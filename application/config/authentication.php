<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/*
| -----------------------------------------------------------------
|				ADD ACL QUERY TO AUTH FUNCTIONS							
| -----------------------------------------------------------------
| This config option turns on an additional query to retreive a logged
| in user's ACL records when they login or when login status is checked. 
| If you're not going to implement your own ACL categories, actions, 
| and take the time to create an interface to manage the ACL, then 
| you would leave this set to FALSE. Furthermore, basic ACL usage doesn't
| require that this option be set to true, because usage of the 
| Auth_model->acl_permits method will query the database if it hasn't
| already been done.
|
*/

$config['add_acl_query_to_auth_functions'] = TRUE;

/*
| -----------------------------------------------------------------
|						MAX_ALLOWED_ATTEMPTS						
| -----------------------------------------------------------------
| This definition sets the maximum amount of failed login attempts
| or failed password recovery attempts before the IP or username is
| placed on hold.
| 
*/

$config['max_allowed_attempts'] = 5;

/*
| -----------------------------------------------------------------
|						DENY_ACCESS	AT					
| -----------------------------------------------------------------
| If for some reason login attempts exceed the max_login_attempts
| value, then when they reach the number held in this definition,
| their IP address is added to the deny list in the local Apache
| configuration file.
|
| SET TO ZERO TO DISABLE THIS FUNCTIONALITY
| 
*/

$config['deny_access_at'] = 10;

/*
| -----------------------------------------------------------------
|					DENIED ACCESS REASON						
| -----------------------------------------------------------------
| The reasons why an IP address may be in the deny list
| 
*/

$config['denied_access_reason'] = [
	'0' => 'Not Specified',
	'1' => 'Login Attempts',
	'2' => 'Malicious User',
	'3' => 'Hacking Attempt',
	'4' => 'Spam',
	'5' => 'Obscene Language',
	'6' => 'Threatening Language'
];

/*
| -----------------------------------------------------------------
|							SECONDS_ON_HOLD							
| -----------------------------------------------------------------
| This definition sets the amount of time an IP or username is on 
| hold if the maximum amount of failed login attempts or failed
| password recovery attempts is reached.
| 
| 600 seconds is 10 minutes
|
*/

$config['seconds_on_hold'] = 600;

/*
| -----------------------------------------------------------------
|						DISALLOW_MULTIPLE_LOGINS					
| -----------------------------------------------------------------
| This setting attempts to either allow or disallow an account to be 
| logged in by the same user on more than one device, or with more 
| than one browser on the same device.
|
*/

$config['disallow_multiple_logins'] = FALSE;

/*
| -----------------------------------------------------------------
|						ALLOW REMEMBER ME							
| -----------------------------------------------------------------
| This setting allows you to turn on and off the ability to have 
| a persistant login where users may choose to stay logged in 
| even after the browser has closed.
|
*/

$config['allow_remember_me'] = TRUE;

/*
| -----------------------------------------------------------------
|					REMEMBER ME COOKIE NAME							
| -----------------------------------------------------------------
| This setting allows you to choose the name of the remember me cookie.
| Remember that Internet Explorer doesn't like underscores.
|
*/

$config['remember_me_cookie_name'] = 'rememberMe';

/*
| -----------------------------------------------------------------
|					REMEMBER ME EXPIRATION							
| -----------------------------------------------------------------
| How long (in seconds) the remember me funcationality allows the session to last.
|
*/

// $config['remember_me_expiration'] = 93062220;
$config['remember_me_expiration'] = 5184000; // 60 days

/*
| -----------------------------------------------------------------
|					RECOVERY CODE EXPIRATION							
| -----------------------------------------------------------------
| How long (in seconds) the password recovery code is good for.
| The default is two hours.
|
*/

$config['recovery_code_expiration'] = 60 * 60 * 24;

/*
| -----------------------------------------------------------------
|				SHOW LOGIN FORM ON LOGOUT							
| -----------------------------------------------------------------
| When the user logs out, they can be presented with a login form
| on the logout page, or else just show the logout confirmation page.
| The default (TRUE) is to show the login form.
*/

$config['show_login_form_on_logout'] = TRUE;

/*
| -----------------------------------------------------------------
|				DEFAULT LOGIN REDIRECT							
| -----------------------------------------------------------------
| When the user logs in, they will usually be redirected back to 
| the page they were trying to access, but if for some reason they
| reached the login page and have no redirect, this URI STRING is where 
| they will be redirected to. The default is to be redirected to the home page.
*/

$config['default_login_redirect'] = '';

/*
| -----------------------------------------------------------------
|				ALLOWED PAGES FOR LOGIN							
| -----------------------------------------------------------------
| Logins may only happen from specified pages on the website.
| So, for instance, we don't want somebody posting directly to 
| an old LOGIN_PAGE, or some random page. LOGIN_PAGE is automatically 
| added, so you just put in optional login pages here.
*/

$config['allowed_pages_for_login'] = [''];

/*
| -----------------------------------------------------------------
|				REDIRECT TO HTTPS						
| -----------------------------------------------------------------
| If a page is supposed to be viewed using an encrypted connection, 
| you can either redirect to the HTTPS version, or serve up a 404 error.
*/

$config['redirect_to_https'] = FALSE;

#
# -----------------------------------------------------------------
#				HANDLE AUTH SESSIONS GC ON LOGOUT						
# -----------------------------------------------------------------
# Unless you create a cron job that calls the auth_sessions_gc 
# method in the auth model, you'll want to leave this setting 
# set to TRUE so that orphaned and expired records in the 
# auth_sessions table are deleted.
#
# If you do have a cron to handle garbage collection, set 
# this setting to FALSE.
#
# Example cron to run once every 10 minutes:
#     */10 * * * * php /path/to/project/index.php crons auth_sessions_gc > /dev/null 2>&1
#
# Example cront to run once every 10 minutes (using wget):
#     */10 * * * * /usr/bin/wget http://<YOUR DOMAIN>/crons/auth_sessions_gc -O /dev/null
#

$config['auth_sessions_gc_on_logout'] = TRUE;


/* End of file authentication.php */
/* Location: /community_auth/config/authentication.php */