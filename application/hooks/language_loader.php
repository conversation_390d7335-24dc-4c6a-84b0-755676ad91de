<?php
defined('BASEPATH') OR exit('No direct script access allowed');

function language_loader()
{
	$ci =& get_instance();
	$ci->load->helper('language');
	// Set default language for system messages
	$site_lang = $ci->session->site_lang ? $ci->session->site_lang : $ci->config->item('language');
	$ci->config->set_item('language', $site_lang);
	// What folder have we stored our files in
	$custom_folder = 'intranet';
	// What language files do we have?
	chdir( APPPATH . 'language' . DIRECTORY_SEPARATOR . $site_lang . DIRECTORY_SEPARATOR . $custom_folder );
	$language_files = glob( '*_lang.php', GLOB_NOSORT );
	// Remove _lang.php and append custom folder
	$language_files = array_map( function($filename, $custom_folder) {
		return $custom_folder . DIRECTORY_SEPARATOR . substr($filename,0,-9);
	}, $language_files, array_fill(0, count($language_files), $custom_folder) );
	// Load langauge files
	$ci->lang->load($language_files,$site_lang);
}