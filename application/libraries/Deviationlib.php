<?php
class Deviationlib {
	
	/*************** BEGIN DB **************/
	
	protected $olddb; //This is the database connection
	private $query; //This is the current query

	/**
	 * Construct a new database object.
	 */
	public function __construct($db) {
		if(!isset($this->olddb)) {
			//Connect to database
			try{
				$this->olddb = new PDO("mysql:host=". $db['hostname'] . ";dbname=" . $db['database'], 
								$db['username'], 
								$db['password'], 
								array(
									PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
									PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8"
								));
			}catch(PDOException $e){
				file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
				die('Tyvärr gick någonting snett, kontakta Kvalprak AB och be dem kontrollera PDO loggen.');
			}
		}
	}

	function disconnect () {
		$this->olddb = null;
		return true;
	}
	
	protected function placeholders($text, $count=0, $separator=","){
		$result = array();
		if($count > 0){
			for($x=0; $x<$count; $x++){
				$result[] = $text;
			}
		}
		return implode($separator, $result);
	}
	
	protected function prepareQuery($data,$separator=",") {
		$insert_values = array();
		$question_marks = array();
		foreach($data as $d){
			$question_marks[] = '('  . $this->placeholders('?', sizeof($d),$separator) . ')';
			$insert_values = array_merge($insert_values, array_values($d));
		}
		return array($question_marks, $insert_values);
	}
	
	protected function prepareQueryUpdate($data) {
		$return = array();
		foreach($data as $d) {
			$return[] = '`'.$d.'`=?';
		}
		return $return;
	}
	
	protected function prepareQueryUpdateData($data,$id=0) {
		$data = array_values($data);
		if($id)
			$data = array_merge($data,array($id));
		return $data;
	}
	
	/*************** END DB **************/
	
	public function getDeviationEventAnalysisMap($id) {
		$id = UUID_TO_BIN($id);
		$this->query = $this->olddb->prepare("SELECT 
												ea_id
											FROM 
												deviation_eventanalysis 
											WHERE 
												a_id = ?");
		$this->query->bindParam(1, $id);
		
		try{
			$this->query->execute();
			$res = true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
		
		$tmp  = $this->query->fetchAll(PDO::FETCH_OBJ);
		$data = array();
		foreach($tmp as $d):
			$data[] = BIN_TO_UUID($d->ea_id);
		endforeach;

		return $data;
	}
	
	public function getDeviationRiskAssesmentsMap($id) {
		$id = UUID_TO_BIN($id);
		$this->query = $this->olddb->prepare("SELECT 
												ra_id
											FROM 
												deviation_risk_assessments 
											WHERE 
												a_id = ?");
		$this->query->bindParam(1, $id);
		
		try{
			$this->query->execute();
			$res = true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}

		$tmp  = $this->query->fetchAll(PDO::FETCH_OBJ);
		$data = array();
		foreach($tmp as $d):
			$data[] = BIN_TO_UUID($d->ra_id);
		endforeach;

		return $data;
	}
	
	public function getEventAnalysisNames($company_id) {
		$company_id = UUID_TO_BIN($company_id);
		$this->query = $this->olddb->prepare("SELECT 
												id, name
											FROM 
												eventanalysis
											WHERE
												company_id = ?");
					
		$this->query->bindParam(1, $company_id);
					
		try{
			$this->query->execute();
			$res = true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
		
		$tmp  = $this->query->fetchAll(PDO::FETCH_OBJ);
		$data = array();
		foreach($tmp as $d):
			$d->id = BIN_TO_UUID($d->id);
			$data[$d->id] = substr($d->id,0,8) . '... - ' . $d->name;
		endforeach;

		return $data;
	}
	// @STEP2: Add department
	public function getRiskAssessmentsNames($company_id) {
		$company_id = UUID_TO_BIN($company_id);
		$this->query = $this->olddb->prepare("SELECT 
												ra_id, name
											FROM 
												risk_assessments
											WHERE
												company_id = ?");
		
		$this->query->bindParam(1, $company_id);
					
		try{
			$this->query->execute();
			$res = true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
		
		$tmp  = $this->query->fetchAll(PDO::FETCH_OBJ);
		$data = array();
		foreach($tmp as $d):
			$d->ra_id = BIN_TO_UUID($d->ra_id);
			$data[$d->ra_id] = substr($d->ra_id,0,8) . '... - ' . $d->name;
		endforeach;

		return $data;
	}
	
	public function insertDeviationEventAnalysisMap($data,$id){
		$this->removeDeviationEventAnalysisMap($id);
		if(!empty($data)) {
			$datafields = array('a_id','ea_id');
			list($question_marks,$insert_values) = $this->prepareQuery($data);
			
			$sql = "INSERT INTO `deviation_eventanalysis` (" . implode(",", $datafields ) . ") VALUES " . implode(',', $question_marks);
			$this->query = $this->olddb->prepare($sql);
			
			try{
				$this->query->execute($insert_values);
				return true;
			}catch(PDOException $e){
				file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
				return false;
			}
		}
		return true;
	}
	
	public function removeDeviationEventAnalysisMap($id) {
		$id = UUID_TO_BIN($id);
		$sql = "DELETE FROM `deviation_eventanalysis` WHERE a_id = ?";
		$this->query = $this->olddb->prepare($sql);
		try{
			$this->query->execute(array($id));
			return true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
	}
	
	public function insertDeviationRiskAssesmentsMap($data,$id){
		$this->removeDeviationRiskAssesmentsMap($id);
		if(!empty($data)) {
			$datafields = array('a_id','ra_id');
			list($question_marks,$insert_values) = $this->prepareQuery($data);
			
			$sql = "INSERT INTO `deviation_risk_assessments` (" . implode(",", $datafields ) . ") VALUES " . implode(',', $question_marks);
			$this->query = $this->olddb->prepare($sql);
			
			try{
				$this->query->execute($insert_values);
				return true;
			}catch(PDOException $e){
				file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
				return false;
			}
		}
		return true;
	}
	
	public function removeDeviationRiskAssesmentsMap($id) {
		$id = UUID_TO_BIN($id);
		$sql = "DELETE FROM `deviation_risk_assessments` WHERE a_id = ?";
		$this->query = $this->olddb->prepare($sql);
		try{
			$this->query->execute(array($id));
			return true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
	}
}