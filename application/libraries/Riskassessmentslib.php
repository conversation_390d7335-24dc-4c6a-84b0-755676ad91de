<?php
class Riskassessmentslib {
	
	/*************** BEGIN DB **************/
	
	protected $olddb; //This is the database connection
	private $query; //This is the current query

	/**
	 * Construct a new database object.
	 */
	public function __construct($db) {
		if(!isset($this->olddb)) {
			//Connect to database
			try{
				$this->olddb = new PDO("mysql:host=". $db['hostname'] . ";dbname=" . $db['database'], 
								$db['username'], 
								$db['password'], 
								array(
									PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
									PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8"
								));
			}catch(PDOException $e){
				file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
				die('Tyvärr gick någonting snett, kontakta Kvalprak AB och be dem kontrollera PDO loggen.');
			}
		}
	}

	function disconnect () {
		$this->olddb = null;
		return true;
	}
	
	protected function placeholders($text, $count=0, $separator=","){
		$result = array();
		if($count > 0){
			for($x=0; $x<$count; $x++){
				$result[] = $text;
			}
		}
		return implode($separator, $result);
	}
	
	protected function prepareQuery($data,$separator=",") {
		$insert_values = array();
		$question_marks = array();
		foreach($data as $d){
			$question_marks[] = '('  . $this->placeholders('?', sizeof($d),$separator) . ')';
			$insert_values = array_merge($insert_values, array_values($d));
		}
		return array($question_marks, $insert_values);
	}
	
	protected function prepareQueryUpdate($data) {
		$return = array();
		foreach($data as $d) {
			$return[] = '`'.$d.'`=?';
		}
		return $return;
	}
	
	protected function prepareQueryUpdateData($data,$id=0) {
		$data = array_values($data);
		if($id)
			$data = array_merge($data,array($id));
		return $data;
	}
	
	/*************** END DB **************/
	
	public function getAll($company_id) {
		$company_id = UUID_TO_BIN($company_id);
		$this->query = $this->olddb->prepare("SELECT 
												company_id, ra_id, name, description, us_id, date
											FROM 
												risk_assessments
											WHERE
												company_id = ?");
		
		$this->query->bindParam(1, $company_id);
					
		try{
			$this->query->execute();
			$res = true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			die('Tyvärr gick någonting snett, kontakta Kvalprak AB och be dem kontrollera PDO loggen.');
		}
		
		$tmp  = $this->query->fetchAll(PDO::FETCH_OBJ);
		$data = array();
		if(!empty($tmp)) {
			foreach($tmp as $d):
				$d->company_id = BIN_TO_UUID($d->company_id);
				$d->ra_id      = BIN_TO_UUID($d->ra_id);
				$d->us_id      = BIN_TO_UUID($d->us_id);
				$data[] = $d;
			endforeach;
		}

		return $data;
	}
	
	public function getRisk($id) {
		$id = UUID_TO_BIN($id);
		$this->query = $this->olddb->prepare("SELECT 
												company_id, ra_id, name, description, us_id, date 
											FROM 
												risk_assessments 
											WHERE 
												ra_id = ?");
		$this->query->bindParam(1, $id);
		
		try{
			$this->query->execute();
			$res = true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			die('Tyvärr gick någonting snett, kontakta Kvalprak AB och be dem kontrollera PDO loggen.');
		}
		
		$tmp = $this->query->fetch(PDO::FETCH_OBJ);
		$data = NULL;
		if(!empty($tmp))
		{
			$tmp->company_id = BIN_TO_UUID($tmp->company_id);
			$tmp->ra_id      = BIN_TO_UUID($tmp->ra_id);
			if( ! empty($tmp->us_id) )
			{
				$tmp->us_id = BIN_TO_UUID($tmp->us_id);
			}
			
			$data = $tmp;
		}
		return $data;
	}
	
	public function getRiskDepartment($id) {
		$id = UUID_TO_BIN($id);
		$this->query = $this->olddb->prepare("SELECT 
												de_id
											FROM 
												risk_assessments_department 
											WHERE 
												ra_id = ?");
		$this->query->bindParam(1, $id);
		
		try{
			$this->query->execute();
			$res = true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			die('Tyvärr gick någonting snett, kontakta Kvalprak AB och be dem kontrollera PDO loggen.');
		}
		
		$department = array();
		$data = $this->query->fetchAll(PDO::FETCH_OBJ);
		foreach($data as $key => $val) {
			$department[] = BIN_TO_UUID($val->de_id);
		}
		return $department;
	}
	
	public function getRisks($id) {
		$id = UUID_TO_BIN($id);
		$this->query = $this->olddb->prepare("SELECT 
												id, risk, occurrence, severity, explanation, acceptable, measure, responsible, done, finished 
											FROM 
												risk_assessments_risk 
											WHERE 
												ra_id = ?
											ORDER BY
												risk ASC");
		$this->query->bindParam(1, $id);
		
		try{
			$this->query->execute();
			$res = true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			die('Tyvärr gick någonting snett, kontakta Kvalprak AB och be dem kontrollera PDO loggen.');
		}
		
		$tmp  = $this->query->fetchAll(PDO::FETCH_OBJ);
		$data = array();
		if(!empty($tmp))
		{
			foreach($tmp as $d):
				$d->id = BIN_TO_UUID($d->id);
				if( ! empty($d->responsible) )
				{
					$d->responsible = BIN_TO_UUID($d->responsible);
				}
				$data[] = $d;
			endforeach;
		}
		
		return $data;
	}
	
	public function getRiskAssessmentsDeviationMap($id) {
		$id = UUID_TO_BIN($id);
		$this->query = $this->olddb->prepare("SELECT 
												a_id
											FROM 
												deviation_risk_assessments 
											WHERE 
												ra_id = ?");
		$this->query->bindParam(1, $id);
		
		try{
			$this->query->execute();
			$res = true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
		
		$deviation = array();
		$data = $this->query->fetchAll(PDO::FETCH_OBJ);
		foreach($data as $key => $val) {
			$deviation[] = BIN_TO_UUID($val->a_id);
		}
		
		return $deviation;
	}
	
	public function getRiskAssessmentsEventAnalysisMap($id) {
		$id = UUID_TO_BIN($id);
		$this->query = $this->olddb->prepare("SELECT 
												ea_id
											FROM 
												risk_assessments_eventanalysis 
											WHERE 
												ra_id = ?");
		$this->query->bindParam(1, $id);
		
		try{
			$this->query->execute();
			$res = true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
		
		$riskassessments = array();
		$data = $this->query->fetchAll(PDO::FETCH_OBJ);
		foreach($data as $key => $val) {
			$riskassessments[] = BIN_TO_UUID($val->ea_id);
		}
		
		return $riskassessments;
	}
	
	// @STEP2: Store "Rubrik" in devation instead
	public function getDeviationNames($departments_id,$deviation_id,$company_id) {
		$departments = array();
		foreach($departments_id as $dep):
			$departments[] = UUID_TO_BIN($dep);
		endforeach;
		$a_id = array();
		foreach($deviation_id as $dev):
			$a_id[] = UUID_TO_BIN($dev);
		endforeach;
		
		if( empty($departments) )
			$departments[] = NULL;
		if( empty($a_id) )
			$a_id[] = NULL;
		
		list($question_departments,$insert_departments) = $this->prepareQuery([$departments]);
		list($question_a_id,$insert_a_id) = $this->prepareQuery([$a_id]);
		
		$sql ="SELECT 
					a.a_id, a.answer
				FROM 
					deviation AS d
				LEFT JOIN
					deviation_department AS de
						ON de.a_id=d.a_id
				LEFT JOIN
					deviation_answers AS a
						ON a.a_id=d.a_id
				WHERE
					de.de_id IN {$question_departments[0]} AND
					(d.regby_three IS NOT NULL OR d.a_id IN {$question_a_id[0]}) AND
					a.df_id = ? AND
					d.company_id = ?
				GROUP BY 
					d.a_id";
					
		$a_id        = implode(',',$a_id);
		$title       = UUID_TO_BIN('9edbc3cc-2293-4bcf-9004-8e1fdd0e5252');
		$company_id  = UUID_TO_BIN($company_id);
		$this->query = $this->olddb->prepare($sql);
		
		$loop_one = count($insert_departments);
		for ($i = 1; $i <= $loop_one; $i++) {
			$this->query->bindParam($i, $insert_departments[$i-1]);
		}
		$loop_two = count($insert_a_id) + $loop_one;
		for ($i = $loop_one + 1; $i <= $loop_two; $i++) {
			$this->query->bindParam($i, $insert_a_id[$i-$loop_two]);
		}
		
		$this->query->bindParam($loop_two+1, $title);
		$this->query->bindParam($loop_two+2, $company_id);
		
		try{
			$this->query->execute();
			$res = true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return array();
		}
		
		$deviation = array();
		$data = $this->query->fetchAll(PDO::FETCH_OBJ);
		foreach($data as $key => $val) {
			$val->a_id = BIN_TO_UUID($val->a_id);
			$deviation[$val->a_id] = substr($val->a_id,0,8) . '... - ' . $val->answer;
		}
		
		return $deviation;
	}
	
	public function getEventAnalysisNames($company_id) {
		$company_id = UUID_TO_BIN($company_id);
		$this->query = $this->olddb->prepare("SELECT 
												id, name
											FROM 
												eventanalysis
											WHERE
												company_id = ?");
					
		$this->query->bindParam(1, $company_id);
					
		try{
			$this->query->execute();
			$res = true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
		
		$tmp  = $this->query->fetchAll(PDO::FETCH_OBJ);
		$data = array();
		foreach($tmp as $d):
			$d->id = BIN_TO_UUID($d->id);
			$data[$d->id] = substr($d->id,0,8) . '... - ' . $d->name;
		endforeach;

		return $data;
	}
	
	public function insertRiskAssessmentsDeviationMap($data,$id){
		$this->removeRiskAssessmentsDeviationMap($id);
		if(!empty($data)) {
			$datafields = array('a_id','ra_id');
			list($question_marks,$insert_values) = $this->prepareQuery($data);
			
			$sql = "INSERT INTO `deviation_risk_assessments` (" . implode(",", $datafields ) . ") VALUES " . implode(',', $question_marks);
			$this->query = $this->olddb->prepare($sql);
			
			try{
				$this->query->execute($insert_values);
				return true;
			}catch(PDOException $e){
				file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
				return false;
			}
		}
		return true;
	}
	
	public function insertRiskAssessmentsEventAnalysisMap($data,$id){
		$this->removeRiskAssessmentsEventAnalysisMap($id);
		if(!empty($data)) {
			$datafields = array('ra_id','ea_id');
			list($question_marks,$insert_values) = $this->prepareQuery($data);
			
			$sql = "INSERT INTO `risk_assessments_eventanalysis` (" . implode(",", $datafields ) . ") VALUES " . implode(',', $question_marks);
			$this->query = $this->olddb->prepare($sql);
			
			try{
				$this->query->execute($insert_values);
				return true;
			}catch(PDOException $e){
				file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
				return false;
			}
		}
		return true;
	}
	
	public function insertRisk($data,$company_id) {
		$ra_id = UUIDv4();
		
		if( isset($data[0]['us_id']) )
		{
			$datafields = array('name','description','date','us_id','company_id','ra_id', 'created_date');
		}
		else
		{
			$datafields = array('name','description','date','company_id','ra_id','us_id', 'created_date');
		}
		
		$data[0]['company_id'] = UUID_TO_BIN($company_id);
		$data[0]['ra_id']      = UUID_TO_BIN($ra_id);
		$data[0]['us_id']      = isset($data[0]['us_id']) ? UUID_TO_BIN($data[0]['us_id']) : NULL;
		$data[0]['created_date'] = date('Y-m-d H:i:s');
		
		list($question_marks,$insert_values) = $this->prepareQuery($data);
		
		$sql = "INSERT INTO `risk_assessments` (" . implode(",", $datafields ) . ") VALUES " . implode(',', $question_marks);
		$this->query = $this->olddb->prepare($sql);
		
		try{
			$this->query->execute($insert_values);
			return $ra_id;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
	}
	
	public function insertRiskDepartment($data){
		$datafields = array('ra_id','de_id');
		list($question_marks,$insert_values) = $this->prepareQuery($data);
		
		$sql = "INSERT INTO `risk_assessments_department` (" . implode(",", $datafields ) . ") VALUES " . implode(',', $question_marks);
		$this->query = $this->olddb->prepare($sql);
		
		try{
			$this->query->execute($insert_values);
			return true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
	}
	
	public function insertRisks($data){
		$datafields = array('id','ra_id','risk','occurrence','severity','explanation','acceptable','measure','responsible','done','finished');
		list($question_marks,$insert_values) = $this->prepareQuery($data);
		
		// var_dump($datafields,$question_marks,$insert_values);exit;
		
		$sql = "INSERT INTO `risk_assessments_risk` (" . implode(",", $datafields ) . ") VALUES " . implode(',', $question_marks);
		$this->query = $this->olddb->prepare($sql);
		
		try{
			$this->query->execute($insert_values);
			return true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
	}
	
	public function updateRisk($data,$id) {
		if( count($data) === 3)
		{
			$datafields = array('name','description','date');
		}
		else
		{
			$data['us_id'] = UUID_TO_BIN($data['us_id']);
			$datafields = array('name','description','date','us_id');
		}
		
		$datafields = $this->prepareQueryUpdate($datafields);
		$update_values = $this->prepareQueryUpdateData($data,$id);
		
		$sql = "UPDATE `risk_assessments` SET " . implode(",", $datafields ) . " WHERE ra_id=?";
		$this->query = $this->olddb->prepare($sql);
		
		try{
			$this->query->execute($update_values);
			return true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
	}
	
	public function updateRisks($data) {
		$datafields = array('risk','occurrence','severity','explanation','acceptable','measure','responsible','done','finished');
		$datafields = $this->prepareQueryUpdate($datafields);
		
		$sql = "UPDATE `risk_assessments_risk` SET " . implode(",", $datafields ) . " WHERE id=?";
		$this->query = $this->olddb->prepare($sql);
		
		try{
			foreach($data AS $id => $val) {
				$this->query->execute($this->prepareQueryUpdateData($val,UUID_TO_BIN($id)));
			}
			return true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
	}
	
	public function removeDepartment($id) {
		$id = UUID_TO_BIN($id);
		$sql = "DELETE FROM `risk_assessments_department` WHERE ra_id=?";
		$this->query = $this->olddb->prepare($sql);
		try{
			$this->query->execute(array($id));
			return true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
	}
	
	public function removeRiskAssessmentsDeviationMap($id) {
		$id = UUID_TO_BIN($id);
		$sql = "DELETE FROM `deviation_risk_assessments` WHERE ra_id = ?";
		$this->query = $this->olddb->prepare($sql);
		try{
			$this->query->execute(array($id));
			return true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
	}
	
	public function removeRiskAssessmentsEventAnalysisMap($id) {
		$id = UUID_TO_BIN($id);
		$sql = "DELETE FROM `risk_assessments_eventanalysis` WHERE ra_id = ?";
		$this->query = $this->olddb->prepare($sql);
		try{
			$this->query->execute(array($id));
			return true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
	}
}