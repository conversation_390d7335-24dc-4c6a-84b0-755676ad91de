<?php
class Eventanalysislib {
	
	/*************** BEGIN DB **************/
	
	protected $olddb; //This is the database connection
	private $query; //This is the current query

	/**
	 * Construct a new database object.
	 */
	public function __construct($db) {
		if(!isset($this->olddb)) {
			//Connect to database
			try{
				$this->olddb = new PDO("mysql:host=". $db['hostname'] . ";dbname=" . $db['database'], 
								$db['username'], 
								$db['password'], 
								array(
									PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
									PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8"
								));
			}catch(PDOException $e){
				file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
				die('<PERSON>värr gick någonting snett, kontakta Kvalprak AB och be dem kontrollera PDO loggen.');
			}
		}
	}

	function disconnect () {
		$this->olddb = null;
		return true;
	}
	
	protected function placeholders($text, $count=0, $separator=","){
		$result = array();
		if($count > 0){
			for($x=0; $x<$count; $x++){
				$result[] = $text;
			}
		}
		return implode($separator, $result);
	}
	
	protected function prepareQuery($data,$separator=",") {
		$insert_values = array();
		$question_marks = array();
		foreach($data as $d){
			$question_marks[] = '('  . $this->placeholders('?', sizeof($d),$separator) . ')';
			$insert_values = array_merge($insert_values, array_values($d));
		}
		return array($question_marks, $insert_values);
	}
	
	protected function prepareQueryUpdate($data) {
		$return = array();
		foreach($data as $d) {
			$return[] = '`'.$d.'`=?';
		}
		return $return;
	}
	
	protected function prepareQueryUpdateData($data,$id=0) {
		$data = array_values($data);
		if($id)
			$data = array_merge($data,array($id));
		return $data;
	}
	
	/*************** END DB **************/
	
	public function getAll($company_id) {
		$company_id = UUID_TO_BIN($company_id);
		$this->query = $this->olddb->prepare("SELECT 
												company_id, id, name, us_id, redo, plan, done, created_date
											FROM 
												eventanalysis
											WHERE
												company_id = ?
											ORDER BY
												created_date DESC");
		
		$this->query->bindParam(1, $company_id);
					
		try{
			$this->query->execute();
			$res = true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
		
		$tmp  = $this->query->fetchAll(PDO::FETCH_OBJ);
		$data = array();
		foreach($tmp as $d):
			$d->company_id = BIN_TO_UUID($d->company_id);
			$d->id         = BIN_TO_UUID($d->id);
			$d->us_id      = BIN_TO_UUID($d->us_id);
			$data[] = $d;
		endforeach;

		return $data;
	}
	
	public function getAllByID($company_id, $id) {
		$company_id = UUID_TO_BIN($company_id);
		
		$ea_id = array();
		foreach($id as $i):
			$ea_id[] = UUID_TO_BIN($i);
		endforeach;
		
		if( empty($ea_id) )
			$ea_id[] = NULL;
		
		list($question_marks,$insert_values) = $this->prepareQuery([$ea_id]);

		$this->query = $this->olddb->prepare("SELECT 
												company_id, id, name, us_id, redo, plan, done
											FROM 
												eventanalysis
											WHERE
												company_id = ? AND
												`id` IN {$question_marks[0]}");
		
		$this->query->bindParam(1, $company_id);
		$loop_count = count($insert_values)+1;
		for ($i = 2; $i <= $loop_count; $i++) {
			$this->query->bindParam($i, $insert_values[$i-2]);
		}
					
		try{
			$this->query->execute();
			$res = true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
		
		$tmp  = $this->query->fetchAll(PDO::FETCH_OBJ);
		$data = array();
		foreach($tmp as $d):
			$d->company_id = BIN_TO_UUID($d->company_id);
			$d->id         = BIN_TO_UUID($d->id);
			$d->us_id      = BIN_TO_UUID($d->us_id);
			$data[$d->id] = $d;
		endforeach;

		return $data;
	}
	
	public function getEventAnalysis($id) {
		$id = UUID_TO_BIN($id);
		$this->query = $this->olddb->prepare("SELECT 
												company_id, id, name, us_id, redo, plan, done 
											FROM 
												eventanalysis 
											WHERE 
												id = ?");
		$this->query->bindParam(1, $id);
		
		try{
			$this->query->execute();
			$res = true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
		
		$tmp = $this->query->fetch(PDO::FETCH_OBJ);
		$data = NULL;
		if(!empty($tmp))
		{
			$tmp->company_id = BIN_TO_UUID($tmp->company_id);
			$tmp->id         = BIN_TO_UUID($tmp->id);
			if( ! empty($tmp->us_id) )
				$tmp->us_id = BIN_TO_UUID($tmp->us_id);
			$data = $tmp;
		}
		return $data;
	}
	
	public function getEventAnalysisQuestions($id) {
		$id = UUID_TO_BIN($id);
		$this->query = $this->olddb->prepare("SELECT 
												id, parent, question, answer, responsible
											FROM 
												eventanalysis_questions 
											WHERE 
												ea_id = ?
											ORDER BY question ASC");
		$this->query->bindParam(1, $id);
		
		try{
			$this->query->execute();
			$res = true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
		
		$questions = array();
		$answers = array();
		$data = $this->query->fetchAll(PDO::FETCH_OBJ);
		foreach($data as $key => $val) {
			$val->id          = BIN_TO_UUID($val->id);
			$val->responsible = BIN_TO_UUID($val->responsible);
			if(!empty($val->parent))
				$val->parent = BIN_TO_UUID($val->parent);
			else
				$val->parent = 0;
			$questions[$val->parent][] = $val;
			if(empty($val->answer))
				$answers[$val->responsible][] = $val->id;
		}
		return array($questions,$answers);
	}
	
	public function getEventAnalysisActionList($id) {
		$id = UUID_TO_BIN($id);
		$this->query = $this->olddb->prepare("SELECT 
												id, name, description, responsible, done 
											FROM 
												eventanalysis_actionlist 
											WHERE 
												ea_id = ?");
		$this->query->bindParam(1, $id);
		
		try{
			$this->query->execute();
			$res = true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
		
		$tmp  = $this->query->fetchAll(PDO::FETCH_OBJ);
		$data = array();
		if(!empty($tmp))
		{
			foreach($tmp as $d):
				$d->id = BIN_TO_UUID($d->id);
				if( ! empty($d->responsible) )
					$d->responsible = BIN_TO_UUID($d->responsible);
				$data[] = $d;
			endforeach;
		}
		
		return $data;
	}
	
	public function getEventAnalysisQuestionsOnResponsible($user) {
		$this->query = $this->olddb->prepare("SELECT 
												ea.id, ea.name, ea.redo 
											FROM 
												`eventanalysis_questions` AS eaq 
											LEFT JOIN 
												`eventanalysis`AS ea 
													ON eaq.ea_id=ea.id 
											WHERE 
												eaq.responsible = ? AND 
												eaq.answer = '' 
											GROUP BY 
												eaq.ea_id");
		$this->query->bindParam(1, $user);
		
		try{
			$this->query->execute();
			$res = true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
		
		$data = $this->query->fetchAll(PDO::FETCH_OBJ);
		return $data;
	}
	
	public function getEventAnalysisDeviationMap($id) {
		$id = UUID_TO_BIN($id);
		$this->query = $this->olddb->prepare("SELECT 
												a_id
											FROM 
												deviation_eventanalysis 
											WHERE 
												ea_id = ?");
		$this->query->bindParam(1, $id);
		
		try{
			$this->query->execute();
			$res = true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
		
		$deviation = array();
		$data = $this->query->fetchAll(PDO::FETCH_OBJ);
		foreach($data as $key => $val) {
			$deviation[] = BIN_TO_UUID($val->a_id);
		}
		
		return $deviation;
	}
	
	public function getEventAnalysisRiskAssessmentsMap($id) {
		$id = UUID_TO_BIN($id);
		$this->query = $this->olddb->prepare("SELECT 
												ra_id
											FROM 
												risk_assessments_eventanalysis 
											WHERE 
												ea_id = ?");
		$this->query->bindParam(1, $id);
		
		try{
			$this->query->execute();
			$res = true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
		
		$riskassessments = array();
		$data = $this->query->fetchAll(PDO::FETCH_OBJ);
		foreach($data as $key => $val) {
			$riskassessments[] = BIN_TO_UUID($val->ra_id);
		}
		
		return $riskassessments;
	}
	
	public function getEventAnalysisDeviation($departments_id,$user_id,$company_id) {
		$departments = array();
		foreach($departments_id as $dep):
			$departments[] = UUID_TO_BIN($dep);
		endforeach;
		
		if( empty($departments) )
			$departments[] = NULL;
		
		list($question_marks,$insert_values) = $this->prepareQuery([$departments]);
		
		$sql = "SELECT 
					dfa.a_id,
					d.eventanalysis,
					df.df_id,
					df.input,
					df.search,
					df.list,
					df.title,
					df.required,
					df.required_kvalprak,
					df.description,
					da.answer
				FROM
					deviation AS d
				INNER JOIN
					deviation_fields_active AS dfa
						ON dfa.a_id=d.a_id
				INNER JOIN
					deviation_fields AS df
						ON df.df_id=dfa.df_id
				LEFT JOIN
					deviation_answers AS da
						ON da.a_id=dfa.a_id AND da.df_id=dfa.df_id
				INNER JOIN 
					deviation_department AS dd ON dd.a_id=dfa.a_id
					AND dd.de_id IN {$question_marks[0]}
				WHERE
					d.eventanalysis = ? AND
					d.company_id = ?
				GROUP BY
					df.df_id
				ORDER BY 
					dfa.a_id DESC,
					df.page ASC,
					df.order ASC,
					df.title ASC";
					
		$user_id     = UUID_TO_BIN($user_id);
		$company_id  = UUID_TO_BIN($company_id);
		$this->query = $this->olddb->prepare($sql);
		
		$loop_count = count($insert_values);
		for ($i = 1; $i <= $loop_count; $i++) {
			$this->query->bindParam($i, $insert_values[$i-1]);
		}
		$this->query->bindParam($loop_count+1, $user_id);
		$this->query->bindParam($loop_count+2, $company_id);
		
		try{
			$this->query->execute();
			$res = true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
		
		$deviation = array();
		$data = $this->query->fetchAll(PDO::FETCH_OBJ);
		foreach($data as $key => $val) {
			$val->a_id  = BIN_TO_UUID($val->a_id);
			$val->df_id = BIN_TO_UUID($val->df_id);
			$val->eventanalysis = BIN_TO_UUID($val->eventanalysis);
			$deviation[$val->a_id][$val->df_id] = $val;
		}
		
		return $deviation;
	}
	
	public function getEventAnalysisDeviationUserById($id,$company_id) {
		$id = UUID_TO_BIN($id);
		$company_id = UUID_TO_BIN($company_id);
		$sql = "SELECT 
					d.a_id,
					d.eventanalysis
				FROM
					deviation AS d
				WHERE
					d.a_id = ? AND
					d.company_id = ?
				LIMIT 1";
		$this->query = $this->olddb->prepare($sql);
		$this->query->bindParam(1, $id);
		$this->query->bindParam(2, $company_id);
		
		try{
			$this->query->execute();
			$res = true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
		
		$data = $this->query->fetch(PDO::FETCH_OBJ);
		$data->a_id = BIN_TO_UUID($data->a_id);
		$data->eventanalysis = BIN_TO_UUID($data->eventanalysis);
		
		return $data;
	}
	// @STEP2: Store "Rubrik" in devation instead
	public function getDeviationNames($departments_id,$deviation_id,$company_id) {
		$departments = array();
		foreach($departments_id as $dep):
			$departments[] = UUID_TO_BIN($dep);
		endforeach;
		$a_id = array();
		foreach($deviation_id as $dev):
			$a_id[] = UUID_TO_BIN($dev);
		endforeach;
		
		if( empty($departments) )
			$departments[] = NULL;
		if( empty($a_id) )
			$a_id[] = NULL;
		
		list($question_departments,$insert_departments) = $this->prepareQuery([$departments]);
		list($question_a_id,$insert_a_id) = $this->prepareQuery([$a_id]);
		
		$sql ="SELECT 
					a.a_id, a.answer
				FROM 
					deviation AS d
				LEFT JOIN
					deviation_department AS de
						ON de.a_id=d.a_id
				LEFT JOIN
					deviation_answers AS a
						ON a.a_id=d.a_id
				WHERE
					de.de_id IN {$question_departments[0]} AND
					(d.regby_three IS NOT NULL OR d.a_id IN {$question_a_id[0]}) AND
					a.df_id = ? AND
					d.company_id = ?
				GROUP BY 
					d.a_id";
					
		// var_dump($question_departments,$insert_departments);
		// var_dump($question_a_id,$insert_a_id);
					
		// $departments = implode(',',$departments);
		$a_id        = implode(',',$a_id);
		$title       = UUID_TO_BIN('9edbc3cc-2293-4bcf-9004-8e1fdd0e5252');
		$company_id  = UUID_TO_BIN($company_id);
		$this->query = $this->olddb->prepare($sql);
		
		$loop_one = count($insert_departments);
		for ($i = 1; $i <= $loop_one; $i++) {
			// echo $i;
			$this->query->bindParam($i, $insert_departments[$i-1]);
		}
		$loop_two = count($insert_a_id) + $loop_one;
		for ($i = $loop_one + 1; $i <= $loop_two; $i++) {
			// echo $i;
			$this->query->bindParam($i, $insert_a_id[$i-$loop_two]);
		}
		// $this->query->bindParam(1, $departments);
		// $this->query->bindParam(2, $a_id);
		$this->query->bindParam($loop_two+1, $title);
		$this->query->bindParam($loop_two+2, $company_id);
		
		// exit;
		
		// var_dump($this->query,$departments,$a_id,$title,$company_id);exit;
		
		try{
			$this->query->execute();
			// $this->query->debugDumpParams();
			$res = true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return array();
		}
		
		$deviation = array();
		$data = $this->query->fetchAll(PDO::FETCH_OBJ);
		foreach($data as $key => $val) {
			$val->a_id = BIN_TO_UUID($val->a_id);
			$deviation[$val->a_id] = substr($val->a_id,0,8) . '... - ' . $val->answer;
		}
		
		return $deviation;
	}
	
	// @STEP2: Store "Rubrik" in devation instead
	public function getDeviationEventAnalysis($departments_id,$company_id) {
		$departments = array();
		foreach($departments_id as $dep):
			$departments[] = UUID_TO_BIN($dep);
		endforeach;
		
		if( empty($departments) )
			$departments[] = NULL;
		
		list($question_departments,$insert_departments) = $this->prepareQuery([$departments]);
		
		$sql ="SELECT 
					a.a_id, a.answer as title
				FROM 
					deviation AS d
				LEFT JOIN
					deviation_department AS de
						ON de.a_id=d.a_id
				LEFT JOIN
					deviation_answers AS a
						ON a.a_id=d.a_id
				WHERE
					d.eventanalysis IS NOT NULL AND
					d.regby_three IS NOT NULL AND
					de.de_id IN {$question_departments[0]} AND
					a.df_id = ? AND
					d.company_id = ?
				GROUP BY 
					d.a_id";
					
		// $departments = implode(',',$departments);
		$title       = UUID_TO_BIN('9edbc3cc-2293-4bcf-9004-8e1fdd0e5252');
		$company_id  = UUID_TO_BIN($company_id);
		$this->query = $this->olddb->prepare($sql);
		$loop_one = count($insert_departments);
		for ($i = 1; $i <= $loop_one; $i++) {
			// echo $i;
			$this->query->bindParam($i, $insert_departments[$i-1]);
		}
		// $this->query->bindParam(1, $departments);
		$this->query->bindParam(2, $title);
		$this->query->bindParam(3, $company_id);
		
		// var_dump($this->query,$departments,$a_id,$title,$company_id);exit;
		
		try{
			$this->query->execute();
			$res = true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return array();
		}
		
		$deviation = array();
		$data = $this->query->fetchAll(PDO::FETCH_OBJ);
		foreach($data as $key => $val) {
			$val->a_id = BIN_TO_UUID($val->a_id);
			$deviation[] = $val;
		}
		
		return $deviation;
	}
	
	// @STEP2: Add department
	public function getRiskAnalysisNames($company_id) {
		$company_id = UUID_TO_BIN($company_id);
		
		$sql ="SELECT 
					ra_id, name
				FROM 
					risk_assessments
				WHERE
					company_id = ?";
		
		$this->query = $this->olddb->prepare($sql);
		$this->query->bindParam(1, $company_id);
					
		try{
			$this->query->execute();
			$res = true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return array();
		}
		
		$eventanalysis = array();
		$data = $this->query->fetchAll(PDO::FETCH_OBJ);
		foreach($data as $key => $val) {
			$val->ra_id = BIN_TO_UUID($val->ra_id);
			$eventanalysis[$val->ra_id] = substr($val->ra_id,0,8) . '... - ' . $val->name;
		}
		
		return $eventanalysis;
	}
	
	public function insertEventAnalysisDeviationMap($data,$id){
		$this->removeEventAnalysisDeviationMap($id);
		if(!empty($data)) {
			$datafields = array('a_id','ea_id');
			list($question_marks,$insert_values) = $this->prepareQuery($data);
			
			$sql = "INSERT INTO `deviation_eventanalysis` (" . implode(",", $datafields ) . ") VALUES " . implode(',', $question_marks);
			$this->query = $this->olddb->prepare($sql);
			
			try{
				$this->query->execute($insert_values);
				return true;
			}catch(PDOException $e){
				file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
				return false;
			}
		}
		return true;
	}
	
	public function insertEventAnalysisRiskAssessmentsMap($data,$id){
		$this->removeEventAnalysisRiskAssessmentsMap($id);
		if(!empty($data)) {
			$datafields = array('ra_id','ea_id');
			list($question_marks,$insert_values) = $this->prepareQuery($data);
			
			$sql = "INSERT INTO `risk_assessments_eventanalysis` (" . implode(",", $datafields ) . ") VALUES " . implode(',', $question_marks);
			$this->query = $this->olddb->prepare($sql);
			
			try{
				$this->query->execute($insert_values);
				return true;
			}catch(PDOException $e){
				file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
				return false;
			}
		}
		return true;
	}
	
	public function insertEventAnalysis($data,$company_id) {
		if( count($data[0]) === 3)
		{
			$datafields = array('name','redo','us_id','company_id','id','created_date');
		}
		else
		{
			$datafields = array('name','redo','company_id','id','us_id','created_date');
		}
		
		$id = UUIDv4();
		$data[0]['company_id']   = UUID_TO_BIN($company_id);
		$data[0]['id']           = UUID_TO_BIN($id);
		$data[0]['us_id']        = isset($data[0]['us_id']) ? UUID_TO_BIN($data[0]['us_id']) : NULL;
		$data[0]['created_date'] = date('Y-m-d H:i:s');
		
		list($question_marks,$insert_values) = $this->prepareQuery($data);
		
		$sql = "INSERT INTO `eventanalysis` (" . implode(",", $datafields ) . ") VALUES " . implode(',', $question_marks);
		$this->query = $this->olddb->prepare($sql);
		
		try{
			$this->query->execute($insert_values);
			return $id;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
	}
	
	public function insertEventAnalysisQuestions($data){
		$datafields = array('id','ea_id','parent','question','answer','responsible');
		list($question_marks,$insert_values) = $this->prepareQuery($data);
		
		$sql = "INSERT INTO `eventanalysis_questions` (" . implode(",", $datafields ) . ") VALUES " . implode(',', $question_marks);
		$this->query = $this->olddb->prepare($sql);
		
		try{
			$this->query->execute($insert_values);
			return TRUE;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
	}
	
	public function insertEventAnalysisActionList($data){
		$datafields = array('id','ea_id','name','description','responsible','done');
		list($question_marks,$insert_values) = $this->prepareQuery($data);
		
		$sql = "INSERT INTO `eventanalysis_actionlist` (" . implode(",", $datafields ) . ") VALUES " . implode(',', $question_marks);
		$this->query = $this->olddb->prepare($sql);
		
		try{
			$this->query->execute($insert_values);
			return $this->olddb->lastInsertId();
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
	}
	
	public function updateEventAnalysis($data,$id) {
		if( count($data) === 2 )
		{
			$datafields = array('name','redo');
		}
		else
		{
			$data['us_id'] = UUID_TO_BIN($data['us_id']);
			$datafields = array('name','redo','us_id');
		}
		
		$datafields = $this->prepareQueryUpdate($datafields);
		$update_values = $this->prepareQueryUpdateData($data,$id);
		
		$sql = "UPDATE `eventanalysis` SET " . implode(",", $datafields ) . " WHERE id=?";
		$this->query = $this->olddb->prepare($sql);
		
		try{
			$this->query->execute($update_values);
			return true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
	}
	
	public function updateEventAnalysisDeviation($data,$id) {
		$id = UUID_TO_BIN($id);
		$datafields = array('eventanalysis');
		$datafields = $this->prepareQueryUpdate($datafields);
		$update_values = $this->prepareQueryUpdateData($data,$id);
		
		$sql = "UPDATE `deviation` SET " . implode(",", $datafields ) . " WHERE a_id=?";
		$this->query = $this->olddb->prepare($sql);
		
		try{
			$this->query->execute($update_values);
			return true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
	}
	
	public function updateEventAnalysisComplete($data,$id) {
		if( count($data) === 4 )
		{
			$datafields = array('name','redo','plan','done');
		}
		else
		{
			$data['us_id'] = UUID_TO_BIN($data['us_id']);
			$datafields = array('name','redo','us_id','plan','done');
		}

		$datafields = $this->prepareQueryUpdate($datafields);
		$update_values = $this->prepareQueryUpdateData($data,$id);
		
		$sql = "UPDATE `eventanalysis` SET " . implode(",", $datafields ) . " WHERE id=?";
		$this->query = $this->olddb->prepare($sql);
		
		try{
			$this->query->execute($update_values);
			return true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
	}
	
	public function updateEventAnalysisQuestions($data,$save = false) {
		if($save)
			$datafields = array('answer');
		else
			$datafields = array('question','answer','responsible');
		
		$datafields = $this->prepareQueryUpdate($datafields);
		
		$sql = "UPDATE `eventanalysis_questions` SET " . implode(",", $datafields ) . " WHERE id=?";
		$this->query = $this->olddb->prepare($sql);
		
		try{
			foreach($data AS $id => $val) {
				$this->query->execute($this->prepareQueryUpdateData($val,UUID_TO_BIN($id)));
			}
			return true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
	}
	
	public function updateEventAnalysisActionList($data) {
		$datafields = array('name','description','responsible','done');
		$datafields = $this->prepareQueryUpdate($datafields);
		
		$sql = "UPDATE `eventanalysis_actionlist` SET " . implode(",", $datafields ) . " WHERE id=?";
		$this->query = $this->olddb->prepare($sql);
		
		try{
			foreach($data AS $id => $val) {
				$this->query->execute($this->prepareQueryUpdateData($val,UUID_TO_BIN($id)));
			}
			return true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
	}
	
	public function removeEventAnalysisDeviationMap($id) {
		$id = UUID_TO_BIN($id);
		$sql = "DELETE FROM `deviation_eventanalysis` WHERE ea_id = ?";
		$this->query = $this->olddb->prepare($sql);
		try{
			$this->query->execute(array($id));
			return true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
	}
	
	public function removeEventAnalysisRiskAssessmentsMap($id) {
		$id = UUID_TO_BIN($id);
		$sql = "DELETE FROM `risk_assessments_eventanalysis` WHERE ea_id = ?";
		$this->query = $this->olddb->prepare($sql);
		try{
			$this->query->execute(array($id));
			return true;
		}catch(PDOException $e){
			file_put_contents(APPPATH.'PDOErrors.txt', $e->getMessage() . "\n", FILE_APPEND);
			return false;
		}
	}
}