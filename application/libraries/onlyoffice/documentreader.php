<?php
/* 
this class is used to convert any docx,xlsx,pptx file to simple text format.
*/ 

if (!defined('PCLZIP_TEMPORARY_DIR')) {
  define('PCLZIP_TEMPORARY_DIR', sys_get_temp_dir(). '/');
}
require_once( dirname(__FILE__) . '/../../../vendor/phpoffice/phpword/src/PhpWord/Shared/PCLZip/pclzip.lib.php');

class DocConversion{
  private $filename;
  
  public function __construct($filePath) {
    $this->filename = $filePath;
  }

  /**
  * Returns the index of the entry in the archive (emulate \ZipArchive)
  *
  * @param string $filename Filename for the file in zip archive
  * @return int
  */
  private function pclzipLocateName($zip, $filename)
  {
      $list = $zip->listContent();
      $listCount = count($list);
      $listIndex = -1;
      for ($i = 0; $i < $listCount; ++$i) {
          if (strtolower($list[$i]['filename']) == strtolower($filename) ||
              strtolower($list[$i]['stored_filename']) == strtolower($filename)) {
              $listIndex = $i;
              break;
          }
      }

      return ($listIndex > -1) ? $listIndex : false;
  }
  
  private function read_docx(){
    
    $xml_filename = "word/document.xml"; //content file name
    $zip = new \PclZip($this->filename);
    $listIndex = $this->pclzipLocateName($zip, $xml_filename);
    $output_text = "";
    if ($listIndex !== false) {
      $extracted = $zip->extractByIndex($listIndex, PCLZIP_OPT_EXTRACT_AS_STRING);
      if ((is_array($extracted)) && ($extracted != 0)) {
        $xml_datas = $extracted[0]['content'];
        $xml_handle = simplexml_load_string($xml_datas);
        $output_text = $xml_handle->saveXML();
        $output_text = str_replace('</w:r></w:p></w:tc><w:tc>', " ", $output_text);
        $output_text = str_replace('</w:r></w:p>', "\r\n", $output_text);
        $output_text = strip_tags($output_text);
      }
    }
    return $output_text;
  }
  
  /************************excel sheet************************************/
  
  function xlsx_to_text(){
    $xml_filename = "xl/sharedStrings.xml"; //content file name
    $zip = new \PclZip($this->filename);
    $listIndex = $this->pclzipLocateName($zip, $xml_filename);
    $output_text = "";
    if ($listIndex !== false) {
      $extracted = $zip->extractByIndex($listIndex, PCLZIP_OPT_EXTRACT_AS_STRING);
      if ((is_array($extracted)) && ($extracted != 0)) {
        $xml_datas = $extracted[0]['content'];
        $xml_handle = simplexml_load_string($xml_datas, 'SimpleXMLElement', LIBXML_NOENT | LIBXML_XINCLUDE | LIBXML_NOERROR | LIBXML_NOWARNING);
        $output_text = strip_tags($xml_handle->saveXML());
      }
    }
    return $output_text;
  }
  
  /*************************power point files*****************************/
  function pptx_to_text(){
    $zip = new \PclZip($this->filename);
    $output_text = "";
    $slide_number = 1; //loop through slide files
    while(($xml_index = $this->pclzipLocateName($zip, "ppt/slides/slide".$slide_number.".xml")) !== false){
      $extracted = $zip->extractByIndex($xml_index, PCLZIP_OPT_EXTRACT_AS_STRING);
      if ((is_array($extracted)) && ($extracted != 0)) {
        $xml_datas = $extracted[0]['content'];
        $xml_handle = simplexml_load_string($xml_datas, 'SimpleXMLElement', LIBXML_NOENT | LIBXML_XINCLUDE | LIBXML_NOERROR | LIBXML_NOWARNING);
        $output_text .= strip_tags($xml_handle->saveXML());
        $slide_number++;
      }
    }
    return $output_text;
  }
  
  public function convertToText() {
    
    if(isset($this->filename) && !file_exists($this->filename)) {
      return "File Not exists";
    }
    
    $fileArray = pathinfo($this->filename);
    $file_ext  = $fileArray['extension'];
    if($file_ext == "docx" || $file_ext == "xlsx" || $file_ext == "pptx")
    {
      if($file_ext == "docx") {
        return $this->read_docx();
      } elseif($file_ext == "xlsx") {
        return $this->xlsx_to_text();
      }elseif($file_ext == "pptx") {
        return $this->pptx_to_text();
      }
    } else {
      return "Invalid File Type";
    }
  }

}
?>