<?php

function track($filename) {
    log_message('debug', "Track START - " . $filename);
    $result;
    $result["error"] = 0;

    $data = readBody();
    if (array_key_exists("error", $data)){
        return $data;
    }

    $_trackerStatus = array(
        0 => 'NotFound',
        1 => 'Editing',
        2 => 'MustSave',
        3 => 'Corrupted',
        4 => 'Closed',
        6 => 'MustForceSave',
        7 => 'CorruptedForceSave'
    );

    $status = $_trackerStatus[$data["status"]];
    $userAddress = $_GET["userAddress"];
    switch ($status) {
        case "Editing":
            if ($data["actions"] && $data["actions"][0]["type"] == 0) {
                $user = $data["actions"][0]["userid"];
                if (array_search($user, $data["users"]) === FALSE) {
                    $commandRequest = commandRequest("forcesave", $data["key"]);
                }
            }
            break;
        case "MustSave":
        case "Corrupted":
            $result = processSave($data, $filename, $userAddress);
            break;
        case "MustForceSave":
        case "CorruptedForceSave":
            $result = processSave($data, $filename, $userAddress);
            break;
    }

    return $result;
}

function convert($fileName) {
    $post = json_decode(file_get_contents('php://input'), true);
    $filePass = $post["filePass"];
    $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
    $internalExtension = trim(getInternalExtension($fileName),'.');

    if (in_array("." + $extension, $GLOBALS['DOC_SERV_CONVERT']) && $internalExtension != "") {

        $fileUri = $post["fileUri"];
        if ($fileUri == NULL || $fileUri == "") {
            $fileUri = FileUri($fileName);
        }
        $key = getDocEditorKey($fileName);

        $newFileUri;
        $result;
        $percent;

        try {
            $percent = GetConvertedUri($fileUri, $extension, $internalExtension, $key, TRUE, $newFileUri, $filePass);
        }
        catch (Exception $e) {
            $result["error"] = "error: " . $e->getMessage();
            return $result;
        }

        if ($percent != 100)
        {
            $result["step"] = $percent;
            $result["filename"] = $fileName;
            $result["fileUri"] = $fileUri;
            return $result;
        }

        $baseNameWithoutExt = substr($fileName, 0, strlen($fileName) - strlen($extension) - 1);

        $newFileName = GetCorrectName($baseNameWithoutExt . "." . $internalExtension);

        if (($data = file_get_contents(str_replace(" ","%20",$newFileUri))) === FALSE) {
            $result["error"] = 'Bad Request';
            return $result;
        } else {
            file_put_contents($newFileName, $data, LOCK_EX);
            createMeta($newFileName);
        }

        $stPath = $fileName;
        unlink($stPath);
        delTree(getHistoryDir($stPath));

        $fileName = $newFileName;
    }

    $result["filename"] = $fileName;
    return $result;
}

function delete($filePath) {
    try {
        unlink($filePath);
        delTree(getHistoryDir($filePath));
    }
    catch (Exception $e) {
        sendlog("Deletion ".$e->getMessage(), "webedior-ajax.log");
        $result["error"] = "error: " . $e->getMessage();
        return $result;
    }
}

function assets() {
    $fileName = basename($_GET["name"]);
    $filePath = dirname(__FILE__) . DIRECTORY_SEPARATOR . "assets" . DIRECTORY_SEPARATOR . "sample" . DIRECTORY_SEPARATOR . $fileName;
    downloadFile($filePath);
}

function csv() {
    $fileName =  "csv.csv";
    $filePath = dirname(__FILE__) . DIRECTORY_SEPARATOR . "assets" . DIRECTORY_SEPARATOR . "sample" . DIRECTORY_SEPARATOR . $fileName;
    downloadFile($filePath);
}

function download($filePath) {
    try {
        downloadFile($filePath);
    } catch (Exception $e) {
        sendlog("Download ".$e->getMessage(), "webedior-ajax.log");
        $result["error"] = "error: File not found";
        return $result;
    }
}

function downloadFile($filePath) {
    if (file_exists($filePath)) {
        if (ob_get_level()) {
            ob_end_clean();
        }

        @header('Content-Length: ' . filesize($filePath));
        @header('Content-Disposition: attachment; filename*=UTF-8\'\'' . urldecode(basename($filePath)));
        @header('Content-Type: ' . mime_content_type($filePath));
        @header('Access-Control-Allow-Origin: *');

        if ($fd = fopen($filePath, 'rb')) {
            while (!feof($fd)) {
                print fread($fd, 1024);
            }
            fclose($fd);
        }
        exit;
    }
}

function delTree($dir) {
    if (!file_exists($dir) || !is_dir($dir)) return;

    $files = array_diff(scandir($dir), array('.','..'));
    foreach ($files as $file) {
        (is_dir("$dir/$file")) ? delTree("$dir/$file") : unlink("$dir/$file");
    }
    return rmdir($dir);
}
