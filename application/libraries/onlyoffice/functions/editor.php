<?php

function getCallbackUrl($company_id, $fileName, $doctype) {
    return serverPath(TRUE) . '/'
                . "documentcallback/" . $company_id . '/' . $fileName . $doctype
                . "?type=track"
                . "&userAddress=" . getClientIp();
}

function getHistory($filename, $filetype, $docKey, $fileuri) {
    $histDir = getHistoryDir($filename);

    if (getFileVersion($histDir) > 0) {
        $curVer = getFileVersion($histDir);

        $hist = [];
        $histData = [];

        for ($i = 1; $i <= $curVer; $i++) {
            $obj = [];
            $dataObj = [];
            $verDir = getVersionDir($histDir, $i);

            $key = $i == $curVer ? $docKey : (is_file($verDir . DIRECTORY_SEPARATOR . "key.txt") ? 
                file_get_contents($verDir . DIRECTORY_SEPARATOR . "key.txt") : $docKey . $i);
            $obj["key"] = $key;
            $obj["version"] = $i;

            if ($i == 1 && is_file($histDir . DIRECTORY_SEPARATOR . "createdInfo.json")) {
                $createdInfo = file_get_contents($histDir . DIRECTORY_SEPARATOR . "createdInfo.json");
                $json = json_decode($createdInfo, true);

                $obj["created"] = $json["created"];
                $obj["user"] = [
                    "id" => $json["uid"],
                    "name" => $json["name"]
                ];
            }

            // $prevFileName = $verDir . DIRECTORY_SEPARATOR . "prev." . $filetype;
            // $prevFileName = substr($prevFileName, strlen(dirname(dirname($prevFileName))));
            $dataObj["key"] = $key;
            $dataObj["fileType"] = $filetype;
            $dataObj["url"] = $i == $curVer ? $fileuri : $fileuri . '?version=' . $i;
            $dataObj["version"] = $i;

            if ($i > 1) {
                if (is_file(getVersionDir($histDir, $i - 1) . DIRECTORY_SEPARATOR . "changes.json")) {
                $changes = json_decode(file_get_contents(getVersionDir($histDir, $i - 1) . DIRECTORY_SEPARATOR . "changes.json"), true);
                $change = $changes["changes"][0];

                $obj["changes"] = $changes["changes"];
                $obj["serverVersion"] = $changes["serverVersion"];
                $obj["created"] = $change["created"];
                $obj["user"] = $change["user"];
                }

                $prev = $histData[$i - 2];
                $dataObj["previous"] = [
                    "fileType" => $filetype,
                    "key" => $prev["key"],
                    "url" => $prev["url"]
                ];
                // $changesUrl = getVersionDir($histDir, $i - 1) . DIRECTORY_SEPARATOR . "diff.zip";
                // $changesUrl = substr($changesUrl, strlen(dirname(dirname($changesUrl))));
                $dataObj["changesUrl"] =  $fileuri . '?version=' . $i . '&diff=true';
            }

            if (isJwtEnabled()) {
                $dataObj["token"] = jwtEncode($dataObj);
            }

            array_push($hist, $obj);
            $histData[$i - 1] = $dataObj;
        }

        $out = [];
        array_push($out, [
                "currentVersion" => $curVer,
                "history" => $hist
            ],
            $histData);
        return $out;
    }
}
