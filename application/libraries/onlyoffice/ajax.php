<?php
/**
 *
 * (c) Copyright Ascensio System SIA 2021
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

require( dirname(__FILE__) . '/config.php' );

//Function to check if the request is an AJAX request
function is_ajax() {
    return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';
}

function get_http_origin() {
    $origin = '';
    if ( ! empty ( $_SERVER[ 'HTTP_ORIGIN' ] ) )
        $origin = $_SERVER[ 'HTTP_ORIGIN' ];
    return $origin;
}

function nocache_headers() {
    $headers = array(
        'Expires' => 'Wed, 11 Jan 1984 05:00:00 GMT',
        'Cache-Control' => 'no-cache, must-revalidate, max-age=0',
        'Pragma' => 'no-cache',
    );
    $headers['Last-Modified'] = false;


    unset( $headers['Last-Modified'] );

    // In PHP 5.3+, make sure we are not sending a Last-Modified header.
    if ( function_exists( 'header_remove' ) ) {
        @header_remove( 'Last-Modified' );
    } else {
        // In PHP 5.2, send an empty Last-Modified header, but only as a
        // last resort to override a header already sent. #WP23021
        foreach ( headers_list() as $header ) {
            if ( 0 === stripos( $header, 'Last-Modified' ) ) {
                $headers['Last-Modified'] = '';
                break;
            }
        }
    }

    foreach( $headers as $name => $field_value )
        @header("{$name}: {$field_value}");
}
