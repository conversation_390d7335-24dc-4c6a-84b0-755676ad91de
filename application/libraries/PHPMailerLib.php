<?php

use <PERSON><PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

#[\AllowDynamicProperties]
class PHPMailerLib
{
	public function __construct()
	{
		log_message('Debug', 'PHPMailer class is loaded.');
		$this->ci =& get_instance();
		$this->ci->load->config('email');
	}

	public function load()
	{
		require_once(APPPATH . 'third_party/PHPMailer/src/PHPMailer.php');
		require_once(APPPATH . 'third_party/PHPMailer/src/Exception.php');
		require_once(APPPATH . 'third_party/PHPMailer/src/SMTP.php');

		$objMail = new PHPMailer();
		$objMail->setLanguage('se');
		$objMail->CharSet = 'UTF-8';

		if( $this->ci->config->item('systemEmailMethod') === 'SMTP' )
		{
			$objMail->isSMTP();
			$objMail->Host = $this->ci->config->item('systemEmailSmtpHost');
			$objMail->Port = $this->ci->config->item('systemEmailSmtpPort');
			if( $this->ci->config->item('systemEmailSmtpUser') ) {
				$objMail->SMTPAuth = true; // turn on SMTP authentication
				$objMail->Username = $this->ci->config->item('systemEmailSmtpUser'); // SMTP username
				$objMail->Password = $this->ci->config->item('systemEmailSmtpPass'); // SMTP password
			}
			if( $this->ci->config->item('systemEmailSSL') ) {
				$objMail->SMTPSecure = $this->ci->config->item('systemEmailSSL');
			}

			$objMail->smtpConnect(
				array(
					"ssl" => array(
						"verify_peer" => false,
						"verify_peer_name" => false,
						"allow_self_signed" => true
					)
				)
			);
		}

		$objMail->From = $this->ci->config->item('systemEmailFrom');
		$objMail->FromName = $this->ci->config->item('systemEmailFromName');
		$objMail->isHTML(true); // This tell's the PhPMailer that the messages uses HTML.

		return $objMail;
	}
}
