<?php
defined('BASEPATH') OR exit('No direct script access allowed');

if ( ! function_exists('safe_anchor'))
{
	/**
	 * Safe Anchor Link
	 *
	 * Creates an anchor based on the local URL.
	 *
	 * @param	string	the URL
	 * @param   string  append id
	 * @param	string	the link title
	 * @param	mixed	any attributes
	 * @return	string
	 */
	function safe_anchor($uri = '', $id = '', $title = '', $attributes = '', $escape_title = true)
	{
		$id    = (array)  $id;
		$title = (string) $title;
		$ids   = array();

		if( !empty($id) )
		{
			foreach($id as $value)
			{
				if( !empty($value) )
				{
					$ids[] = urlencode($value);
				}
			}
		}

		if( !empty($ids) )
		{
			$uri .= '/' . implode('/',$ids);
		}

		if($title !== '' && $escape_title)
		{
			$title = html_escape($title);
		}

		return anchor($uri, $title, $attributes);
	}
}

if ( ! function_exists('icon_anchor'))
{

	/**
	 * Icon Anchor Link
	 *
	 * Creates an anchor based on the local URL.
	 *
	 * @param	string	the URL
	 * @param   string  append id
	 * @param	string	the link title
	 * @param	mixed	any attributes
	 * @return	string
	 */
	function icon_anchor($uri = '', $id = '', $title = '', $attributes = '')
	{
		$id    = (array)  $id;
		$title = (string) $title;
		$ids   = array();

		if( !empty($id) )
		{
			foreach($id as $value)
			{
				if( !empty($value) )
				{
					$ids[] = urlencode($value);
				}
			}
		}

		if( !empty($ids) )
		{
			$uri .= '/' . implode('/',$ids);
		}

		return anchor($uri, $title, $attributes);
	}
}

if ( ! function_exists('get_flashdata'))
{
	function get_flashdata($name = NULL, $type = 'success', $destroy = TRUE, $escape = TRUE)
	{
		if( ! empty($name) && isset($_SESSION[$name]) )
		{
			$tmp = $_SESSION[$name];

			if( ! in_array($type, ['danger','info','warning','success']) )
				$type = 'success';
			if( $destroy )
				unset($_SESSION[$name]);

			return '<div class="alert alert-' . html_escape($type) . ' no-print">' . ($escape ? html_escape($tmp) : $tmp) . '</div>';
		}
	}
}

if ( ! function_exists('cdn_url'))
{
	/**
	 * CDN URL
	 *
	 * Returns cdn_url [. uri_string]
	 *
	 * @param string $uri
	 *
	 * @return string
	 */
	function cdn_url($uri = '')
	{
		return get_instance()->config->item('cdn_url') . $uri;
	}
}
