<?php  
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Community Auth - Auth Helper
 *
 * Community Auth is an open source authentication application for CodeIgniter 3
 *
 * @package     Community Auth
 * <AUTHOR>
 * @copyright   Copyright (c) 2011 - 2018, <PERSON>. (http://brianswebdesign.com/)
 * @license     BSD - http://www.opensource.org/licenses/BSD-3-Clause
 * @link        http://community-auth.com
 */

// ------------------------------------------------------------------------

/**
 * Allows to check role just about anywhere
 *
 * @param string The role
 * @return bool 
 */
if( ! function_exists('is_role') )
{
	function is_role( $role = '' )
	{
		$CI =& get_instance();

		if( ! isset($CI->groups['security'][$role]) )
			return FALSE;

		return in_array($CI->groups['security'][$role], $CI->groups['types']['uuid']['security']) ? TRUE : FALSE;
	}
}

// ------------------------------------------------------------------------

/**
 * Allows to check ACL permissions just about anywhere
 *
 * @param string The category name and action code to check for the logged in user.
 *               This string is joined with a period.
 * @return bool 
 */
if( ! function_exists('acl_group_permits') )
{
	function acl_group_permits( $str, $groups, $emptygroups = FALSE )
	{
		$CI =& get_instance();

		return $CI->auth_model->acl_group_permits( $str, $groups, $emptygroups );
	}
}

// ------------------------------------------------------------------------

/**
 * Allows to check ACL permissions just about anywhere
 *
 * @param string The category name and action code to check for the logged in user.
 *               This string is joined with a period.
 * @return bool 
 */
if( ! function_exists('acl_deviation_permits') )
{
	function acl_deviation_permits( $str, $groups )
	{
		$CI =& get_instance();

		return $CI->auth_model->acl_deviation_permits( $str, $groups );
	}
}

// ------------------------------------------------------------------------

/**
 * Allows to check ACL permissions just about anywhere
 *
 * @param string The category name and action code to check for the logged in user.
 *               This string is joined with a period.
 * @return bool 
 */
if( ! function_exists('acl_object_permits') )
{
	function acl_object_permits( $str, $objects )
	{
		$CI =& get_instance();

		return $CI->auth_model->acl_object_permits( $str, $objects );
	}
}

// ------------------------------------------------------------------------

/**
 * Retrieve the true name of a database table.
 *
 * @param  string  the alias (common name) of the table
 *
 * @return  string  the true name (with CI prefix) of the table
 */
if( ! function_exists('db_table') )
{
	function db_table( $name )
	{
		$CI =& get_instance();

		$auth_model = $CI->authentication->auth_model;

		return $CI->$auth_model->db_table( $name );
	}
}

if( ! function_exists('username') )
{
	function username( $user_id )
	{
		$CI =& get_instance();
		
		if( isset($CI->users_all[$user_id]) )
		{
			return $CI->users_all[$user_id]->name;
		}
		// @STEP2: Look in DB after a Kvalprak AB user
		else
		{
			return lang('users_name');
		}
	}
}

// ------------------------------------------------------------------------

/* End of file auth_helper.php */
/* Location: /community_auth/helpers/auth_helper.php */
