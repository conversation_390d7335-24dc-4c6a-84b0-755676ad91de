<?php
defined('BASEPATH') OR exit('No direct script access allowed');

if ( ! function_exists('forms_generator'))
{
	/**
	 * Form Generator Helper
	 *
	 * Using Codeigniter Form Helper
	 *
	 * @param	string	the URL
	 * @param   string  append id
	 * @param	string	the link title
	 * @param	mixed	any attributes
	 * @return	string
	 */
	function forms_generator($type = NULL, $id = NULL, $name = NULL, $description = NULL, $options = array(), $selected = array(), $settings = NULL, $odd_even = 'even', $header = TRUE, $question = NULL, $form_name = 'form_')
	{
		// $id    = (array)  $id;
		// $title = (string) $title;
		// $ids   = array();
		
		// @STEP2: Add required
		// echo $settings;
		
		// var_dump($settings);
		
		if( ! in_array($type, ['users','department','table']) && $settings !== NULL )
		{
			$settings = json_decode($settings);
			if(json_last_error() !== JSON_ERROR_NONE)
				return NULL;
		}
		
		if( is_object($settings) && isset($settings->required) && $settings->required === 1 )
			$odd_even .= ' required';
		
		// var_dump($settings);
		
		switch($type)
		{
			case 'input':
				if($header)
					echo '<div class="form-group '.$odd_even.'">';
				$data = array(
					'name'  => $form_name . $id,
					'id'    => $form_name . $id,
					'value' => set_value($form_name . $id,$selected),
					'class' => 'form-control'
				);
				if( is_object($settings) && isset($settings->required) && $settings->required === 1 )
					$data['required'] = 'required';
				if($header)
					echo form_label($name,$form_name . $id);
				if($header && $description)
					echo '<p class="form-text">' . nl2br(html_escape($description)) . '</p>';
				echo form_input($data);
				if($header)
					echo '</div>';
			break;
			case 'date':
				if($header)
					echo '<div class="form-group date '.$odd_even.'">';
				$data = array(
					'name'  => $form_name . $id,
					'id'    => $form_name . $id,
					'value' => set_value($form_name . $id,$selected),
					'class' => 'form-control datepicker'
				);
				if( is_object($settings) && isset($settings->required) && $settings->required === 1 )
					$data['required'] = 'required';
				if($header)
					echo form_label($name,$form_name . $id);
				if($header && $description)
					echo '<p class="form-text">' . nl2br(html_escape($description)) . '</p>';
				echo form_input($data);
				if($header)
					echo '</div>';
			break;
			case 'text':
			case 'text_wysiwyg':
				if($header)
					echo '<div class="form-group '.$odd_even.'">';
				$data = array(
					'name'  => $form_name . $id,
					'id'    => $form_name . $id,
					'value' => set_value($form_name . $id,$selected,FALSE),
					'class' => 'form-control'
				);
				if( is_object($settings) && isset($settings->required) && $settings->required === 1 )
					$data['required'] = 'required';
				if($header)
					echo form_label($name,$form_name . $id);
				if($header && $description)
					echo '<p class="form-text">' . nl2br(html_escape($description)) . '</p>';
				echo form_textarea($data);
				if($header)
					echo '</div>';
			break;
			case 'radio':
				if( !empty($options) )
				{
					if($header)
						echo '<div class="form-group '.$odd_even.'">';
					if($header)
						echo form_label($name,$form_name . $id);
					if($header && $description)
						echo '<p class="form-text">' . nl2br(html_escape($description)) . '</p>';
					
					// echo strlen_array($options, 'name');
					$strlen_array = strlen_array($options, 'name');
					// echo $strlen_array;
					$group_class = $strlen_array > 40 ? 'form-control-sub' : 'form-control-sub-inline';
					$group_class = $strlen_array > 100 ? 'form-control-sub-col' : $group_class;
					
					echo '<div class="'. $group_class .'">';
					foreach($options as $option_id => $option):
					$data = array(
						'name'    => $form_name . $id,
						'class'   => $form_name . $id,
						'value'   => $option_id,
						'checked' => set_radio($form_name . $id, $option->option_id, $option_id === $selected OR $option->default)
					);
					if( is_object($settings) && isset($settings->required) && $settings->required === 1 )
						$data['required'] = 'required';
					?>
					<div class="form-check">
						<label>
							<?php echo form_radio($data) . $option->name . '&nbsp;'; ?>
						</label>
					</div>
					<?php
					endforeach;
					if($header)
						echo '</div></div>';
					else
						echo '</div>';
				}
			break;
			case 'checkbox':
				if( !empty($options) )
				{
					if($header)
						echo '<div class="form-group '.$odd_even.'">';
					if($header)
						echo form_label($name,$form_name . $id);
					if($header && $description)
						echo '<p class="form-text">' . nl2br(html_escape($description)) . '</p>';
					
					// echo strlen_array($options, 'name');
					$strlen_array = strlen_array($options, 'name');
					// echo $strlen_array;
					$group_class = $strlen_array > 40 ? 'form-control-sub' : 'form-control-sub-inline';
					$group_class = $strlen_array > 100 ? 'form-control-sub-col' : $group_class;
					
					if ($selected == null) {
						$selected = array();
					} else {
					$selected = json_decode($selected);
					if(json_last_error() !== JSON_ERROR_NONE)
					{
						$selected = array();
					}
					}
					
					if( empty($selected) )
					{
						$selected = array();
					}
					
					echo '<div class="'. $group_class .'">';
					foreach($options as $option_id => $option):
					$data = array(
						'name'    => $form_name . $id . '[]',
						'class'   => $form_name . $id,
						'value'   => $option_id,
						'checked' => set_radio($form_name . $id, $option->option_id, in_array($option_id, $selected))
					);
					if( is_object($settings) && isset($settings->required) && $settings->required === 1 )
						$data['required'] = 'required';
					?>
					<div class="form-check">
						<label>
							<?php echo form_checkbox($data) . $option->name . '&nbsp;'; ?>
						</label>
					</div>
					<?php
					endforeach;
					if($header)
						echo '</div></div>';
					else
						echo '</div>';
				}
			break;
			case 'dropdown':
				if( !empty($options) )
				{
					if($header)
						echo '<div class="form-group '.$odd_even.'">';
					if($header)
						echo form_label($name,$form_name . $id);
					if($header && $description)
						echo '<p class="form-text">' . nl2br(html_escape($description)) . '</p>';
					
					if($header)
						$dropdown = array(NULL => '-- '. $name . ' --');
					else
						$dropdown = array(NULL => NULL);
					foreach($options as $option) 
					{
						$dropdown[$option->option_id] = $option->name;
					}
					
					$data = array(
						'class' => 'form-control'
					);
					if( is_object($settings) && isset($settings->required) && $settings->required === 1 )
						$data['required'] = 'required';
					// var_dump($dropdown);
					
					echo form_dropdown($form_name . $id, $dropdown, set_value($form_name . $id,$selected), $data);
					if($header)
						echo '</div>';
				}
			break;
			case 'heading':
				echo '<div class="form-group '.$odd_even.'">';
				echo form_label($name,$form_name . $id);
				if($description)
					echo '<p class="form-text">' . nl2br(html_escape($description)) . '</p>';
				echo '</div>';
			break;
			case 'table':
				$settings = json_decode($settings);
				if(json_last_error() === JSON_ERROR_NONE)
				{
					echo '<div class="form-group form-control-table">';
					echo form_label($name,$form_name . $id);
					if($description)
						echo '<p class="form-text">' . nl2br(html_escape($description)) . '</p>';
					
					echo '<table class="table table-bordered table-striped table-hover no-data-table">';
					if( ! empty( array_filter($settings->header) ) )
					{
						echo '<thead><tr>';
						foreach($settings->header as $h)
						{
							echo '<th>' . html_escape($h) . '</th>';
						}
						echo '</tr></thead>';
					}
					if( ! empty($settings->body) && ! empty($settings->type) )
					{
						foreach($settings->body as $ii => $body)
						{
							$count = count($settings->body[$ii]);
							$prev  = NULL;
							echo '<tr>';
							foreach($body as $i => $b)
							{
								if($settings->type[$i] === 'text')
								{
									echo '<td>' . html_escape($b) . '&nbsp;</td>';
								}
								else
								{
									/*
									if( $b === '00000000-0000-0000-0000-000000000000' && $settings->type[$i-1] === $settings->type[$i])
									{
										continue;
									}

									// Legacy cols
									if( $i + 1 === $count && $count !== $settings->cols )
									{
										$colspan = $settings->cols - $count + 1;
										echo '<td colspan="'. $colspan .'">';
									}
									// New match
									else if( isset($settings->body[$ii][$i+1]) && $settings->body[$ii][$i+1] === '00000000-0000-0000-0000-000000000000' )
									{
										$colspan = $settings->cols - $count + 2;
										echo '<td colspan="'. $colspan .'">';
									}
									else
									{
										echo '<td>';
									}
									*/
									
									echo '<td>';
										
									if( isset($question[$id][$b]) )
									{
										$sub = $question[$id][$b];
										$o_sub = isset($options[$sub->question_id]) ? $options[$sub->question_id] : array();
										$s_sub = isset($selected[$sub->question_id]) ? $selected[$sub->question_id] : '';
										forms_generator($sub->field, $sub->question_id, $sub->name, $sub->description, $o_sub, $s_sub, $sub->settings, $odd_even, FALSE);
									}
									echo '</td>';
								}

								$prev = $b;
							}
							echo '</tr>';
						}
					}
					echo '</table>';
					echo '</div>';
				}
			break;
			case 'users':
				forms_generator('dropdown', $id, $name, $description, $options, $selected, $settings, $odd_even, $header, $question, $form_name);
			break;
			case 'email':
				if( !empty($options) )
				{
					$form_name = 'emails';
					
					if($header)
						echo '<div class="form-group '.$odd_even.'">';
					if($header)
						echo form_label($name,$form_name);
					if($header && $description)
						echo '<p class="form-text">' . nl2br(html_escape($description)) . '</p>';
					
					$dropdown = [];
					foreach($options as $option) 
					{
						$dropdown[$option->option_id] = $option->name;
					}
					
					$data = array(
						'class' => 'form-control selectize'
					);
					if( is_object($settings) && isset($settings->required) && $settings->required === 1 )
						$data['required'] = 'required';
					
					echo '<div id="emailAddress" class="margin">';
					if( ! empty($selected) )
					{
						foreach($selected as $user_id)
						{
							if( ! isset($dropdown[$user_id]) )
								continue;
							echo '<span class="badge badge-success">' . $dropdown[$user_id] .  '</span> ';
						}
						
					}
					echo '</div>';
					
					// var_dump($options);
					echo form_hidden('emailId',$id);
					
					// @STEP2: Remove user that already have been pre-selected.
					echo form_multiselect($form_name . '[]', $dropdown, set_value($form_name . '[]'), $data);
					
					if($header)
						echo '</div>';
				}
			break;
			case 'department':
				$id = 'department';
				$form_name = '';
				
				if( count($options) === 1 )
				{
					$option = array_pop($options);
					echo form_hidden($form_name . $id . '[]',$option->option_id);
				}
				else
				{
					forms_generator('checkbox', $id, $name, $description, $options, $selected, $settings, $odd_even, $header, $question, $form_name);					
				}
			break;
			case 'eventanalysis':
				$id = 'eventanalysis';
				$form_name = '';

				$settings->required = 0;
				$settings = json_encode($settings);
				
				if( ! empty($options) )
				{
					if ($selected) $selected = $options[$selected]->option_id;
					forms_generator('dropdown', $id, $name, $description, $options, $selected, $settings, $odd_even, $header, $question, $form_name);
				}
				else
				{
					$one = new stdClass();
					$two = new stdClass();
					$one->option_id = 0;
					$one->name = 'Nej';
					$one->default = 1;
					$two->option_id = 1;
					$two->name = 'Ja';
					$two->default = 0;
					
					$selected = 0;
					
					$options = [0 => $one, 1 => $two];
					forms_generator('radio', $id, $name, $description, $options, $selected, $settings, $odd_even, $header, $question, $form_name);
				}
				// var_dump($options);
			break;
			case 'upload':
			?>
				<div class="form-group file-upload margin">
					<?php echo form_label($name,$form_name . $id); ?>
					<p class="form-text"><?php echo nl2br(html_escape($description)); ?></p>
					<div class="cols-sm-10">
						<div class="input-group">
							<span class="input-group-prepend"><i class="fa fa-file" aria-hidden="true"></i></span>
							<div id="dropzone" class="form-control dropzone"></div>
						</div>
					</div>
				</div>
			<?php
			break;
			case 'document':
				if($header)
					echo '<div id="' . $id . '" class="form-group '.$odd_even.'">';
				$data = array(
					'name'     => $form_name . $id,
					'data-id'  => $id,
					'data-url' => base_url(),
					'class'    => 'formUpload btn btn-sm btn-success',
					'content'  => 'Hantera filer',
				);
				if($header)
					echo form_label($name,$form_name . $id);
				if($header && $description)
					echo '<p class="form-text">' . nl2br(html_escape($description)) . '</p>';
				
				echo '<div class="formUploadedFiles">';
				if( ! empty($selected) )
				{
					echo '<table class="table table-bordered no-data-table">';
					echo '<tr><th>Dokumentnamn</th><th>Skapat datum</th><th>Senast reviderad</th><th>Gäller t.o.m.</th></tr>';
					foreach($options as $document_id => $document)
					{
						if( ! in_array($document_id,$selected) )
							continue;
						
						echo '<tr>';
							echo '<td>' . safe_anchor('documents', $document_id, $document->name, ['target' => '_blank']) . '</td>';
							echo '<td>' . $document->created . '</td>';
							echo '<td>' . $document->last_revised . '</td>';
							echo '<td>' . $document->valid_until . '</td>';
						echo '</tr>';
						
					}
					echo '</table>';
				}
				echo '</div>';
				
				echo '<div>' . form_button($data) . '</div>';
				if($header)
					echo '</div>';
			break;
			default:
			break;
		}
	}
}

if ( ! function_exists('forms_search_generator'))
{
	function forms_search_generator($type = NULL, $id = NULL, $name = NULL, $description = NULL, $options = array(), $selected = array(), $settings = NULL, $odd_even = 'even', $header = TRUE, $question = NULL, $form_name = 'search_')
	{
		switch($type)
		{
			case 'input':
			case 'text':
			case 'text_wysiwyg':
				if($header)
					echo '<div class="form-group '.$odd_even.'">';
				$data = array(
					'name'  => $form_name . $type . '_' . $id,
					'id'    => $form_name . $type . '_' . $id,
					'value' => set_value($form_name . $type . '_' . $id,$selected),
					'class' => 'form-control',
					'placeholder' => $name,
				);
				if($header)
					echo form_label($name,$form_name . $type . '_' . $id);
				if($header && $description)
					echo '<p class="form-text">' . nl2br(html_escape($description)) . '</p>';
				echo form_input($data);
				if($header)
					echo '</div>';
			break;
			case 'date':
				if( empty($selected['first']) )
					$selected_first = NULL;
				else
					$selected_first = $selected['first'];
				
				if( empty($selected['last']) )
					$selected_last = NULL;
				else
					$selected_last = $selected['last'];
			
				if($header)
					echo '<div class="form-group date '.$odd_even.'">';
				$data_from = array(
					'name'  => $form_name . $type . '_first_' . $id,
					'id'    => $form_name . $type . '_first_' . $id,
					'value' => set_value($form_name . $type . '_first_' . $id,$selected_first),
					'class' => 'form-control datepicker',
					'placeholder' => lang('from'),
				);
				$data_to = array(
					'name'  => $form_name . $type . '_last_' . $id,
					'id'    => $form_name . $type . '_last_' . $id,
					'value' => set_value($form_name . $type . '_last_' . $id,$selected_last),
					'class' => 'form-control datepicker',
					'placeholder' => lang('to'),
				);
				if($header)
					echo form_label($name,$form_name . $id);
				if($header && $description)
					echo '<p class="form-text">' . nl2br(html_escape($description)) . '</p>';
				echo form_input($data_from);
				echo form_input($data_to);
				if($header)
					echo '</div>';
			break;
			case 'radio':
			case 'dropdown':
			case 'checkbox':
			case 'department':
			case 'users':
				if( !empty($options) )
				{
					if($header)
						echo '<div class="form-group '.$odd_even.'">';
					if($header)
						echo form_label($name,$form_name . $type . '_' . $id);
					if($header && $description)
						echo '<p class="form-text">' . nl2br(html_escape($description)) . '</p>';
					
					if($header)
						$dropdown = array(NULL => '-- '. $name . ' --');
					else
						$dropdown = array(NULL => NULL);
					foreach($options as $option) 
					{
						$dropdown[$option->option_id] = $option->name;
					}
					
					echo form_dropdown($form_name . $type . '_' . $id, $dropdown, set_value($form_name . $type . '_' . $id,$selected),array(
						'class' => 'form-control'
					));
					if($header)
						echo '</div>';
				}
			break;
			default:
			break;
		}
	}
}

if ( ! function_exists('forms_view'))
{
	function forms_view($type = NULL, $id = NULL, $name = NULL, $description = NULL, $options = array(), $selected = array(), $settings = NULL, $odd_even = 'even', $header = TRUE, $question = NULL)
	{
		switch($type)
		{
			case 'heading':
			case 'input':
			case 'date':
				if($header)
					echo '<div class="form-group '.$odd_even.'">';
				if($header)
					echo '<strong>' . $name . '</strong>';
				if($header && $description)
					echo '<p class="form-text">' . nl2br(html_escape($description)) . '</p>';
				echo '<p>' . html_escape($selected) . '</p>';
				if($header)
					echo '</div>';
			break;
			case 'text':
				if($header)
					echo '<div class="form-group '.$odd_even.'">';
				if($header)
					echo '<strong>' . $name . '</strong>';
				if($header && $description)
					echo '<p class="form-text">' . nl2br(html_escape($description)) . '</p>';
				echo '<p>' . nl2br(html_escape($selected));
				if($header)
					echo '</div>';
			break;
			case 'text_wysiwyg':
				if($header)
					echo '<div class="form-group '.$odd_even.'">';
				if($header)
					echo '<strong>' . $name . '</strong>';
				if($header && $description)
					echo '<p class="form-text">' . nl2br(html_escape($description)) . '</p>';
				echo $selected;
				if($header)
					echo '</div>';
			break;
			case 'dropdown':
			case 'radio':
			case 'users':
			case 'eventanalysis':
				if($header)
					echo '<div class="form-group '.$odd_even.'">';
				if($header)
					echo '<strong>' . $name . '</strong>';
				if($header && $description)
					echo '<p class="form-text">' . nl2br(html_escape($description)) . '</p>';
				echo isset($options[$selected]) ? '<p>' . html_escape($options[$selected]->name)  . '</p>': NULL;
				if($header)
					echo '</div>';
			break;
			case 'checkbox':
				if( !empty($options) && !empty($selected) )
				{
					if($header)
						echo '<div class="form-group '.$odd_even.'">';
					if($header)
						echo '<strong>' . $name . '</strong>';
					if($header && $description)
						echo '<p class="form-text">' . nl2br(html_escape($description)) . '</p>';
					
					$selected = json_decode($selected);
					if(json_last_error() !== JSON_ERROR_NONE)
					{
						$selected = array();
					}
					
					if( empty($selected) )
					{
						$selected = array();
					}
					
					foreach($selected as $option_id):
						echo '<p>';
						if( isset($options[$option_id]) )
						{
							echo $options[$option_id]->name . '<br/>';
						}
						echo '</p>';
					endforeach;
					if($header)
						echo '</div>';
				}
			break;
			case 'table':
				$settings = json_decode($settings);
				if(json_last_error() === JSON_ERROR_NONE)
				{
					echo '<div class="form-group form-control-table">';
					echo '<strong>' . $name . '</strong>';
					if($description)
						echo '<p class="form-text">' . nl2br(html_escape($description)) . '</p>';
					
					echo '<table class="table table-bordered table-striped table-hover no-data-table">';
					if( ! empty( array_filter($settings->header) ) )
					{
						echo '<thead><tr>';
						foreach($settings->header as $h)
						{
							echo '<th>' . html_escape($h) . '</th>';
						}
						echo '</tr></thead>';
					}
					if( ! empty($settings->body) && ! empty($settings->type) )
					{
						foreach($settings->body as $ii => $body)
						{
							$count = count($settings->body[$ii]);
							$prev  = NULL;
							echo '<tr>';
							foreach($body as $i => $b)
							{
								if($settings->type[$i] === 'text')
								{
									echo '<td>' . html_escape($b) . '&nbsp;</td>';
								}
								else
								{
									/*
									if( $b === '00000000-0000-0000-0000-000000000000' && $settings->type[$i-1] === $settings->type[$i])
									{
										continue;
									}

									// Legacy cols
									if( $i + 1 === $count && $count !== $settings->cols )
									{
										$colspan = $settings->cols - $count + 1;
										echo '<td colspan="'. $colspan .'">';
									}
									// New match
									else if( isset($settings->body[$ii][$i+1]) && $settings->body[$ii][$i+1] === '00000000-0000-0000-0000-000000000000' )
									{
										$colspan = $settings->cols - $count + 2;
										echo '<td colspan="'. $colspan .'">';
									}
									else
									{
										echo '<td>';
									}*/
									
									echo '<td>';
										
									if( isset($question[$id][$b]) )
									{
										$sub = $question[$id][$b];
										$o_sub = isset($options[$sub->question_id]) ? $options[$sub->question_id] : array();
										$s_sub = isset($selected[$sub->question_id]) ? $selected[$sub->question_id] : '';
										forms_view($sub->field, $sub->question_id, $sub->name, $sub->description, $o_sub, $s_sub, $sub->settings, $odd_even, FALSE);
									}
									echo '</td>';
								}

								$prev = $b;
							}
							echo '</tr>';
						}
					}
					echo '</table>';
					echo '</div>';
				}
			break;
			case 'department':
				if($header)
					echo '<div class="form-group '.$odd_even.'">';
				if($header)
					echo '<strong>' . $name . '</strong>';
				if($header && $description)
					echo '<p class="form-text">' . nl2br(html_escape($description)) . '</p>';
				if( ! empty($selected) )
				{
					echo '<p>';
					foreach($selected as $s)
					{
						echo isset($options[$s]) ? html_escape($options[$s]->name)  . '<br/>': NULL;
					}
					echo '</p>';
				}
				if($header)
					echo '</div>';
			break;
			case 'email':
				if($header)
					echo '<div class="form-group '.$odd_even.'">';
				if($header)
					echo '<strong>' . $name . '</strong>';
				if($header && $description)
				{
					echo '<p class="form-text">' . nl2br(html_escape($description)) . 
					     '<br/><strong>OBS! Mail skickas endast ut när ett ärende skapas, inte redigeras.</strong></p>';
				}
				
				echo '<div id="emailAddress" class="margin">';
				if( ! empty($selected) )
				{
					foreach($selected as $name)
					{
						echo '<span class="badge badge-success">' . $name .  '</span> ';
					}
					
				}
				echo '</div>';
				
				if($header)
					echo '</div>';
			break;
		}
		return NULL;
	}
}

if ( ! function_exists('forms_value'))
{
	function forms_value($type = NULL, $options = array(), $selected = array())
	{
		switch($type)
		{
			case 'input':
			case 'text':
			case 'text_wysiwyg':
			case 'date':
				return $selected;
			break;
			case 'dropdown':
			case 'radio':
			case 'users':
				return isset($options[$selected]) ? $options[$selected]->name : NULL;
				// var_dump($options);
			break;
		}
		return NULL;
	}
}

if ( ! function_exists('strlen_array'))
{
	function strlen_array( $array = array(), $name = NULL )
	{
		$count = 0;
		if( !isset($name) OR empty($array) )
			return $count;
		
		$first = array_values($array)[0];
		if( !is_object($first) OR !isset($first->{$name}) )
			return $count;
		
		foreach($array as $a)
		{
			$count += strlen($a->$name);
		}
		return $count;
	}
}

if ( ! function_exists('forms_navigation'))
{
	function forms_navigation( $page_id = NULL, $array = array() )
	{
		$navigation = array(
			'previous' => NULL,
			'next'     => NULL
		);
		
		if( !isset($page_id) OR empty($array) )
			return $navigation;
		
		$current = array_search($page_id,$array);
		if( $current === FALSE )
			return $navigation;
		$previous = $current - 1;
		$next     = $current + 1;
		
		$navigation['previous'] = isset($array[$previous]) ? $array[$previous] : FALSE;
		$navigation['next']     = isset($array[$next])     ? $array[$next]     : FALSE;
		
		return $navigation;
	}
}
