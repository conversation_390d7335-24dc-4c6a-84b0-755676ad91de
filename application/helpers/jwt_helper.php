<?php
defined('BASEPATH') OR exit('No direct script access allowed');

use Firebase\JWT\JWT;
use Firebase\JWT\Key;

if (!function_exists('generate_jwt_token')) {
    /**
     * Generate JWT token for API authentication
     * @param object $user User data object
     * @return string JWT token
     */
    function generate_jwt_token($user) {
        $CI =& get_instance();

        $issued_at = time();
        $expiration = $issued_at + $CI->config->item('jwt_expiration');

        $payload = [
            'iss' => base_url(),
            'aud' => base_url(),
            'iat' => $issued_at,
            'exp' => $expiration,
            'data' => [
                'user_id' => $user->user_id,
                'email' => $user->email,
                'session' => $CI->session->regenerated_session_id,
            ]
        ];

        return JWT::encode($payload, $CI->config->item('jwt_secret_key'), 'HS256');
    }
}

if (!function_exists('validate_jwt_token')) {
    /**
     * Validate JWT token
     * @param string $token JWT token
     * @return mixed Decoded token data or FALSE if invalid
     */
    function validate_jwt_token($token) {
        $CI =& get_instance();
        try {
            return JWT::decode($token, new Key($CI->config->item('jwt_secret_key'), 'HS256'));
        } catch (Exception $e) {
            log_message('error', 'JWT Validation Error: ' . $e->getMessage());
            return FALSE;
        }
    }
}
