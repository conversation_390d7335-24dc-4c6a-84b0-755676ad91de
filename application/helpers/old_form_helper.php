<?php
defined('BASEPATH') OR exit('No direct script access allowed');

function buildForm($df_id,$input,$required,$title,$description,$value=null,$disabled=null,$values=null) {
	global $wysiwyg;
	$required = ($required)?'required':''; ?>
	<div class="form-group form-control-table">
	<label for="<?= $df_id; ?>"><?= $title; ?></label>
	<p class="form-text"><?= $description; ?></p>
	<?php
	switch($input) {
		case 'input': ?>
			<input class="form-control <?= $required; ?>" type="text" name="<?= $df_id; ?>" maxlength="255" value="<?= $value?$value:''; ?>">
		<?php break;
		case 'text': ?>
			<textarea class="form-control <?= $required; ?>" rows="5" name="<?= $df_id; ?>"><?= $value?$value:''; ?></textarea>
		<?php break;
		case 'text_wysiwyg':
			$wysiwyg[] = 'wysiwyg'.$df_id;
			$required = '';?>
			<textarea class="text_wysiwyg <?= $required; ?>" name="<?= $df_id; ?>" id="wysiwyg<?= $df_id; ?>"><?= $value?$value:''; ?></textarea><br/>
		<?php break;
		case 'date': ?>
			<input class="form-control datepicker info-date <?= $required; ?>" type="text" name="<?= $df_id; ?>" value="<?= $value?$value:'';?>">
		<?php break;
		case 'checkbox': ?>
			<span class="form-control">
				<input class="<?= $required; ?>" type="radio" name="<?= $df_id; ?>" value="1" <?= $value == 1 || $value == null?'checked="checked"':''; ?>/> Nej<br/>
				<input class="<?= $required; ?>" type="radio" name="<?= $df_id; ?>" value="2" <?= $value == 2?'checked="checked"':''; ?>/> Ja
			</span>
		<?php break;
		case 'users':
			if($disabled) { ?>
			<input type="hidden" name="<?= $df_id; ?>" value="<?= $value; ?>">
			<p><?= username($value); ?></p>
			<?php } else { ?>
			<select class="form-control" name="<?= $df_id; ?>">
				<option value="">-- <?= $title; ?> --</option>
				<?php if(!empty($values)) { ?>
					<?php foreach ($values AS $key => $val): 
					$selected = ($key==$value)?' selected':''; ?>
					<option value="<?= $key; ?>"<?= $selected; ?>><?= $val->name; ?></option>
					<?php endforeach; ?>
				<?php } ?>
			</select>
			<?php } ?>
		<?php break;
		case 'department':
			if (count($values) >= 2) {
				foreach ($values AS $key => $val) {
					$checked = '';
					if( !empty($value) && in_array($key,$value) ) {
						$checked = 'checked="checked"';
					}
					?>
					<div class="form-check">
						<label>
							<input type="checkbox" name="department[]" class="required" value="<?= $key; ?>" <?= $checked; ?> />
							<?= $val; ?> 
						</label>
					</div>
					<?php
				}
			} elseif(count($values) == 1) {
				foreach ($values AS $key => $val) {
					echo '<input type="checkbox" name="department[]" value="'.$key.'" checked="checked" disabled /> ';
					echo '<input type="hidden" name="department[]" value="'.$key.'" /> ';
					echo $val.'<br>';
				}
			} ?>
		<?php break;
	} ?>
	</div>
	<?php
}

function viewForm($df_id,$input,$required,$title,$description,$value=null,$values=null) { ?>
	
	<dt><?= $title; ?></dt><dd><p class="form-text"><?= $description; ?></p>
	<?php
	switch($input) {
		case 'input':
		case 'date': ?>
			<?= $value?$value:''; ?>
		<?php break;
		case 'text': ?>
			<?= $value?nl2br($value):''; ?>
		<?php break;
		case 'text_wysiwyg':
			echo $value?$value:''; ?>
		<?php break;
		case 'users': ?>
			<p><?= username($value); ?></p>
		<?php break;
		case 'department':
			echo '<p>';
			foreach ($values AS $key => $val) {
				if( !empty($value) && in_array($key,$value) ) {
					echo $val.'<br/>';
				}
			}
			echo '</p>';
		break;
	}?>
	</dd>
	<?php
}

function buildInputs($settings) {
	if(isset($settings['nameAppend']) && preg_match('/\[\]$/',$settings['name'])) {
		$settings['name'] = substr($settings['name'], 0, -2).$settings['nameAppend'].'[]';
	} elseif(isset($settings['nameAppend'])) {
		$settings['name'] .= $settings['nameAppend'];
	}
	
	if( !isset($settings['append']) )
		$settings['append'] = '';
	
	if( !isset($settings['prepend']) )
		$settings['prepend'] = '';
	
	if( !isset($settings['value']) )
		$settings['value'] = '';
	
	if( !isset($settings['class']) )
		$settings['class'] = '';
	
	// $settings['class'] .= ' form-control';
	if( $settings['type'] !== 'hidden') { ?>
	<div class="form-group form-control-table">
	<?php }
	
	switch($settings['type']) {
		case 'hidden': ?>
			<input type="hidden" name="<?= $settings['name']; ?>" value="<?= $settings['value']; ?>" />
		<?php break;
		case 'input': ?>
			<?= $settings['prepend']; ?><input name="<?= $settings['name']; ?>" class="form-control <?= $settings['class']; ?>" placeholder="<?= $settings['placeholder']; ?>" title="<?= $settings['title']; ?>" value="<?= $settings['value']; ?>"><?= $settings['append']; ?>
		<?php break;
		case 'users': ?>
			<?= $settings['prepend']; ?><select name="<?= $settings['name']; ?>" class="form-control <?= $settings['class']; ?>">
				<option value="">-- <?= $settings['title']; ?> --</option>
				<?php foreach($settings['values'] as $key => $val):
				$selected = ($key==$settings['value'])?' selected':''; ?>
				<option value="<?= $key; ?>"<?= $selected; ?>><?= $val->name; ?></option>
				<?php endforeach; ?>
			</select><?= $settings['append']; ?>
		<?php break;
		case 'select': ?>
			<?= $settings['prepend']; ?><select name="<?= $settings['name']; ?>" class="form-control <?= $settings['class']; ?>">
				<?php foreach($settings['values'] as $key => $val):
				$selected = ($key==$settings['value'])?' selected':''; ?>
				<option value="<?= $key; ?>"<?= $selected; ?>><?= $val; ?></option>
				<?php endforeach; ?>
			</select><?= $settings['append']; ?>
		<?php break;
		case 'select-multiple': ?>
			<?= $settings['prepend']; ?><select id="<?= $settings['id']; ?>" name="<?= $settings['name']; ?>" class="form-control <?= $settings['class']; ?>" multiple placeholder="<?= $settings['placeholder']; ?>">
				<?php foreach($settings['values'] as $key => $val):
				$selected = (in_array($key,$settings['multiple-value']))?' selected':''; ?>
				<option value="<?= $key; ?>"<?= $selected; ?>><?= $val; ?></option>
				<?php endforeach; ?>
			</select><?= $settings['append']; ?>
		<?php break;
		case 'textarea': ?>
			<?= $settings['prepend']; ?><textarea name="<?= $settings['name']; ?>" class="form-control <?= $settings['class']; ?>" rows="5" placeholder="<?= $settings['placeholder']; ?>"><?= $settings['value']; ?></textarea><?= $settings['append']; ?>
		<?php break;
		case 'checkbox': 
			$selected = ($settings['values']==$settings['value'])?' checked':'';
			echo $settings['prepend']; ?>
			<div class="form-check">
				<label>
					<input type="checkbox" name="<?= $settings['name']; ?>" class="<?= $settings['class']; ?> fixPostEmpty" value="<?= $settings['values']; ?>" <?= $selected; ?> />
					<?= $settings['title']; ?> 
				</label>
			</div>
			<?= $settings['append']; ?>
		<?php break;
		case 'radio': ?>
			<?= $settings['prepend']; ?>
				<?php foreach($settings['values'] as $key => $val):
					$selected = ($key==$settings['value'])?' checked':''; ?>
					<input type="radio" name="<?= $settings['name']; ?>" class="<?= $settings['class']; ?>" value="<?= $key; ?>" <?= $selected; ?> /> <?= $val; ?>
				<?php endforeach; ?>
			<?= $settings['append']; ?>
		<?php
	}
	if( $settings['type'] !== 'hidden') { ?>
	</div>
	<?php }
}

function viewInputs($settings) {
	$settings['title'] = $settings['title']?'<b>' . $settings['title'] . '</b>: ':$settings['title'];
	switch($settings['type']) {
		case 'input':
			echo $settings['prepend'] . $settings['title'] . $settings['value'] . $settings['append'];
		break;
		case 'users':
			echo $settings['prepend'] . $settings['title'];
			echo username($settings['value']);
			echo $settings['append'];
		break;
		case 'select':
			echo $settings['prepend'] . $settings['title'];
				foreach($settings['values'] as $key => $val):
					if($key==$settings['value'])
						echo $val;
				endforeach;
			echo $settings['append'];
		break;
		case 'textarea':
			echo $settings['prepend'] . nl2br($settings['value']) . $settings['append'];
		break;
		case 'checkbox':
			$value = $settings['value']?'Ja':'Nej';
			echo '<p><b>' . $settings['title'] . '</b>' . $value . '</p>';
		break;
	}
}