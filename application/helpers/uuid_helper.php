<?php
defined('BASEPATH') OR exit('No direct script access allowed');

if ( ! function_exists('UUID_TO_BIN'))
{
	function UUID_TO_BIN($uuid) {
		if( empty($uuid) ) return;
		$uuid = hex2bin(str_replace('-','',$uuid));
		return $uuid;
	}
}

if ( ! function_exists('BIN_TO_UUID'))
{
	function BIN_TO_UUID($uuid) {
		if( empty($uuid) ) return;
		$uuid = bin2hex($uuid);
		$uuid = substr($uuid, 0, 8) . '-' . substr($uuid, 8, 4) . '-' . substr($uuid, 12, 4) . '-' . substr($uuid, 16, 4)  . '-' . substr($uuid, 20);
		return $uuid;
	}
}


if ( ! function_exists('UUIDv4'))
{
	/**
	 * Generates a random UUID using the secure RNG.
	 *
	 * Returns Version 4 UUID format: xxxxxxxx-xxxx-4xxx-Yxxx-xxxxxxxxxxxx where x is
	 * any random hex digit and Y is a random choice from 8, 9, a, or b.
	 *
	 * @return string the UUID
	 */
	function UUIDv4()
	{
		$bytes = random_bytes(16);
		$bytes[6] = chr((ord($bytes[6]) & 0x0f) | 0x40);
		$bytes[8] = chr((ord($bytes[8]) & 0x3f) | 0x80);
		$uuid = vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($bytes), 4));
		return $uuid;
		// $id = str_split(bin2hex($bytes), 4);
		// return "{$id[0]}{$id[1]}-{$id[2]}-{$id[3]}-{$id[4]}-{$id[5]}{$id[6]}{$id[7]}";
	}
}

if ( ! function_exists('VALID_UUIDv4'))
{
	function VALID_UUIDv4($uuid)
	{
		if (!isset($uuid)) return false;
		return (bool) preg_match('/^[0-9A-F]{8}-[0-9A-F]{4}-4[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i',$uuid);
	}
}
