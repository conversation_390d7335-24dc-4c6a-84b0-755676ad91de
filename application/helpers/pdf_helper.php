<?php
defined('BASEPATH') OR exit('No direct script access allowed');

if ( ! function_exists('pdf'))
{
	function pdf(object $attachment, string $filepath)
	{
		if ( ! @is_file($filepath) OR ($filesize = @filesize($filepath)) === FALSE)
		{
			return;
		}

		// Generate the server headers
		header('Content-Type: application/pdf');
		header('Content-Disposition: inline; filename="'.$attachment->file_name.'"');
		header('Content-Transfer-Encoding: binary');
		header('Content-Length: '.$filesize);
		header('Accept-Ranges: bytes');

		if (($fp = @fopen($filepath, 'rb')) === FALSE)
		{
			return;
		}

		// Clean output buffer
		if (ob_get_level() !== 0 && @ob_end_clean() === FALSE)
		{
			@ob_clean();
		}

		// Flush 1MB chunks of data
		while ( ! feof($fp) && ($data = fread($fp, 1048576)) !== FALSE)
		{
			echo $data;
		}

		fclose($fp);
		exit;
	}
}