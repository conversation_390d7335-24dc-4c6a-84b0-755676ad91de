<script type="text/javascript">
	function submitForm(e) {
		$('#submit-btn').prop('disabled', true); 
		$('#submit-btn').html('Loading...');
		document.getElementById('form-orna-analys').submit();
}
</script>
<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<h1>
				<?php echo lang('documents_document'); ?>
				<small></small>
			</h1>
			<div class="float-right">
				<a href="/docs/orna_analys_manual" target="_blank" class="btn btn-primary btn-block"><?php echo lang('orna_analys_manual'); ?></a>
			</div>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<?php if ($limit): ?>
						<div class="alert alert-error"> You can only make data available to one orna analys account </div>
					<?php endif; ?>
					<?php if ($success): ?>
						<div class="alert alert-success"> Ditt material är nu tillgängligt i Orna analys för att analyseras.
							Gå till följande adress <a href="https://orna-analys.kvalprak.se">https://orna-analys.kvalprak.se</a> </div>
					<?php endif; ?>
					<p>
					För att använda Orna analys första gången,
					</p>
					<ol>
						<li>Registrera ett konto på <a href="https://orna-analys.kvalprak.se">https://orna-analys.kvalprak.se</a></li>
						<li>Skriv in e-mailen som du använde för att registrera kontot på Orna analys</li>
						<li>Klicka på "analys", så tillgängliggörs dokumenten för analys</li>
					</ol>
					Orna analys
					När du loggar in på Orna analys så ser du ditt ledningssystem i listan med projekt.
					Klicka på ditt projekt och börja analysera ditt ledningssystem.
					</p>
					<p>					
					Det finns 3 olika analysmetoder
					</p>
					<ol>
						<li>Gör en analys av ledningssystemet för en patientsäkerhetsberättelse</li>
						<li>Gör en analys av ledningssystemet för följsamhet av KVO11:9</li>
						<li>Fritextsökning</li>
					</ol>
					<p>
					Fritextsökningen kan kombineras med de två första valen
					</p>
					<p>
						Observera att analysen kan ta 10 - 15 minuter beroende på hur många frågor du ställer. Svaren uppdateras efterhand och presenteras när dom är färdiga. Ladda om sidan om du är osäker på om alla frågor är besvarade.
					</p>
					<?php
						echo form_open(NULL,array(
							'id' => 'form-orna-analys',
							'autocomplete' => 'off'
						));
					?>

						<div class="box-header with-border">
							<?php echo form_label('Email','email'); ?>
							<?php
							echo form_input(array(
									'name'	=> 'email',
									'class' => 'form-control',
									'placeholder' => 'email'
								));
							?>
						</div>
						<?php
				echo form_button(array(
					'type' => 'submit',
					'form' => 'form-orna-analys',
					'class' => 'btn btn-primary',
					'id' => 'submit-btn',
					'onClick' => "submitForm()",
					'content' => 'Submit'
				));
				?>
					<?php echo form_close(); ?>
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');