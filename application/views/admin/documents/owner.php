<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<h1>
				<?php echo lang('documents_owner'); ?>
				<small></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="alert alert-danger"><?php echo lang('documents_change_document_owner_desc'); ?></div>
					<div class="alert alert-warning"><?php echo lang('documents_change_document_owner_warning'); ?></div>
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('documents_owner'); ?></h3>
						</div>
						<div class="box-body">
							<?php if( ! empty($to) && ! empty($from) ): ?>
								<?php 
									echo validation_errors();
									echo form_open(NULL,array(
										'id' => 'form-company-group',
										'autocomplete' => 'off'
									));
								?>
								<div class="form-group">
								<?php
									echo form_label(lang('from'),'documents_owner_from');
									echo form_dropdown('documents_owner_from', $from, set_value('documents_owner_from'),array(
										'class' => 'form-control'
									));
								?>
								</div>
								<div class="form-group">
								<?php
									echo form_label(lang('to'),'documents_owner_to');
									echo form_dropdown('documents_owner_to', $to, set_value('documents_owner_to'),array(
										'class' => 'form-control'
									));
								?>
								</div>
								<?php echo form_label(lang('confirm_change'),'confirm_change'); ?>
								<div class="form-group">
									<div class="form-check">
										<label>
											<?php echo form_checkbox('confirm_change', 1) . lang('confirm_change_help'); ?>
										</label>
									</div>
								</div>
								<?php
								echo form_button(array(
									'type' => 'submit',
									'form' => 'form-company-group',
									'class' => 'btn btn-danger',
									'title' => lang('save'),
									'content' => lang('documents_change_document_owner')
								));
								?>
								<?php echo form_close(); ?>
							<?php else: ?>
								<?php echo lang('documents_empty'); ?>
							<?php endif; ?>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /.box -->
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');