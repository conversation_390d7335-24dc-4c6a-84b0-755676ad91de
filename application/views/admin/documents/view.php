<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<h1>
				<?php echo lang('documents_document'); ?>
				<small></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="nav-tabs-custom">
						<ul class="nav nav-tabs">
							<li><a href="#document" data-toggle="tab" aria-expanded="true" class="active"><?php echo lang('documents_document'); ?></a></li>
							<!-- <li><a href="#autosave" data-toggle="tab" aria-expanded="false"><?php echo lang('documents_autosave'); ?></a></li>
							<li><a href="#empty" data-toggle="tab" aria-expanded="false"><?php echo lang('documents_empty_head'); ?></a></li> -->
						</ul>
						<div class="tab-content">
							<div class="tab-pane active" id="document">
								<p class=""><?php echo lang('documents_all_documents'); ?></p>
								<?php //var_dump($move['document']); ?>
								<?php //var_dump($move['folder']); ?>
								<div class="document-tree">
									<ul>
										<?php
										foreach($move['menu'][0] as $menu_id => $menu)
										{
											if( ! in_array($menu_id, $move['menu']['loop']) )
												continue;
											echo '<li><span>' . html_escape($menu->name) . '</span><ul>';
											// var_dump($menu_id);exit;
											if( isset($move['menu'][$menu_id]) )
											{
												foreach($move['menu'][$menu_id] as $parent)
												{
													if( ! in_array($parent->menu_id, $move['menu']['loop']) )
														continue;
													$parent->name = preg_replace('/^[0-9\.\s]+/u', '',$parent->name);
													// In case it's a folder
													if($parent->type == 1)
													{
														if(	isset($move['menu'][$parent->menu_id]) )
														{
															echo '<li><span><strong>' . html_escape($parent->name) . '</strong></span><ul>';
															// Echo $parent
															foreach($move['menu'][$parent->menu_id] as $child)
															{
																if( isset($move['folder'][$child->menu_id]) )
																{
																	// echo '<li>' . html_escape($child->name) . '<ul>';

																	$print_folder = TRUE;
																	foreach($move['folder'][$child->menu_id] as $child_folder)
																	{
																		if( isset($move['document'][$child_folder->folder_id]) )
																		{
																			if($print_folder)
																			{
																				echo '<li><span><i>' . html_escape($child->name) . '</i></span><ul>';
																			}
																			
																			echo '<li><span>' . html_escape($child_folder->name) .'</span><ul>';
																			foreach($move['document'][$child_folder->folder_id] as $document)
																			{
																				echo '<li>' . safe_anchor('documents', $document->document_id, $document->name, ['target' => '_blank']) . '</li>';
																			}
																			echo '</ul></li>';
																			
																			if($print_folder)
																			{
																				echo '</ul></li>';
																				$print_folder = FALSE;
																			}
																		}
																	}

																	// echo '</ul></li>';
																}
															}
															echo '</ul></li>';
															// Close $parent
														}
														
													}
													else
													{
														if( isset($move['folder'][$parent->menu_id]) )
														{
															echo '<li><span><strong>' . html_escape($parent->name) . '</strong></span><ul>';
															foreach($move['folder'][$parent->menu_id] as $parent_folder)
															{
																if( isset($move['document'][$parent_folder->folder_id]) )
																{
																	echo '<li><span>' . html_escape($parent_folder->name) .'</span><ul>';
																	foreach($move['document'][$parent_folder->folder_id] as $document)
																	{
																		echo '<li>' . safe_anchor('documents', $document->document_id, $document->name, ['target' => '_blank']) . '</li>';
																	}
																	echo '</ul></li>';
																}
															}
															echo '</ul></li>';
														}
														// Echo $parent, that's not a folder
													}
												}
											}
											echo '</ul></li>';
										}
										?>
									</ul>
								</div>
								<!-- /.document-tree -->
							</div>
							<!-- /#document -->
							<div class="tab-pane active" id="autosave">
							</div>
							<div class="tab-pane active" id="empty">
							</div>
						</div>
						<!-- /.tab-content -->
					</div>
					<!-- /.nav-tabs-custom -->
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');