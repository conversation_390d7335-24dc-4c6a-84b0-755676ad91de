<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<h1>
				<?php echo lang('documents_move_copy'); ?>
				<small></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="alert alert-danger"><?php echo lang('documents_move_danger'); ?></div>
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('documents_move_copy'); ?></h3>
						</div>
						<div class="box-body">
							<p><?php echo lang('groups_group_desc'); ?></p>
							<?php 
								echo validation_errors();
								echo form_open(NULL,array(
									'id' => 'form-company-group',
									'autocomplete' => 'off'
								));
							?>
							<?php echo form_label(lang('documents_document'),'tree_documents'); ?>
							<div class="document-tree">
								<ul>
									<?php
									foreach($move['menu'][0] as $menu_id => $menu)
									{
										if( ! in_array($menu_id, $move['menu']['loop']) )
											continue;
										echo '<li><span>' . html_escape($menu->name) . '</span><ul>';
										// var_dump($menu_id);exit;
										if( isset($move['menu'][$menu_id]) )
										{
											foreach($move['menu'][$menu_id] as $parent)
											{
												if( ! in_array($parent->menu_id, $move['menu']['loop']) )
													continue;
												$parent->name = preg_replace('/^[0-9\.\s]+/u', '',$parent->name);
												// In case it's a folder
												if($parent->type == 1)
												{
													if(	isset($move['menu'][$parent->menu_id]) )
													{
														echo '<li><span><input type="checkbox" class="markAll"/> <strong>' . html_escape($parent->name) . '</strong></span><ul>';
														// Echo $parent
														foreach($move['menu'][$parent->menu_id] as $child)
														{
															if( isset($move['folder'][$child->menu_id]) )
															{
																// echo '<li>' . html_escape($child->name) . '<ul>';
																$print_folder = TRUE;
																foreach($move['folder'][$child->menu_id] as $child_folder)
																{
																	if( isset($move['document'][$child_folder->folder_id]) )
																	{
																		if($print_folder)
																		{
																			echo '<li><span><input type="checkbox" class="markAll"/><i> ' . html_escape($child->name) . '</i></span><ul>';
																		}
																		
																		echo '<li><span><input type="checkbox" class="markAll"/> ' . html_escape($child_folder->name) .'</span><ul>';
																		foreach($move['document'][$child_folder->folder_id] as $document)
																		{
																			echo '
																			<li>
																				<label>
																					' . form_checkbox('tree_documents[]', $document->document_id . '_' . $document->last_revised, set_checkbox('tree_documents', $document->document_id . '_' . $document->last_revised)) . $document->name . '
																				</label>
																			</li>
																			';
																		}
																		echo '</ul></li>';
																		
																		if($print_folder)
																		{
																			echo '</ul></li>';
																			$print_folder = FALSE;
																		}
																	}
																}

																// echo '</ul>';
															}
														}
														echo '</ul></li>';
														// Close $parent
													}
													
												}
												else
												{
													if( isset($move['folder'][$parent->menu_id]) )
													{
														echo '<li><span><input type="checkbox" class="markAll"/> <strong>' . html_escape($parent->name) . '</strong></span><ul>';
														foreach($move['folder'][$parent->menu_id] as $parent_folder)
														{
															if( isset($move['document'][$parent_folder->folder_id]) )
															{
																echo '<li><span><input type="checkbox" class="markAll"/> ' . html_escape($parent_folder->name) .'</span><ul>';
																foreach($move['document'][$parent_folder->folder_id] as $document)
																{
																	echo '
																	<li>
																		<label>
																			' . form_checkbox('tree_documents[]', $document->document_id . '_' . $document->last_revised, set_checkbox('tree_documents', $document->document_id . '_' . $document->last_revised)) . $document->name . '
																		</label>
																	</li>
																	';
																}
																echo '</ul></li>';
															}
														}
														echo '</ul></li>';
													}
													// Echo $parent, that's not a folder
												}
											}
										}
										echo '</ul></li>';
									}
									?>
								</ul>
							</div>
							<hr/>
							<div class="form-group">
								<?php echo form_label(lang('folder_folders'),'move_document'); ?>
								<ul class="move list-unstyled">
									<?php
									foreach($move['menu'][0] as $menu_id => $menu)
									{
										// if( ! in_array($menu_id, $move['menu']['loop']) )
											// continue;
										echo '<li><h4>' . html_escape($menu->name) . '</h4><ul>';
										if( isset($move['menu'][$menu_id]) )
										{
											foreach($move['menu'][$menu_id] as $parent)
											{
												// if( ! in_array($parent->menu_id, $move['menu']['loop']) )
													// continue;
												$parent->name = preg_replace('/^[0-9\.\s]+/u', '',$parent->name);
												// In case it's a folder
												if($parent->type == 1)
												{
													if(	isset($move['menu'][$parent->menu_id]) )
													{
														echo '<li><strong>' . html_escape($parent->name) . '</strong><br/>';
														// Echo $parent
														foreach($move['menu'][$parent->menu_id] as $child)
														{
															if( isset($move['folder'][$child->menu_id]) )
															{
																echo ' - <i>' . html_escape($child->name) . '</i><ul>';
																foreach($move['folder'][$child->menu_id] as $child_folder)
																{
																	echo '
																	<li>
																		<div class="form-check">
																			<label>
																				' . form_radio('move_document', $child_folder->folder_id, set_radio('move_document', $child_folder->folder_id)) . $child_folder->name . '
																			</label>
																		</div>
																	</li>
																	';
																}
																echo '</ul>';
															}
														}
														echo '</li>';
														// Close $parent
													}
													
												}
												else
												{
													if( isset($move['folder'][$parent->menu_id]) )
													{
														echo '<li><strong>' . html_escape($parent->name) . '</strong><ul>';
														foreach($move['folder'][$parent->menu_id] as $parent_folder)
														{
															echo '
															<li>
																<div class="form-check">
																	<label>
																		' . form_radio('move_document', $parent_folder->folder_id, set_radio('move_document', $parent_folder->folder_id)) . $parent_folder->name . '
																	</label>
																</div>
															</li>
															';
														}
														echo '</ul></li>';
													}
													// Echo $parent, that's not a folder
												}
											}
										}
										echo '</ul></li>';
									}
									?>
								</ul>
							</div>
							<hr/>
							<?php echo form_label(lang('confirm_change'),'confirm_change'); ?>
							<div class="form-group">
								<div class="form-check">
									<label>
										<?php echo form_checkbox('confirm_change', 1) . lang('confirm_change_help'); ?>
									</label>
								</div>
							</div>

							<div class="form-group">
								<div class="form-check">
									<label>
										<?php echo form_checkbox('copy_only', 1) . lang('copy_data_help'); ?>
									</label>
								</div>
							</div>
							<?php
							echo form_button(array(
								'type' => 'submit',
								'form' => 'form-company-group',
								'class' => 'btn btn-danger',
								'title' => lang('save'),
								'content' => lang('documents_move_copy')
							));
							?>
							<?php echo form_close(); ?>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /.box -->
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');