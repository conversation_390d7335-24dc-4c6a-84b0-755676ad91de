<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php
				echo anchor('admin/documents/categories/create', '<i class="fa fa-plus" aria-hidden="true"></i>',
					array(
					'title' => lang('add') . ' ' . lang('posts_category'),
					'class' => 'btn btn-primary',
					)
				);
				?>
			</div>
			<h1>
				<?php echo lang('documents_document'); ?>
				<small><?php echo lang('posts_categories'); ?></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('posts_categories'); ?></h3>
						</div>
						<div class="box-body no-padding">
							<?php // var_dump($category); ?>
							<?php if( ! empty($categories) ): ?>
							<table class="table table-striped no-data-table">
								<colspan>
									<col>
									<col width="50">
								</colspan>
								<tbody>
								<?php foreach($categories as $category_id => $name): ?>
									<tr>
										<td><?php echo safe_anchor('admin/documents/categories/update', $category_id, $name); ?></td>
										<td>
											<?php if( ! in_array($category_id, $categories_in_use) ): ?>
												<div class="btn-group btn-group-sm">
													<?php
													echo icon_anchor('admin/documents/categories/delete', $category_id ,'<i class="fa fa-trash" aria-hidden="true"></i>',
														array(
														'title' => lang('delete'),
														'class' => 'btn btn-danger',
														)
													);
													?>
												</div>
											<?php endif; ?>
										</td>
									</tr>
								<?php endforeach; ?>
								</tbody>
							</table>
							<?php else: ?>
								<p class="margin"><?php echo lang('posts_categories_empty'); ?></p>
							<?php endif; ?>
						</div>
					</div>
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');