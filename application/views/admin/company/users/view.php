<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php
				echo icon_anchor('admin/companies/users/create', $company->company_id, '<i class="fa fa-plus" aria-hidden="true"></i>',
					array(
					'title' => lang('add') . ' ' . lang('users_user'),
					'class' => 'btn btn-primary',
					)
				);
				echo anchor('admin/companies/company', '<i class="fa fa-reply" aria-hidden="true"></i>',
					array(
					'title' => lang('cancel'),
					'class' => 'btn btn-default'
					)
				);
				?>
			</div>
			<h1>
				<?php echo lang('users_users'); ?>
				<small><?php echo html_escape($company->name) . ' (' . html_escape($company->alias) . ')'; ?></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('users_users'); ?></h3>
						</div>
						<div class="box-body no-padding">
								<?php
								if( empty($users) )
								{
									lang('users_empty');
								}
								else
								{
								?>
								<table class="table table-striped no-data-table">
									<tbody>
									<?php foreach($users as $user): ?>
										<tr>
											<td><?php echo safe_anchor('admin/companies/users/update', [$company_id, $user->user_id], $user->name); ?></td>
										</tr>
									<?php endforeach; ?>
									</tbody>
								</table>
								<?php
								}
								?>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /.box -->
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');