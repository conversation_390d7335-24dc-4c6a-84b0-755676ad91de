<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php
				echo form_button(array(
					'type' => 'submit',
					'form' => 'form-company-group',
					'class' => 'btn btn-primary',
					'title' => lang('save'),
					'content' => '<i class="fa fa-save"></i>'
				));
				echo icon_anchor('admin/companies/users/view', $company_id, '<i class="fa fa-reply" aria-hidden="true"></i>',
					array(
					'title' => lang('cancel'),
					'class' => 'btn btn-default'
					)
				);
				?>
			</div>
			<h1>
				<?php echo lang('users_users'); ?>
				<small><?php echo html_escape($company->name) . ' (' . html_escape($company->alias) . ')'; ?></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<?php 
						echo validation_errors();
						echo form_open(NULL,array(
							'id' => 'form-company-group',
							'autocomplete' => 'off'
						));
					?>
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('users_user_details'); ?></h3>
						</div>
						<div class="box-body">
							<p><?php echo lang('users_user_desc'); ?></p>
							<div class="form-group">
							<?php
								echo form_label(lang('users_name'),'users_name');
								echo form_input(array(
										'name'	=> 'users_name',
										'value'	=> set_value('users_name'),
										'class' => 'form-control'
									));
							?>
							</div>
							<div class="form-group">
							<?php
								echo form_label(lang('users_position'),'users_position');
								echo form_input(array(
										'name'	=> 'users_position',
										'value'	=> set_value('users_position'),
										'class' => 'form-control'
									));
							?>
							</div>
							<div class="form-group">
							<?php
								echo form_label(lang('users_HSAID'),'users_HSAID');
								echo form_input(array(
										'name'	=> 'users_HSAID',
										'value'	=> set_value('users_HSAID'),
										'class' => 'form-control'
									));
							?>
							</div>
							<div class="form-group">
							<?php
								echo form_label(lang('users_phone'),'users_phone');
								echo form_input(array(
										'name'	=> 'users_phone',
										'value'	=> set_value('users_phone'),
										'class' => 'form-control'
									));
							?>
							</div>
							<div class="form-group">
							<?php
								echo form_label(lang('users_mobile'),'users_mobile');
								echo form_input(array(
										'name'	=> 'users_mobile',
										'value'	=> set_value('users_mobile'),
										'class' => 'form-control'
									));
							?>
							</div>
							<div class="form-group">
								<?php echo form_label(lang('users_email'),'users_email'); ?>
								<p class="form-text"><?php echo nl2br(lang('users_email_desc')); ?></p>
								<?php
								echo form_input(array(
										'name'	=> 'users_email',
										'value'	=> set_value('users_email'),
										'class' => 'form-control'
									));
								?>
							</div>
							<div class="form-group">
								<?php echo form_label(lang('users_password'),'users_password'); ?>
								<p class="form-text"><?php echo nl2br(lang('users_password_desc')); ?></p>
							</div>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /.box -->
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('users_private_contact_details'); ?></h3>
						</div>
						<div class="box-body">
							<div class="form-group">
							<?php
								echo form_label(lang('users_address'),'users_address');
								echo form_input(array(
										'name'	=> 'users_address',
										'value'	=> set_value('users_address'),
										'class' => 'form-control'
									));
							?>
							</div>
							<div class="form-group">
							<?php
								echo form_label(lang('users_zip'),'users_zip');
								echo form_input(array(
										'name'	=> 'users_zip',
										'value'	=> set_value('users_zip'),
										'class' => 'form-control'
									));
							?>
							</div>
							<div class="form-group">
							<?php
								echo form_label(lang('users_city'),'users_city');
								echo form_input(array(
										'name'	=> 'users_city',
										'value'	=> set_value('users_city'),
										'class' => 'form-control'
									));
							?>
							</div>
							<div class="form-group">
							<?php
								echo form_label(lang('users_phone_private'),'users_phone_private');
								echo form_input(array(
										'name'	=> 'users_phone_private',
										'value'	=> set_value('users_phone_private'),
										'class' => 'form-control'
									));
							?>
							</div>
							<div class="form-group">
							<?php
								echo form_label(lang('users_mobile_private'),'users_mobile_private');
								echo form_input(array(
										'name'	=> 'users_mobile_private',
										'value'	=> set_value('users_mobile_private'),
										'class' => 'form-control'
									));
							?>
							</div>
							<div class="form-group">
							<?php
								echo form_label(lang('users_email_private'),'users_email_private');
								echo form_input(array(
										'name'	=> 'users_email_private',
										'value'	=> set_value('users_email_private'),
										'class' => 'form-control'
									));
							?>
							</div>
						</div>
					</div>
					<?php
						
					echo form_close();
					?>
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');