<?php $this->load->view('template/header'); ?>
   <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header margin-bottom">
	  <div class="btn-group float-right">
		<?php
		echo anchor('admin/companies/company/create', '<i class="fa fa-plus" aria-hidden="true"></i>',
			array(
			'title' => lang('company_add_company'),
			'class' => 'btn btn-primary',
			)
		);
		?>
	  </div>
      <h1>
        <?php echo lang('company_companies'); ?>
        <small></small>
      </h1>
    </section>
    <!-- Main content -->
    <section class="content">
	<div class="row">
	<div class="col-md-12">
		<div class="box">
		<div class="box-header">
			<div class="btn-group float-right">
				<?php echo anchor('admin/companies/company/export/pdf/all', lang('all'), ['target' => '_blank', 'class' => 'btn btn-primary']); ?>
				<?php echo anchor('admin/companies/company/export/pdf/flex', 'Flex', ['target' => '_blank', 'class' => 'btn btn-default']); ?>
				<?php echo anchor('admin/companies/company/export/pdf/web', 'Web', ['target' => '_blank', 'class' => 'btn btn-default']); ?>
				<?php echo anchor('admin/companies/company/export/pdf/manuell', 'Manuell', ['target' => '_blank', 'class' => 'btn btn-default']); ?>
			</div>
		</div>
		<div class="box-body table-responsive">
		<table class="table table-bordered table-striped table-hover no-data-table">
			<thead>
				<tr role="row">
					<th><?php echo lang('users_name'); ?></th>
					<th><?php echo lang('company_name'); ?></th>
					<th></th>
					<th><?php echo lang('company_alias'); ?></th>
					<th><?php echo lang('memberships_membership'); ?></th>
					<th><?php echo lang('company_licenses'); ?></th>
					<th><?php echo lang('company_city'); ?></th>
					<th><?php echo lang('company_phone'); ?></th>
					<th><?php echo lang('company_email'); ?></th>
				</tr>
			</thead>
			<tbody>
				<?php foreach($companies as $company): ?>
					<tr>
						<td><?php echo isset($users[$company->company_id]) ? current($users[$company->company_id]) : NULL; ?></td>
						<td><?php echo safe_anchor('admin/companies/company/update', $company->company_id, $company->name); ?></td>
						<td width="65">
							<div class="btn-group btn-group-sm">
								<?php echo icon_anchor('admin/companies/users/view', $company->company_id, '<i class="fa fa-users" aria-hidden="true"></i>',[
									'class' => 'btn btn-default',
								]); ?>
							</div>
						</td>
						<td><?php echo $company->alias; ?></td>
						<td><?php echo isset($memberships[$company->membership_id]) ? ltrim($memberships[$company->membership_id]->name, 'Kvalprak') : NULL; ?></td>
						<td><?php echo isset($users[$company->company_id]) ? count($users[$company->company_id]) : NULL; ?></td>
						<td><?php echo $company->city; ?></td>
						<td><?php echo $company->phone; ?></td>
						<td><?php echo $company->email; ?></td>
					</tr>
				<?php endforeach; ?>
			</tbody>
		</table>
		</div>
		<!-- /.box-body -->
	  </div>
	</div>
	<!-- /.col-md-9-->
	</div>
	<!-- /.row-->
    </section>
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer'); ?>