<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php
				echo form_button(array(
					'type' => 'submit',
					'form' => 'form-company-group',
					'class' => 'btn btn-primary',
					'title' => lang('save'),
					'content' => '<i class="fa fa-save"></i>'
				));
				echo anchor('admin/companies/company', '<i class="fa fa-reply" aria-hidden="true"></i>',
					array(
					'title' => lang('cancel'),
					'class' => 'btn btn-default'
					)
				);
				?>
			</div>
			<h1>
				<?php echo lang('company_companies'); ?>
				<small></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('add') . ' ' . lang('company_company'); ?></h3>
						</div>
						<div class="box-body">
							<p><?php echo lang('company_company_desc'); ?></p>
							<?php 
								echo validation_errors();
								echo form_open(NULL,array(
									'id' => 'form-company-group',
									'autocomplete' => 'off'
								));
							?>
							<div class="form-group">
							<?php
								echo form_label(lang('company_name'),'company_name');
								echo form_input(array(
										'name'	=> 'company_name',
										'value'	=> set_value('company_name'),
										'class' => 'form-control'
									));
							?>
							</div>
							<div class="form-group">
							<?php
								echo form_label(lang('company_alias'),'company_alias');
								echo form_input(array(
										'name'	=> 'company_alias',
										'value'	=> set_value('company_alias'),
										'class' => 'form-control'
									));
							?>
							</div>
							<div class="form-group">
							<?php
								echo form_label(lang('company_address'),'company_address');
								echo form_input(array(
										'name'	=> 'company_address',
										'value'	=> set_value('company_address'),
										'class' => 'form-control'
									));
							?>
							</div>
							<div class="form-group">
							<?php
								echo form_label(lang('company_address2'),'company_address2');
								echo form_input(array(
										'name'	=> 'company_address2',
										'value'	=> set_value('company_address2'),
										'class' => 'form-control'
									));
							?>
							</div>
							<div class="form-group">
							<?php
								echo form_label(lang('company_zip'),'company_zip');
								echo form_input(array(
										'name'	=> 'company_zip',
										'value'	=> set_value('company_zip'),
										'class' => 'form-control'
									));
							?>
							</div>
							<div class="form-group">
							<?php
								echo form_label(lang('company_city'),'company_city');
								echo form_input(array(
										'name'	=> 'company_city',
										'value'	=> set_value('company_city'),
										'class' => 'form-control'
									));
							?>
							</div>
							<div class="form-group">
							<?php
								echo form_label(lang('company_phone'),'company_phone');
								echo form_input(array(
										'name'	=> 'company_phone',
										'value'	=> set_value('company_phone'),
										'class' => 'form-control'
									));
							?>
							</div>
							<div class="form-group">
							<?php
								echo form_label(lang('company_fax'),'company_fax');
								echo form_input(array(
										'name'	=> 'company_fax',
										'value'	=> set_value('company_fax'),
										'class' => 'form-control'
									));
							?>
							</div>
							<div class="form-group">
							<?php
								echo form_label(lang('company_email'),'company_email');
								echo form_input(array(
										'name'	=> 'company_email',
										'value'	=> set_value('company_email'),
										'class' => 'form-control'
									));
							?>
							</div>
							<div class="form-group">
							<?php
								echo form_label(lang('company_speciality'),'company_speciality');
								echo form_dropdown('company_speciality', $specialities['specialities'], set_value('company_speciality'),array(
									'class' => 'form-control'
								));
							?>
							</div>
							<div class="form-group">
							<?php
								echo form_label(lang('company_membership'),'company_membership');
								echo form_dropdown('company_membership', $memberships['memberships'], set_value('company_membership',$memberships['default']),array(
									'class' => 'form-control'
								));
							?>
							</div>
							<div class="form-group">
							<?php
								echo form_label(lang('company_database'),'company_database');
								echo form_dropdown('company_database', $databases['databases'], set_value('company_database',$databases['default']),array(
									'class' => 'form-control'
								));
							?>
							</div>
							<?php
							echo form_close();
							?>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /.box -->
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');