<?php
$echoForm = function($menu) use($questions, $options, $selected)
{
	$odd_even = 'odd';
	if( isset($questions[$menu->page_id]) && !empty($questions[$menu->page_id]) ):
		if( !empty($menu->description) )
			echo '<div class="form-control-description">' . nl2br(html_escape($menu->description)) . '</div>';
		if( isset($questions[$menu->page_id][0]) ):
			foreach($questions[$menu->page_id][0] as $question):
				$odd_even = $odd_even === 'even' ? 'odd' : 'even';
				$o = isset($options[$question->question_id]) ? $options[$question->question_id] : array();
				$s = isset($selected[$question->question_id]) ? $selected[$question->question_id] : '';
				$q = $question;
				if( in_array($question->field, array('table')) )
				{
					$o = $options;
					$s = $selected;
					$q = $questions[$menu->page_id];
					$odd_even = 'even';
				}
				
				if( in_array($question->field, array('upload')) )
				{
					$question->field = 'document';
				}
				
				forms_view($question->field, $question->question_id, $question->name, $question->description, $o, $s, $question->settings, $odd_even, TRUE, $q);
				if( isset($questions[$menu->page_id][$question->question_id]) && !in_array($question->field, array('table')) ):
					foreach($questions[$menu->page_id][$question->question_id] as $sub):
						$o_sub = isset($options[$sub->question_id]) ? $options[$sub->question_id] : array();
						$s_sub = isset($selected[$sub->question_id]) ? $selected[$sub->question_id] : '';
						
						if( in_array($sub->field, array('upload')) )
						{
							$sub->field = 'document';
						}
						
						forms_view($sub->field, $sub->question_id, $sub->name, $sub->description, $o_sub, $s_sub, $sub->settings, $odd_even);
					endforeach;
				endif;
			endforeach;
		endif;
	endif;
}
?>
<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="float-right btn-group">
				<?php
					echo form_button('','<i class="fa fa-print" aria-hidden="true"></i>',
						array(
						'title' => 'Skriv ut',
						'class' => 'btn btn-default',
						'onclick' => 'window.print()'
						)
					);
					echo safe_anchor('admin/companies/qualityassurance/view', $fiscal, lang('back'),
						array(
						'title' => '',
						'class' => 'btn btn-default',
						)
					);
				?>
			</div>
			<h1>
				<?php echo html_escape($form->name); ?>
			</h1>
		</section>
		<?php // @STEP2: html_escape ?>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12 max-width-750">
					<h2><?php echo html_escape($user->name); ?> (<?php echo html_escape($fiscal); ?>)</h2>
					<?php
					$active_parent = '8d8b7df4-36ae-439c-b6c2-bbfce4226a14';

					if( isset($menu[$active_parent]) )
					{
						// var_dump($active_parent);exit;
						foreach($menu[$active_parent] as $parent)
						{
							// Active or not
							$active = in_array($parent->menu_id,$sidebar['menu']['active']) && $highlight?' active':'';
							$parent->name = preg_replace('/^[0-9\.\s]+/u', '',$parent->name);
							// In case it's a folder
							if($parent->type == 1)
							{
								if(	isset($menu[$parent->menu_id]) )
								{
									echo '<h3>'.html_escape($parent->name).'</h3>';
									// Echo $parent
									foreach($menu[$parent->menu_id] as $child)
									{
										// In case it's a folder
										if($child->type == 1)
										{
											if(	isset($menu[$child->menu_id]) )
											{
												echo '<h4>'.html_escape($child->name).'</h4>';
												foreach($menu[$child->menu_id] as $last)
												{
													?>
													<div class="box box-solid">
														<div class="box-header with-border"><h3 class="box-title"><?= html_escape($last->name); ?></h3></div>
														<div class="box-body no-padding"><?php $echoForm($last); ?></div>
													</div>
													<?php
												}
											}
										}
										else
										{
											echo '<h4>'.html_escape($child->name).'</h4>';
											?>
											<div class="box box-solid">
												<div class="box-body no-padding"><?php $echoForm($child); ?></div>
											</div>
											<?php
										}
									}
								}
							}
							else
							{
								if(empty($parent->settings)):
									echo '<h3>'.html_escape($parent->name).'</h3>';
									?>
									<div class="box box-solid">
										<div class="box-body no-padding"><?php $echoForm($parent); ?></div>
									</div>
									<?php
								endif;
							}
						}
					}
					?>
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
	</div>
	<!-- /.content-wrapper -->
<?php $this->load->view('template/footer');