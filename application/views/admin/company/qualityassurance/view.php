<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="float-right btn-group">
				<?php
					echo icon_anchor('admin/companies/qualityassurance/view', $fiscal - 1, '<i class="fa fa-minus" aria-hidden="true"></i>',
						array(
						'title' => '',
						'class' => 'btn btn-default',
						)
					);
				?>
				<span class="btn btn-primary"><?= html_escape($fiscal); ?></span>
				<?php
					echo icon_anchor('admin/companies/qualityassurance/view', $fiscal + 1, '<i class="fa fa-plus" aria-hidden="true"></i>',
						array(
						'title' => '',
						'class' => 'btn btn-default',
						)
					);
				?>
			</div>
			<h1>
				<?php echo lang('users_qa'); ?>
				<small>Fiscal <?= html_escape($fiscal); ?></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title">Fiscal <?= html_escape($fiscal); ?></h3>
						</div>
						<div class="box-body table-responsive">
							<table class="table table-bordered table-striped table-hover no-data-table">
								<thead>
									<tr role="row">
										<th width="200px"><?php echo lang('users_qa_done'); ?></th>
										<th><?php echo lang('users_user'); ?></th>
										<th width="120px"><?php echo lang('memberships_membership'); ?></th>
										<th width="200px"><?php echo lang('users_last_visit'); ?></th>
									</tr>
								</thead>
								<tbody>
									<?php foreach($users as $user): ?>
										<tr>
											<td><?php echo $user->qa_done; ?></td>
											<?php if(!empty($user->qa_done)): ?>
											<td><?php echo safe_anchor('admin/companies/qualityassurance/form', [$fiscal, $user->user_id], $user->name); ?></td>
											<?php else: ?>
											<td><?php echo html_escape($user->name); ?></td>
											<?php endif; ?>
											<td><?php echo isset($memberships[$user->membership_id]) ? ltrim($memberships[$user->membership_id]->name, 'Kvalprak') : NULL; ?></td>
											<td><?php echo $user->last_visit; ?></td>
										</tr>
									<?php endforeach; ?>
								</tbody>
							</table>
						</div>
						<!-- /.box-body -->
					</div>
				</div>
				<!-- /.col-md-9-->
			</div>
			<!-- /.row-->
		</section>
	</div>
	<!-- /.content-wrapper -->
<?php $this->load->view('template/footer'); ?>