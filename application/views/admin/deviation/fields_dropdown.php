<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php
				echo form_button(array(
					'type' => 'submit',
					'form' => 'form-company-group',
					'id' => 'submit_order',
					'class' => 'btn btn-primary',
					'title' => lang('save'),
					'content' => 'Sortera',
					'disabled' => 'disabled'
				));
				echo anchor('admin/deviation/fields', '<i class="fa fa-reply" aria-hidden="true"></i>',
					array(
					'title' => lang('cancel'),
					'class' => 'btn btn-default'
					)
				);
				?>
			</div>
			<h1>
				<?php echo lang('deviation_field_input'); ?>
				<small><?php echo $inputs[$field->input]; ?></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo html_escape($field->title); ?></h3>
						</div>
						<div class="box-body no-padding">
							<?php
								echo validation_errors();
								echo form_open('admin/deviation/fields/sort_dropdown/' . $field->df_id,array(
									'id' => 'form-company-group',
									'autocomplete' => 'off'
								));
								echo form_input([
									'type' => 'hidden',
									'id' => 'uuid_kvalprak',
									'name' => 'uuid_kvalprak',
									'value' => $uuid_kvalprak
								]);
								echo form_input([
									'type' => 'hidden',
									'id' => 'sort_order',
									'name' => 'sort_order'
								]);
								echo form_close();
							?>
							<table class="table table-striped no-data-table">
								<colspan>
									<col>
									<col width="120">
								</colspan>
								<thead>
									<tr>
										<th>Värde</th>
										<th></th>
									</tr>
								</thead>
								<tbody class="sortlist">
								<?php foreach($dropdown as $d): ?>
									<tr id="<?php echo $d->option_id; ?>">
										<td><?php echo empty($d->name)? 'Inget värde valt' : html_escape($d->name); ?></td>
										<td>
											<div class="btn-group btn-group-sm">
												<?php
												echo form_button(array(
													'class' => 'add btn btn-default',
													'title' => lang('add'),
													'content' => '<i class="fa fa-plus-square" aria-hidden="true"></i>',
												));
												echo icon_anchor('admin/deviation/fields/edit_dropdown', [$field->df_id, $d->option_id],'<i class="fa fa-pencil-square-o" aria-hidden="true"></i>',
													array(
													'title' => lang('edit'),
													'class' => 'btn btn-default',
													)
												);
												if( ! in_array($d->option_id, $answers) )
												{
													echo icon_anchor('admin/deviation/fields/delete_dropdown', [$field->df_id, $d->option_id] ,'<i class="fa fa-times-circle"" aria-hidden="true"></i>',
														array(
														'title' => lang('delete'),
														'class' => 'btn btn-default',
														)
													);
												}
												?>
											</div>
										</td>
									</tr>
								<?php endforeach; ?>
								</tbody>
							</table>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /.box -->
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');