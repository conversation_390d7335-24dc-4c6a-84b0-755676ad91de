<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php
				echo form_button(array(
					'type' => 'submit',
					'form' => 'form-company-group',
					'class' => 'btn btn-primary',
					'title' => lang('save'),
					'content' => '<i class="fa fa-save"></i>',
				));
				?>
			</div>
			<h1>
				<?php echo lang('deviation_deviations'); ?>
				<small><?php echo lang('deviation_fields_sort'); ?></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="box box-primary collapsed-box">
				<div class="box-header with-border">
					<h3 class="box-title"><?php echo lang('deviation_sort_heading'); ?></h3>

					<div class="box-tools float-right">
						<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-plus"></i>
						</button>
					</div>
					<!-- /.box-tools -->
				</div>
				<!-- /.box-header -->
				<div class="box-body">
					<?php echo nl2br(lang('deviation_sort_information')); ?>
				</div>
				<!-- /.box-body -->
			</div>
			<?php
				echo validation_errors();
				echo form_open(NULL,array(
					'id' => 'form-company-group',
					'autocomplete' => 'off'
				));
				echo form_input([
					'type' => 'hidden',
					'id'   => 'fieldAvailableInput',
					'name' => 'fieldAvailable'
				]);
				echo form_input([
					'type' => 'hidden',
					'id'   => 'fieldOneInput',
					'name' => 'fieldOne'
				]);
				echo form_input([
					'type' => 'hidden',
					'id'   => 'fieldTwoInput',
					'name' => 'fieldTwo'
				]);
				echo form_input([
					'type' => 'hidden',
					'id'   => 'fieldThreeInput',
					'name' => 'fieldThree'
				]);
				echo form_close();
			?>
			<?php if( ! $newDeviation ): ?>
				<div class="alert alert-danger no-print">Du kan inte skapa några avvikelser för tillfället - alla obligatoriska fält är inte aktiverade.</div>
			<?php endif; ?>
			<div class="row">
				<div class="col-md-3">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title">Tillgängliga</h3>
						</div>
						<div class="box-body no-padding">
							<ul id="fieldAvailable" class="fieldWidget nav flex-column nav-pills nav-stacked" aria-orientation="vertical">
								<?php
								if( !empty($fields[0]) ):
									foreach($fields[0] as $key => $val):
										$required = '';
										if($val->required == 2) {
											$required = 'required red';
										}
										if($val->required_kvalprak) {
											$required = 'required dark-red';
										} ?>
										<li class="<?php echo $required; ?>" id="<?php echo $val->df_id; ?>">
											<?php echo $val->title; ?>
											<div class="btn-group">
												<button type="button" class="btn btn-default btn-sm" data-toggle="tooltip" title="<?php echo $inputs[$val->input]; ?>">
													<i class="fa fa-list"></i>
												</button>
												<button type="button" class="btn btn-default btn-sm" data-toggle="tooltip" title="<?php echo $val->description ? $val->description : 'Ingen beskrivning vald.';?>">
													<i class="fa fa-info"></i>
												</button>
											</div>
										</li>
								<?php 
									endforeach;
								endif;
								?>
							</ul>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /.box -->
				</div>
				<!-- /.col-md-3-->
				<div class="col-md-3">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title">Steg 1</h3>
						</div>
						<div class="box-body no-padding">
							<ul id="fieldOne" class="fieldWidget nav flex-column nav-pills nav-stacked" aria-orientation="vertical">
								<?php
								if( !empty($fields[1]) ):
									foreach($fields[1] as $key => $val):
										$required = '';
										if($val->required == 2) {
											$required = 'required red';
										}
										if($val->required_kvalprak) {
											$required = 'required dark-red';
										} ?>
										<li class="<?php echo $required; ?>" id="<?php echo $val->df_id; ?>">
											<?php echo $val->title; ?>
											<div class="btn-group">
												<button type="button" class="btn btn-default btn-sm" data-toggle="tooltip" title="<?php echo $inputs[$val->input]; ?>">
													<i class="fa fa-list"></i>
												</button>
												<button type="button" class="btn btn-default btn-sm" data-toggle="tooltip" title="<?php echo $val->description;?>">
													<i class="fa fa-info"></i>
												</button>
											</div>
										</li>
								<?php 
									endforeach;
								endif;
								?>
							</ul>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /.box -->
				</div>
				<!-- /.col-md-3-->
				<div class="col-md-3">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title">Steg 2</h3>
						</div>
						<div class="box-body no-padding">
							<ul id="fieldTwo" class="fieldWidget nav flex-column nav-pills nav-stacked" aria-orientation="vertical">
								<?php
								if( !empty($fields[2]) ):
									foreach($fields[2] as $key => $val):
										$required = '';
										if($val->required == 2) {
											$required = 'required red';
										}
										if($val->required_kvalprak) {
											$required = 'required dark-red';
										} ?>
										<li class="<?php echo $required; ?>" id="<?php echo $val->df_id; ?>">
											<?php echo $val->title; ?>
											<div class="btn-group">
												<button type="button" class="btn btn-default btn-sm" data-toggle="tooltip" title="<?php echo $inputs[$val->input]; ?>">
													<i class="fa fa-list"></i>
												</button>
												<button type="button" class="btn btn-default btn-sm" data-toggle="tooltip" title="<?php echo $val->description;?>">
													<i class="fa fa-info"></i>
												</button>
											</div>
										</li>
								<?php 
									endforeach;
								endif;
								?>
							</ul>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /.box -->
				</div>
				<!-- /.col-md-3-->
				<div class="col-md-3">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title">Steg 3</h3>
						</div>
						<div class="box-body no-padding">
							<ul id="fieldThree" class="fieldWidget nav flex-column nav-pills nav-stacked" aria-orientation="vertical">
								<?php
								if( !empty($fields[3]) ):
									foreach($fields[3] as $key => $val):
										$required = '';
										if($val->required == 2) {
											$required = 'required red';
										}
										if($val->required_kvalprak) {
											$required = 'required dark-red';
										} ?>
										<li class="<?php echo $required; ?>" id="<?php echo $val->df_id; ?>">
											<?php echo $val->title; ?>
											<div class="btn-group">
												<button type="button" class="btn btn-default btn-sm" data-toggle="tooltip" title="<?php echo $inputs[$val->input]; ?>">
													<i class="fa fa-list"></i>
												</button>
												<button type="button" class="btn btn-default btn-sm" data-toggle="tooltip" title="<?php echo $val->description;?>">
													<i class="fa fa-info"></i>
												</button>
											</div>
										</li>
								<?php 
									endforeach;
								endif;
								?>
							</ul>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /.box -->
				</div>
				<!-- /.col-md-3-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');