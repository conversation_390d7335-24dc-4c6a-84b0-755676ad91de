<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php
				echo form_button(array(
					'type' => 'submit',
					'form' => 'form-company-group',
					'class' => 'btn btn-primary',
					'title' => lang('save'),
					'content' => '<i class="fa fa-save"></i>'
				));
				
				echo anchor('admin/deviation/fields', '<i class="fa fa-reply" aria-hidden="true"></i>',
					array(
					'title' => lang('cancel'),
					'class' => 'btn btn-default'
					)
				);
				?>
			</div>
			<h1>
				<?php echo lang('deviation_deviations'); ?>
				<small><?php echo lang('add'); ?></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('add') . ' ' . lang('deviation_field'); ?></h3>
						</div>
						<div class="box-body">
							<p><?php echo lang('deviation_fields_desc'); ?></p>
							<?php 
								echo validation_errors();
								echo form_open(NULL,array(
									'id' => 'form-company-group',
									'autocomplete' => 'off'
								));
							?>
							<div class="form-group">
								<?php echo form_label(lang('deviation_field_title'),'deviation_field_title'); ?>
								<p class="form-text"><?php echo nl2br(lang('deviation_field_title_help')); ?></p>
								<?php
									echo form_input(array(
											'name'	=> 'deviation_field_title',
											'value'	=> set_value('deviation_field_title'),
											'class' => 'form-control'
										));
								?>
							</div>
								<div class="form-group">
									<?php echo form_label(lang('deviation_field_description'),'deviation_field_description'); ?>
									<p class="form-text"><?php echo nl2br(lang('deviation_field_description_help')); ?></p>
									<?php
										echo form_textarea(array(
												'name'	=> 'deviation_field_description',
												'value'	=> set_value('deviation_field_description'),
												'class' => 'form-control'
											));
									?>
								</div>
							<div class="form-group">
								<?php echo form_label(lang('deviation_field_input'),'deviation_field_input'); ?>
								<p class="form-text"><?php echo nl2br(lang('deviation_field_input_help')); ?></p>
								<?php
									echo form_dropdown('deviation_field_input', $inputs, set_value('deviation_field_input','input'),array(
										'class' => 'form-control'
									));
								?>
							</div>
							<div class="form-group">
								<?php echo form_label(lang('deviation_field_required'),'deviation_field_required'); ?>
								<p class="form-text"><?php echo nl2br(lang('deviation_field_required_help')); ?></p>
								<?php foreach($no_yes AS $val => $name): ?>
								<div class="form-check">
									<label>
										<?php echo form_radio('deviation_field_required', $val, $val === 1) . $name; ?>
									</label>
								</div>
								<?php endforeach; ?>
							</div>
							<div class="form-group">
								<?php echo form_label(lang('deviation_field_search'),'deviation_field_search'); ?>
								<p class="form-text"><?php echo nl2br(lang('deviation_field_search_help')); ?></p>
								<?php foreach($no_yes AS $val => $name): ?>
								<div class="form-check">
									<label>
										<?php echo form_radio('deviation_field_search', $val, $val === 1) . $name; ?>
									</label>
								</div>
								<?php endforeach; ?>
							</div>
							<div class="form-group">
								<?php echo form_label(lang('deviation_field_list'),'deviation_field_list'); ?>
								<p class="form-text"><?php echo nl2br(lang('deviation_field_list_help')); ?></p>
								<?php foreach($no_yes AS $val => $name): ?>
								<div class="form-check">
									<label>
										<?php echo form_radio('deviation_field_list', $val, $val === 1) . $name; ?>
									</label>
								</div>
								<?php endforeach; ?>
							</div>
							<div class="form-group">
								<?php echo form_label(lang('deviation_field_sort'),'deviation_field_sort'); ?>
								<p class="form-text"><?php echo nl2br(lang('deviation_field_sort_help')); ?></p>
							</div>
							<?php echo form_close(); ?>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /.box -->
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');