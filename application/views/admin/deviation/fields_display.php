<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php
				echo anchor('admin/deviation/fields/add', '<i class="fa fa-plus" aria-hidden="true"></i>',
					array(
					'title' => lang('add') . ' ' . lang('deviation_field'),
					'class' => 'btn btn-primary',
					)
				);
				?>
			</div>
			<h1>
				<?php echo lang('deviation_deviations'); ?>
				<small><?php echo lang('deviation_field_management'); ?></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('deviation_fields'); ?></h3>
						</div>
						<div class="box-body no-padding">
							<table class="table table-striped no-data-table">
								<thead>
									<tr>
										<th>Fältnr</th>
										<th>Rubrik</th>
										<th>Fälttyp</th>
										<th>Obligatoriskt</th>
										<th>Sökbar</th>
										<th>Lista</th>
									</tr>
								</thead>
								<tbody>
								<?php foreach($fields as $field): ?>
									<tr>
										<td><?php echo safe_anchor('admin/deviation/fields/edit',$field->df_id,substr($field->df_id,0,8).'...'); ?></td>
										<td><?php echo html_escape($field->title); ?></td>
										<td>
											<?php
											if(in_array($field->input, ['dropdown','email']))
											{
												echo safe_anchor('admin/deviation/fields/' . $field->input, $field->df_id, $inputs[$field->input]);
											}
											else
											{
												echo $inputs[$field->input];
											}
											?>
										</td>
										<td><?php echo $no_yes[$field->required]; ?></td>
										<td><?php echo $no_yes[$field->search]; ?></td>
										<td><?php echo $no_yes[$field->list]; ?></td>
									</tr>
								<?php endforeach; ?>
								</tbody>
							</table>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /.box -->
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');