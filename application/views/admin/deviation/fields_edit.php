<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php
				if( ! in_array($field->input, ['upload','department','email']) && ! $field->required_kvalprak && empty($answers) )
				{
					echo icon_anchor('admin/deviation/fields/delete', $field->df_id, '<i class="fa fa-trash" aria-hidden="true"></i>',
						array(
						'title' => lang('delete'),
						'class' => 'btn btn-danger'
						)
					);
				}

				echo form_button(array(
					'type' => 'submit',
					'form' => 'form-company-group',
					'class' => 'btn btn-primary',
					'title' => lang('save'),
					'content' => '<i class="fa fa-save"></i>'
				));
				
				echo anchor('admin/deviation/fields', '<i class="fa fa-reply" aria-hidden="true"></i>',
					array(
					'title' => lang('cancel'),
					'class' => 'btn btn-default'
					)
				);
				?>
			</div>
			<h1>
				<?php echo lang('deviation_deviations'); ?>
				<small><?php echo lang('edit'); ?></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('edit') . ' ' . lang('deviation_field'); ?></h3>
						</div>
						<div class="box-body">
							<p><?php echo lang('deviation_fields_desc'); ?></p>

							<?php if( $field->input === 'upload' ): ?>
								<div class="alert alert-warning no-print">Det går endast att redigera rubrik samt beskrivning utav följande fälttyp.</div>
							<?php elseif( $field->input === 'email' ): ?>
								<div class="alert alert-warning no-print">Det går endast att redigera rubrik, beskrivning, kontaktuppgifter samt obligatoriskt utav följande fälttyp.</div>
							<?php elseif( $field->required_kvalprak OR $field->input === 'department' ): ?>
								<div class="alert alert-warning no-print">Följande fält har valts som obligatorisk av Kvalprak AB. Du kan därför inte ändra på ett antal fält.</div>
							<?php elseif( ! empty($answers) ): ?>
							<div class="alert alert-warning no-print">Följande fält har besvarats inuti en avvikelserapport.<br/>Ändra därför rubrik och beskrivningen varsamt.</div>
							<?php endif; ?>

							<?php 
								echo validation_errors();
								echo form_open(NULL,array(
									'id' => 'form-company-group',
									'autocomplete' => 'off'
								));
							?>
							<div class="form-group">
								<?php echo form_label(lang('deviation_field_title'),'deviation_field_title'); ?>
								<p class="form-text"><?php echo nl2br(lang('deviation_field_title_help')); ?></p>
								<?php
									echo form_input(array(
											'name'	=> 'deviation_field_title',
											'value'	=> set_value('deviation_field_title',$field->title),
											'class' => 'form-control'
										));
								?>
							</div>
							<div class="form-group">
								<?php echo form_label(lang('deviation_field_description'),'deviation_field_description'); ?>
								<p class="form-text"><?php echo nl2br(lang('deviation_field_description_help')); ?></p>
								<?php
									echo form_textarea(array(
											'name'	=> 'deviation_field_description',
											'value'	=> set_value('deviation_field_description',$field->description, FALSE),
											'class' => 'form-control'
										));
								?>
							</div>
							<?php if( ! $field->required_kvalprak && ! in_array($field->input,['upload','department','email','eventanalysis']) && empty($answers) ): ?>
							<div class="form-group">
								<?php echo form_label(lang('deviation_field_input'),'deviation_field_input'); ?>
								<p class="form-text"><?php echo nl2br(lang('deviation_field_input_help')); ?></p>
								<?php
									echo form_dropdown('deviation_field_input', $inputs, set_value('deviation_field_input',$field->input),array(
										'class' => 'form-control'
									));
								?>
							</div>
							<?php endif; ?>
							<div class="form-group">
								<?php echo form_label(lang('deviation_field_required'),'deviation_field_required'); ?>
								<p class="form-text"><?php echo nl2br(lang('deviation_field_required_help')); ?></p>
								<?php foreach($no_yes AS $val => $name): ?>
								<div class="form-check">
									<label>
										<?php
											$data = [
												'name' => 'deviation_field_required',
												'value' => $val,
												'checked' => set_radio('deviation_field_required', $val, $val == $field->required),
											];
											if( $field->required_kvalprak OR in_array($field->input,['upload','department','eventanalysis']) )
											{
												$data['disabled'] = 'disabled';
											}
											echo form_radio($data) . $name;
										?>
									</label>
								</div>
								<?php endforeach; ?>
							</div>
							<div class="form-group">
								<?php echo form_label(lang('deviation_field_search'),'deviation_field_search'); ?>
								<p class="form-text"><?php echo nl2br(lang('deviation_field_search_help')); ?></p>
								<?php foreach($no_yes AS $val => $name): ?>
								<div class="form-check">
									<label>
										<?php
											$data = [
												'name' => 'deviation_field_search',
												'value' => $val,
												'checked' => set_radio('deviation_field_search', $val, $val == $field->search),
											];
											if( in_array($field->input,['upload','email','eventanalysis']) )
											{
												$data['disabled'] = 'disabled';
											}
											echo form_radio($data) . $name;
										?>
									</label>
								</div>
								<?php endforeach; ?>
							</div>
							<div class="form-group">
								<?php echo form_label(lang('deviation_field_list'),'deviation_field_list'); ?>
								<p class="form-text"><?php echo nl2br(lang('deviation_field_list_help')); ?></p>
								<?php foreach($no_yes AS $val => $name): ?>
								<div class="form-check">
									<label>
										<?php
											$data = [
												'name' => 'deviation_field_list',
												'value' => $val,
												'checked' => set_radio('deviation_field_list', $val, $val == $field->list),
											];
											if( in_array($field->input,['upload','email','eventanalysis','department']) )
											{
												$data['disabled'] = 'disabled';
											}
											echo form_radio($data) . $name;
										?>
									</label>
								</div>
								<?php endforeach; ?>
							</div>
							<div class="form-group">
								<?php echo form_label(lang('deviation_field_sort'),'deviation_field_sort'); ?>
								<p class="form-text"><?php echo nl2br(lang('deviation_field_sort_help')); ?></p>
							</div>
							<?php echo form_close(); ?>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /.box -->
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');