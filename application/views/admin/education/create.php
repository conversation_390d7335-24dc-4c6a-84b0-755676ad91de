<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php
				echo form_button(array(
					'type' => 'submit',
					'form' => 'form-company-group',
					'class' => 'btn btn-primary',
					'title' => lang('save'),
					'content' => '<i class="fa fa-save"></i>'
				));
				echo anchor('admin/education', '<i class="fa fa-reply" aria-hidden="true"></i>',
					array(
					'title' => lang('cancel'),
					'class' => 'btn btn-default'
					)
				);
				?>
			</div>
			<h1>
				<?php echo lang('education_groups'); ?>
				<small></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('add') . ' ' . lang('groups_group'); ?></h3>
						</div>
						<div class="box-body">
							<p><?php echo lang('groups_group_desc'); ?></p>
							<?php 
								echo validation_errors();
								echo form_open(NULL,array(
									'id' => 'form-company-group',
									'autocomplete' => 'off'
								));
								echo form_hidden('groups_type', 'education');
							?>
							<div class="form-group">
							<?php
								echo form_label(lang('groups_name'),'groups_name');
								echo form_input(array(
										'name'	=> 'groups_name',
										'value'	=> set_value('groups_name'),
										'class' => 'form-control'
									));
							?>
							</div>
							<?php echo form_label(lang('documents_document'),'tree_documents'); ?>
							<div class="document-tree">
								<ul>
									<?php
									foreach($move['menu'][0] as $menu_id => $menu)
									{
										if( ! in_array($menu_id, $move['menu']['loop']) )
											continue;
										echo '<li><span>' . html_escape($menu->name) . '</span><ul>';
										// var_dump($menu_id);exit;
										if( isset($move['menu'][$menu_id]) )
										{
											foreach($move['menu'][$menu_id] as $parent)
											{
												if( ! in_array($parent->menu_id, $move['menu']['loop']) )
													continue;
												$parent->name = preg_replace('/^[0-9\.\s]+/u', '',$parent->name);
												// In case it's a folder
												if($parent->type == 1)
												{
													if(	isset($move['menu'][$parent->menu_id]) )
													{
														echo '<li><span><input type="checkbox" class="markAll"/> <strong>' . html_escape($parent->name) . '</strong></span><ul>';
														// Echo $parent
														foreach($move['menu'][$parent->menu_id] as $child)
														{
															if( isset($move['folder'][$child->menu_id]) )
															{
																// echo '<li>' . html_escape($child->name) . '<ul>';
																$print_folder = TRUE;
																foreach($move['folder'][$child->menu_id] as $child_folder)
																{
																	if( isset($move['document'][$child_folder->folder_id]) )
																	{
																		if($print_folder)
																		{
																			echo '<li><span><input type="checkbox" class="markAll"/><i> ' . html_escape($child->name) . '</i></span><ul>';
																		}
																		
																		echo '<li><span><input type="checkbox" class="markAll"/> ' . html_escape($child_folder->name) .'</span><ul>';
																		foreach($move['document'][$child_folder->folder_id] as $document)
																		{
																			echo '
																			<li>
																				<label>
																					' . form_checkbox('tree_documents[]', $document->document_id . '_' . $document->last_revised, set_checkbox('tree_documents', $document->document_id . '_' . $document->last_revised)) . $document->name . '
																				</label>
																			</li>
																			';
																		}
																		echo '</ul></li>';
																		
																		if($print_folder)
																		{
																			echo '</ul></li>';
																			$print_folder = FALSE;
																		}
																	}
																}

																// echo '</ul>';
															}
														}
														echo '</ul></li>';
														// Close $parent
													}
													
												}
												else
												{
													if( isset($move['folder'][$parent->menu_id]) )
													{
														echo '<li><span><input type="checkbox" class="markAll"/> <strong>' . html_escape($parent->name) . '</strong></span><ul>';
														foreach($move['folder'][$parent->menu_id] as $parent_folder)
														{
															if( isset($move['document'][$parent_folder->folder_id]) )
															{
																echo '<li><span><input type="checkbox" class="markAll"/> ' . html_escape($parent_folder->name) .'</span><ul>';
																foreach($move['document'][$parent_folder->folder_id] as $document)
																{
																	echo '
																	<li>
																		<label>
																			' . form_checkbox('tree_documents[]', $document->document_id . '_' . $document->last_revised, set_checkbox('tree_documents', $document->document_id . '_' . $document->last_revised)) . $document->name . '
																		</label>
																	</li>
																	';
																}
																echo '</ul></li>';
															}
														}
														echo '</ul></li>';
													}
													// Echo $parent, that's not a folder
												}
											}
										}
										echo '</ul></li>';
									}
									?>
								</ul>
							</div>
							<?php echo form_close(); ?>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /.box -->
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');