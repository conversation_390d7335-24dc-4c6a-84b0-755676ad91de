<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php
				echo anchor('admin/education/create', '<i class="fa fa-plus" aria-hidden="true"></i>',
					array(
					'title' => lang('add') . ' ' . lang('education_group'),
					'class' => 'btn btn-primary',
					)
				);
				?>
			</div>
			<h1>
				<?php echo lang('education_groups'); ?>
				<small></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('education_group'); ?></h3>
						</div>
						<div class="box-body no-padding">
							<?php
							if( empty($groups['education']) )
							{
								echo '<p class="margin">' . lang('education_empty') . '</p>';
							}
							else
							{
							?>
							<table class="table table-striped no-data-table">
								<colspan>
									<col>
									<col width="85px;">
								</colspan>
								<tbody>
								<?php foreach($groups['education'] as $group_id => $group_name): ?>
									<tr>
										<td><?php echo safe_anchor('admin/education/update', $group_id, $group_name); ?></td>
										<td>
											<div class="btn-group btn-group-sm">
												<?php
												echo icon_anchor('admin/education/groups', $group_id ,'<i class="fa fa-users" aria-hidden="true"></i>',
													array(
													'title' => lang('users_groups'),
													'class' => 'btn btn-default',
													)
												);
												echo icon_anchor('admin/education/delete', $group_id ,'<i class="fa fa-trash" aria-hidden="true"></i>',
													array(
													'title' => lang('delete'),
													'class' => 'btn btn-default',
													)
												);
												?>
											</div>
										</td>
									</tr>
								<?php endforeach; ?>
								</tbody>
							</table>
							<?php
							}
							?>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /.box -->
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');