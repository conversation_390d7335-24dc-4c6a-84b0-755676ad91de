<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php
				echo form_button(array(
					'type' => 'submit',
					'form' => 'form-company-group',
					'class' => 'btn btn-primary',
					'title' => lang('save'),
					'content' => '<i class="fa fa-save"></i>'
				));
				
				echo anchor('admin/forms', '<i class="fa fa-reply" aria-hidden="true"></i>',
					array(
					'title' => lang('cancel'),
					'class' => 'btn btn-default'
					)
				);
				?>
			</div>
			<h1>
				<?php echo lang('forms_forms'); ?>
				<small></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title">
								<?php if( $form_id )
								{
									echo lang('add') . ' ' . mb_strtolower(lang('forms_'.$type) . ' '. lang('for')) . ' ' . $form->name;
								}
								?>
							</h3>
						</div>
						<div class="box-body">
							<p><?php echo lang('forms_form_desc'); ?></p>
							<?php 
								echo validation_errors();
								echo form_open(NULL,array(
									'id' => 'form-company-group',
									'autocomplete' => 'off'
								));
							?>
							<div class="form-group">
								<?php echo form_label(lang('forms_name'),'forms_name'); ?>
								<p class="form-text"><?php echo nl2br(lang('forms_name_help')); ?></p>
								<?php
									echo form_input(array(
											'name'	=> 'forms_name',
											'value'	=> set_value('forms_name', $form->name),
											'class' => 'form-control'
										));
								?>
							</div>
							<div class="form-group">
								<?php echo form_label(lang('forms_description'),'forms_description'); ?>
								<p class="form-text"><?php echo lang('forms_description_page_help'); ?></p>
								<?php
									echo form_textarea(array(
											'name'	=> 'forms_description',
											'value'	=> set_value('forms_description',$form->description, FALSE),
											'class' => 'form-control',
											'rows'  => '3',
										));
								?>
							</div>
							<?php if($type === 'form'): ?>
								<div class="form-group">
									<?php echo form_label(lang('forms_types'),'forms_types'); ?>
									<p class="form-text"><?php echo nl2br(lang('forms_types_help')); ?></p>
									<p class="form-text"><?php echo nl2br(lang('forms_types_help_extended')); ?></p>
									<?php
										echo form_dropdown('forms_types', $forms_types, set_value('forms_types',$form->type),array(
											'class' => 'form-control'
										));
									?>
								</div>
								<div class="form-group">
								<?php
									echo form_label(lang('forms_global'),'forms_global');
									$default = array(0 => lang('no'), 1 => lang('yes'));
									echo form_dropdown('forms_global', $default, set_value('forms_global',$form->global),array(
										'class' => 'form-control'
									));
								?>
								</div>
							<?php endif; ?>
							<?php echo form_close(); ?>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /.box -->
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');