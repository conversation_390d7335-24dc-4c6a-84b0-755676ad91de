<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="float-right btn-group">
				<?php
				// var_dump($complete_url,$question_url);
				if( in_array('question',$buttons,TRUE) ) 
				{
					echo icon_anchor('admin/forms/option/create/00000000-0000-0000-0000-000000000000', $complete_url, '<i class="fa fa-plus" aria-hidden="true"></i>',
						array(
						'title' => lang('add') . ' ' . lang('forms_question'),
						'class' => 'btn btn-primary',
						)
					);
				}
				if( isset($question_url) && isset($question->parent_id) ) 
				{
					echo icon_anchor('admin/forms/question/children', array_merge(array($question->parent_id),$question_url), '<i class="fa fa-reply" aria-hidden="true"></i>',
						array(
						'title' => lang('cancel'),
						'class' => 'btn btn-default',
						)
					);
				}
				else if( isset($question_url) ) 
				{
					echo icon_anchor('admin/forms/questions', $question_url, '<i class="fa fa-reply" aria-hidden="true"></i>',
						array(
						'title' => lang('cancel'),
						'class' => 'btn btn-default',
						)
					);
				}
				?>
			</div>
			<h1>
				<?php echo lang('forms_values'); ?>
				<small></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title">
								<?php
								if( isset( $form ) )
								{
									echo html_escape($form->name);
									
									if( isset($page) )
									{
										echo ' > ' . html_escape($page->name);
									}
									
									if( isset($sub_page) )
									{
										echo ' > ' . html_escape($sub_page->name);
									}
									if( isset($page_current) )
									{
										echo ' > ' . html_escape($page_current->name);
									}
									if( isset($question_parent) )
									{
										echo ' > ' . html_escape($question_parent->name);
									}
									if( isset($question) )
									{
										echo ' > ' . html_escape($question->name);
									}
								}
								else
								{
									echo lang('forms_question');
								}
								?>
							</h3>
						</div>
						<div class="box-body no-padding">
							<table class="table table-striped no-data-table">
								<colspan>
									<col>
									<col width="150px;">
									<col width="150px;">
								</colspan>
								<tbody>
								<?php foreach($options as $q): ?>
									<tr>
										<td><?php echo html_escape($q->name); ?></td>
										<td>
											<div class="btn-group btn-group-sm">
												<?php
												echo icon_anchor('admin/forms/option/edit', array_merge([$q->option_id],$complete_url), '<i class="fa fa-pencil" aria-hidden="true"></i>',
													array(
													'title' => lang('forms_question'),
													'class' => 'btn btn-default',
													)
												);
												echo icon_anchor('admin/forms/option/delete', array_merge([$q->option_id],$complete_url), '<i class="fa fa-trash" aria-hidden="true"></i>',
													array(
													'title' => lang('delete') . ' ' . lang('forms_values'),
													'class' => 'btn btn-danger',
													)
												);
												?>
											</div>
										</td>
									</tr>
								<?php endforeach; ?>
								</tbody>
							</table>
						</div>
					</div>
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');