<?php $this->load->view('template/header-multidatespicker'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php
				echo form_button(array(
					'type' => 'submit',
					'form' => 'form-company-group',
					'class' => 'btn btn-primary',
					'title' => lang('save'),
					'content' => '<i class="fa fa-save"></i>'
				));
				echo icon_anchor('admin/forms/view', $form_id, '<i class="fa fa-reply" aria-hidden="true"></i>',
					array(
					'title' => lang('cancel'),
					'class' => 'btn btn-default',
					)
				);
				?>
			</div>
			<h1>
				<?php echo lang('edit') . ' ' . mb_strtolower(lang('forms_checklist')); ?>
				<small></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<?php 
				echo validation_errors();
				echo form_open(NULL,array(
					'id' => 'form-company-group',
					'autocomplete' => 'off'
				));
			?>
			<div class="row">
				<div class="col-lg-3">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title">
								Befattning och/eller användare
							</h3>
						</div>
						<div class="box-body">
							<div class="form-group">
								<?php echo form_label(lang('forms_survey_position'),'survey_position[]'); ?>
								<p class="form-text"><?php echo lang('forms_survey_position_help'); ?></p>
								<?php foreach($groups['position'] as $group_id => $group_name): ?>
								<div class="form-check">
									<label>
										<?php echo form_checkbox('survey_position[]', $group_id, set_checkbox('survey_position[]', $group_id, in_array($group_id, $groups_checked))) . $group_name; ?>
									</label>
								</div>
								<?php endforeach; ?>
							</div>
							<div class="form-group">
								<?php echo form_label(lang('forms_survey_users'),'survey_users[]'); ?>
								<p class="form-text"><?php echo lang('forms_survey_users_help'); ?></p>
								<?php
									echo form_multiselect('survey_users[]', $users, set_value('survey_users[]', $users_checked),array(
										'class' => 'form-control',
										'id'    => 'survey_users'
									));
								?>
							</div>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /.box -->
				</div>
				<!-- /.col-lg-3-->
				<div class="col-lg-9">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title">
								När skall checklistan fyllas i?
							</h3>
						</div>
						<div class="box-body">
							<?php
								echo form_input([
									'name' => 'survey_dates',
									'id'   => 'survey_dates',
									'type' => 'hidden',
								]);
							?>
							<div id="mdp-survey"></div>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /.box -->
				</div>
				<!-- /.col-lg-9-->
			</div>
			<!-- /.row -->
			<?php echo form_close(); ?>
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer-multidatespicker');