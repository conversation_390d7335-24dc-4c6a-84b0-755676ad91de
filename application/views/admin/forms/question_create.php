<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php
				echo form_button(array(
					'type' => 'submit',
					'form' => 'form-company-group',
					'class' => 'btn btn-primary',
					'title' => lang('save'),
					'content' => '<i class="fa fa-save"></i>'
				));
				if( isset($question_id) ) 
				{
					echo icon_anchor('admin/forms/question/children', $complete_url, '<i class="fa fa-reply" aria-hidden="true"></i>',
						array(
						'title' => lang('cancel'),
						'class' => 'btn btn-default',
						)
					);
				}
				else if( isset($question_url) ) 
				{
					echo icon_anchor('admin/forms/questions', $question_url, '<i class="fa fa-reply" aria-hidden="true"></i>',
						array(
						'title' => lang('cancel'),
						'class' => 'btn btn-default',
						)
					);
				}
				?>
			</div>
			<h1>
				<?php echo lang('add') . ' ' . mb_strtolower(lang('forms_question')); ?>
				<small></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title">
								<?php if( $form_id )
								{
									echo html_escape($form->name);
									
									if( isset($page) )
									{
										echo ' > ' . html_escape($page->name);
									}
									
									if( isset($sub_page) )
									{
										echo ' > ' . html_escape($sub_page->name);
									}
									if( isset($page_current) )
									{
										echo ' > ' . html_escape($page_current->name);
									}
									if( isset($question_parent) )
									{
										echo ' > ' . html_escape($question_parent->name);
									}
									if( isset($question) )
									{
										echo ' > ' . html_escape($question->name);
									}
								}
								?>
							</h3>
						</div>
						<div class="box-body">
							<p><?php echo lang('forms_form_desc'); ?></p>
							<?php 
								echo validation_errors();
								echo form_open(NULL,array(
									'id' => 'form-company-group',
									'autocomplete' => 'off'
								));
							?>
							<?php /*
							<div class="form-group">
								<?php echo form_label(lang('forms_question_id_old'),'forms_question_id_old'); ?>
								<p class="form-text"><?php echo nl2br(lang('forms_name_help')); ?></p>
								<?php
									echo form_input(array(
											'name'	=> 'forms_question_id_old',
											'value'	=> set_value('forms_question_id_old'),
											'class' => 'form-control'
										));
								?>
							</div>
							*/ ?>
							<div class="form-group">
								<?php echo form_label(lang('forms_name'),'forms_name'); ?>
								<p class="form-text"><?php echo nl2br(lang('forms_name_help')); ?></p>
								<?php
									echo form_input(array(
											'name'	=> 'forms_name',
											'value'	=> set_value('forms_name'),
											'class' => 'form-control'
										));
								?>
							</div>
							<div class="form-group">
								<?php echo form_label(lang('forms_description'),'forms_description'); ?>
								<p class="form-text"><?php echo nl2br(lang('forms_description_help')); ?></p>
								<?php
									echo form_textarea(array(
											'name'	=> 'forms_description',
											'value'	=> set_value('forms_description'),
											'class' => 'form-control'
										));
								?>
							</div>
							<?php if ( ! empty($question_id) ): ?>
							<div class="form-group">
								<?php echo form_label(lang('forms_parent_id'),'forms_parent_id'); ?>
								<p class="form-text"><?php echo nl2br(lang('forms_parent_id_help')); ?></p>
								<?php
									// $parents = array( 0 => 'Null');
									echo form_dropdown('forms_parent_id', $question_parents, set_value('forms_parent_id'),array(
										'class' => 'form-control'
									));
								?>
							</div>
							<?php endif; ?>
							<div class="form-group">
								<?php echo form_label(lang('forms_field'),'forms_field'); ?>
								<p class="form-text"><?php echo nl2br(lang('forms_field_help')); ?></p>
								<?php
									echo form_dropdown('forms_field', $field_types, set_value('forms_field'),array(
										'class' => 'form-control'
									));
								?>
							</div>
							<?php /*
							<div class="form-group">
								<?php echo form_label(lang('forms_validate'),'forms_validate'); ?>
								<p class="form-text"><?php echo nl2br(lang('forms_validate_help')); ?></p>
								<?php
									$parents = array( 0 => 'Null');
									echo form_dropdown('forms_validate', $parents, set_value('forms_validate',0),array(
										'class' => 'form-control'
									));
								?>
							</div>
							*/ ?>
							<?php echo form_close(); ?>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /.box -->
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');