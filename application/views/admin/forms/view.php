<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="float-right btn-group">
				<?php
				if( isset($form) && isset($page_id) && $form->type === 'checklist' )
				{
					echo icon_anchor('admin/forms/delete/checklist', array($page_id,$form_id), '<i class="fa fa-trash" aria-hidden="true"></i>',
						array(
						'title' => lang('delete') . ' ' . lang('forms_checklist'),
						'class' => 'btn btn-danger',
						)
					);
				}
				if( in_array('form',$buttons,TRUE) ) 
				{
					echo icon_anchor('admin/forms/create/form', array($page_id,$sub_page_id), '<i class="fa fa-plus" aria-hidden="true"></i>',
						array(
						'title' => lang('add') . ' ' . lang('forms_form'),
						'class' => 'btn btn-primary',
						)
					);
				}
				
				if( in_array('page',$buttons,TRUE) && (!isset($page_current) || $page_current->type !== '1') ) 
				{
					echo icon_anchor('admin/forms/create/page', array($form_id,$page_id,$sub_page_id), '<i class="fa fa-file" aria-hidden="true"></i>',
						array(
						'title' => lang('add') . ' ' . lang('forms_page'),
						'class' => 'btn btn-primary',
						)
					);
				}
				
				if( in_array('question',$buttons,TRUE) && count($forms) === 0 ) 
				{
					$question = array(
						'00000000-0000-0000-0000-000000000000',
						$page_current_id,
						$form_id
					);
					if( !in_array($page_id,$question) )
						$question[] = $page_id;
					if( !in_array($sub_page_id,$question) )
						$question[] = $sub_page_id;
					
					echo icon_anchor('admin/forms/question/create', $question, '<i class="fa fa-question" aria-hidden="true"></i>',
						array(
						'title' => lang('add') . ' ' . lang('forms_question'),
						'class' => 'btn btn-primary',
						)
					);
				}
				
				if( isset($page_id) && isset($sub_page_id) )
				{
					echo icon_anchor('admin/forms/view', array($form_id,$page_id), '<i class="fa fa-reply" aria-hidden="true"></i>',
						array(
						'title' => lang('cancel'),
						'class' => 'btn btn-default'
						)
					);
				}
				
				if( isset($page_id) && !isset($sub_page_id) )
				{
					echo icon_anchor('admin/forms/view', array($form_id), '<i class="fa fa-reply" aria-hidden="true"></i>',
						array(
						'title' => lang('cancel'),
						'class' => 'btn btn-default'
						)
					);
				}
				
				if( !isset($page_id) && !isset($sub_page_id) )
				{
					echo anchor('admin/forms', '<i class="fa fa-reply" aria-hidden="true"></i>',
						array(
						'title' => lang('cancel'),
						'class' => 'btn btn-default'
						)
					);
				}
				?>
			</div>
			<h1>
				<?php echo lang('forms_forms'); ?>
				<small></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title">
								<?php
								if( isset( $form ) )
								{
									echo html_escape($form->name);
									
									if( isset($page) )
									{
										echo ' > ' . html_escape($page->name);
									}
									
									if( isset($sub_page) )
									{
										echo ' > ' . html_escape($sub_page->name);
									}
								}
								else
								{
									echo lang('forms_forms');
								}
								?>
							</h3>
						</div>
						<div class="box-body no-padding">
							<table class="table table-striped no-data-table">
								<colgroup>
									<col>
									<col width="85px;">
								</colgroup>
								<tbody>
								<?php foreach($forms as $f): ?>
									<tr>
										<td>
											<?php
												// var_dump($f);
												if( !$form_id )
												{
													echo safe_anchor('admin/forms/view', array($f->form_id), $f->name);
												}
												else if( !$page_id && !$sub_page_id )
												{
													if( $f->type === '1' )
													{
														echo safe_anchor('admin/forms/questions', array($f->page_id,$f->form_id), $f->name);
													}
													else
													{
														echo safe_anchor('admin/forms/view', array($f->form_id,$f->page_id), $f->name);
													}
												}
												else if( $page_id && !$sub_page_id )
												{
													if( $f->type === '1' )
													{
														echo safe_anchor('admin/forms/questions', array($f->page_id,$f->form_id,$f->parent_id), $f->name);
													}
													else
													{
														echo safe_anchor('admin/forms/view', array($f->form_id,$f->parent_id,$f->page_id), $f->name);
													}
												}
												else
												{
													echo safe_anchor('admin/forms/questions', array($f->page_id,$form_id,$page_id,$sub_page_id), $f->name);
													// echo $f->form_id . ' - ' . html_escape($f->name);
												}
											?>
										</td>
										<td>
											<?php //var_dump($f); ?>
											<div class="btn-group btn-group-sm">
												<?php
												if( isset($f->page_id) )
												{
													echo icon_anchor('admin/forms/edit/page', array_merge(array($f->page_id), $page_url), '<i class="fa fa-pencil" aria-hidden="true"></i>',
														array(
														'title' => lang('edit'),
														'class' => 'btn btn-default',
														)
													);												
												}
												else
												{
													echo icon_anchor('admin/forms/edit/form', $f->form_id, '<i class="fa fa-pencil" aria-hidden="true"></i>',
														array(
														'title' => lang('edit'),
														'class' => 'btn btn-default',
														)
													);		
												}
												if( isset($form->type) && $form->type ==='checklist' )
												{
													echo icon_anchor('admin/forms/survey', array_merge(array($f->page_id), $page_url), '<i class="fa fa-calendar" aria-hidden="true"></i>',
														array(
														'title' => lang('calender'),
														'class' => 'btn btn-default',
														)
													);	
												}
												?>
											</div>
										</td>
									</tr>
								<?php endforeach; ?>
								</tbody>
							</table>
						</div>
					</div>
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');