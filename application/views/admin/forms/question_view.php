<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="float-right btn-group">
				<?php
				if( empty($questions) && $form->type === 'checklist' )
				{
					echo icon_anchor('admin/forms/delete/checklist', $question_url, '<i class="fa fa-trash" aria-hidden="true"></i>',
						array(
						'title' => lang('delete') . ' ' . lang('forms_checklist'),
						'class' => 'btn btn-danger',
						)
					);
				}
				// var_dump($complete_url,$question_url);
				if( in_array('question',$buttons,TRUE) ) 
				{
					echo icon_anchor('admin/forms/question/create/00000000-0000-0000-0000-000000000000', $question_url, '<i class="fa fa-plus" aria-hidden="true"></i>',
						array(
						'title' => lang('add') . ' ' . lang('forms_question'),
						'class' => 'btn btn-primary',
						)
					);
				}
				
				if( isset($page_url) ) 
				{
					echo icon_anchor('admin/forms/view', $page_url, '<i class="fa fa-reply" aria-hidden="true"></i>',
						array(
						'title' => lang('cancel'),
						'class' => 'btn btn-default',
						)
					);
				}
				?>
			</div>
			<h1>
				<?php echo lang('forms_question'); ?>
				<small></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title">
								<?php
								if( isset( $form ) )
								{
									echo html_escape($form->name);
									
									if( isset($page) )
									{
										echo ' > ' . html_escape($page->name);
									}
									
									if( isset($sub_page) )
									{
										echo ' > ' . html_escape($sub_page->name);
									}
									if( isset($page_current) )
									{
										echo ' > ' . html_escape($page_current->name);
									}
								}
								else
								{
									echo lang('forms_question');
								}
								?>
							</h3>
						</div>
						<div class="box-body no-padding">
							<table class="table table-striped no-data-table">
								<colspan>
									<col>
									<col width="200px;">
									<col width="150px;">
								</colspan>
								<tbody>
								<?php foreach($questions as $q): ?>
									<tr>
										<td><?php echo html_escape($q->name); ?></td>
										<td><?php echo $field_types[$q->field]; ?></td>
										<td>
											<div class="btn-group btn-group-sm">
												<?php
												echo icon_anchor('admin/forms/question/edit', array($q->question_id) + $complete_url, '<i class="fa fa-pencil" aria-hidden="true"></i>',
													array(
													'title' => lang('forms_question'),
													'class' => 'btn btn-default',
													)
												);
												
												echo icon_anchor('admin/forms/question/children', array($q->question_id) + $complete_url, '<i class="fa fa-list" aria-hidden="true"></i>',
													array(
													'title' => lang('forms_parents_id'),
													'class' => 'btn btn-default',
													)
												);
												
												if( in_array($q->field, array('dropdown','checkbox','radio')) ) {
													echo icon_anchor('admin/forms/options', array($q->question_id) + $complete_url, '<i class="fa fa-cog" aria-hidden="true"></i>',
														array(
														'title' => lang('forms_values'),
														'class' => 'btn btn-default',
														)
													);
												}

												if($q->field === 'table') {
													echo icon_anchor('admin/forms/question/table', array($q->question_id) + $complete_url, '<i class="fa fa-table" aria-hidden="true"></i>',
														array(
														'title' => lang('forms_table'),
														'class' => 'btn btn-default',
														)
													);
												}
												
												if( empty($questions_children) || !isset($questions_children[$q->question_id]) )
												{
													echo icon_anchor('admin/forms/question/delete', array($q->question_id) + $complete_url, '<i class="fa fa-trash" aria-hidden="true"></i>',
														array(
														'title' => lang('delete') . ' ' . lang('forms_question'),
														'class' => 'btn btn-danger',
														)
													);
												}
												?>
											</div>
										</td>
									</tr>
								<?php endforeach; ?>
								</tbody>
							</table>
						</div>
					</div>
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');