<?php $this->load->view('template/header-contextmenu'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php
				echo form_button(array(
					'type' => 'submit',
					'form' => 'form-company-group',
					'class' => 'btn btn-primary',
					'title' => lang('save'),
					'content' => '<i class="fa fa-save"></i>'
				));
				if( isset($question->parent_id) )
				{
					echo icon_anchor('admin/forms/question/children', [$question->parent_id] + $complete_url, '<i class="fa fa-reply" aria-hidden="true"></i>',
						array(
						'title' => lang('cancel'),
						'class' => 'btn btn-default',
						)
					);
				}
				else
				{
					echo icon_anchor('admin/forms/questions', $question_url, '<i class="fa fa-reply" aria-hidden="true"></i>',
						array(
						'title' => lang('cancel'),
						'class' => 'btn btn-default',
						)
					);
				}
				?>
			</div>
			<h1>
				<?php echo lang('edit') . ' ' . mb_strtolower(lang('forms_question')); ?>
				<small></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title">
								<?php if( $form_id )
								{
									echo html_escape($form->name);
									
									if( isset($page) )
									{
										echo ' > ' . html_escape($page->name);
									}
									
									if( isset($sub_page) )
									{
										echo ' > ' . html_escape($sub_page->name);
									}
									if( isset($page_current) )
									{
										echo ' > ' . html_escape($page_current->name);
									}
									if( isset($question_parent) )
									{
										echo ' > ' . html_escape($question_parent->name);
									}
									if( isset($question) )
									{
										echo ' > ' . html_escape($question->name);
									}
								}
								?>
							</h3>
						</div>
						<div class="box-body">
							<p><?php echo lang('forms_form_desc'); ?></p>
							<?php 
								echo validation_errors();
								echo form_open(NULL,array(
									'id' => 'form-company-group',
									'autocomplete' => 'off'
								));
							?>
							<?php
							$settings = json_decode($question->settings);
							if(json_last_error() === JSON_ERROR_NONE)
							{
								// var_dump($settings);
								echo '<div class="form-group">';
								echo '<table id="contextmenu" class="table table-bordered table-striped table-hover no-data-table">';
								if( ! empty($settings->header) && ! empty($settings->type) )
								{
									echo '<tr>';
									$ih = 0;
									foreach($settings->header as $h)
									{
										echo '<th class="' . $settings->type[$ih] . '">';
										echo form_input(array(
											'name'	=> 'form_table_header[]',
											'value'	=> $h,
											'class' => 'form-control'
										));
										echo form_input(array(
											'name'	=> 'form_table_type[]',
											'value'	=> $settings->type[$ih],
											'type'  => 'hidden'
										));
										echo '</th>';
										++$ih;
									}
									echo '</tr>';
								}
								if( !empty($settings->body) )
								{
									foreach($settings->body as $ii => $body)
									{
										echo '<tr>';

										if( ! empty($body) )
										{
											foreach($body as $i => $b)
											{
												echo '<td>';
												if( VALID_UUIDv4($b) OR $b === '00000000-0000-0000-0000-000000000000' )
												{
													echo form_dropdown('form_table_body[]', $page_options, $b, array(
														'class' => 'form-control'
													));
												}
												else
												{
													echo form_input(array(
														'name'	=> 'form_table_body[]',
														'value'	=> $b,
														'class' => 'form-control'
													));
												}
												echo '</td>';
											}											
										}
										else
										{
											echo '<td>'.form_input(array(
												'name'	=> 'form_table_body[]',
												'class' => 'form-control'
											)).'</td>';
										}

										echo '</tr>';
									}
								}
								echo '</table>';
								echo '</div>';
							}
							?>
							<?php echo form_close(); ?>
							<div id="hiddenTableDropdown" style="display:none">
								<?php
									echo form_dropdown('form_table_body[]', $page_options, NULL, array(
										'class' => 'form-control'
									));
								?>
							</div>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /.box -->
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer-contextmenu');