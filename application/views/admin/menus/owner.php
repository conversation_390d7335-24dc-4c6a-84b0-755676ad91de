<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php
				echo form_button(array(
					'type' => 'submit',
					'form' => 'form-company-group',
					'class' => 'btn btn-primary',
					'title' => lang('save'),
					'content' => '<i class="fa fa-save"></i>'
				));
				
				if( isset($folder_id) OR isset($sub_folder_id) )
				{
					echo icon_anchor('admin/menus/view', array($folder_id,$sub_folder_id), '<i class="fa fa-reply" aria-hidden="true"></i>',
						array(
						'title' => lang('cancel'),
						'class' => 'btn btn-default'
						)
					);
				}
				
				if( !isset($folder_id) && !isset($sub_folder_id) )
				{
					echo anchor('admin/menus', '<i class="fa fa-reply" aria-hidden="true"></i>',
						array(
						'title' => lang('cancel'),
						'class' => 'btn btn-default'
						)
					);
				}
				?>
			</div>
			<h1>
				<?php echo lang('menus_menus'); ?>
				<small>
					<?php echo isset($folder_id)     ? $folder->name . ' > ' : ''; ?>
					<?php echo isset($sub_folder_id) ? $sub_folder->name . ' > ' : ''; ?>
					<?php echo $menu->name; ?>
				</small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('edit') . ' ' . mb_strtolower(lang('menus_owner'))	; ?></h3>
						</div>
						<div class="box-body">
							<p><?php echo lang('menus_menu_desc'); ?></p>
							<?php 
								echo validation_errors();
								echo form_open(NULL,array(
									'id' => 'form-company-group',
									'autocomplete' => 'off'
								));
							?>
							<div class="form-group">
								<?php echo form_label(lang('menus_owner'),'menus_owner'); ?>
								<p class="form-text"><?php echo nl2br(lang('menus_owner_help')); ?></p>
								<p class="form-text"><?php echo nl2br(lang('menus_owner_help_extended')); ?></p>
								<?php
									echo form_dropdown('menus_owner', $users['owner']['available'], set_value('menus_owner',$menu->owner),array(
										'class' => 'form-control'
									));
								?>
							</div>
							<?php echo form_close(); ?>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /.box -->
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');