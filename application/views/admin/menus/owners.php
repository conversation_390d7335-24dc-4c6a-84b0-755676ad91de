<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php
				echo form_button(array(
					'type' => 'submit',
					'form' => 'form-company-group',
					'class' => 'btn btn-primary',
					'title' => lang('save'),
					'content' => '<i class="fa fa-save"></i>'
				));
				echo anchor('admin/groups', '<i class="fa fa-reply" aria-hidden="true"></i>',
					array(
					'title' => lang('cancel'),
					'class' => 'btn btn-default'
					)
				);
				?>
			</div>
			<h1>
				<?php echo lang('menus_owner'); ?>
				<small><?php echo $user->name; ?></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('edit') . ' ' . mb_strtolower(lang('menus_owner')); ?></h3>
						</div>
						<div class="box-body">
							<p><?php echo lang('groups_group_desc'); ?></p>
							<?php 
								echo validation_errors();
								echo form_open(NULL,array(
									'id' => 'form-company-group',
									'autocomplete' => 'off'
								));
							?>
							<?php echo form_label(lang('menus_owner'),'owner_menu') . ' - ' . $user->name; ?>
							<p class="form-text"><?php echo nl2br(lang('menus_owner_help')); ?></p>
							<div class="document-tree-open">
								<ul>
									<?php
									foreach($move['menu'][0] as $menu_id => $menu)
									{
										if( isset($move['menu'][$menu_id]) )
										{
											echo '
											<li>
												' . $menu->name . '
												<ul>
											';
											foreach($move['menu'][$menu_id] as $parent)
											{
												$parent->name = preg_replace('/^[0-9\.\s]+/u', '',$parent->name);
												// In case it's a folder
												if($parent->type == 1)
												{
													if(	isset($move['menu'][$parent->menu_id]) )
													{
														echo '
														<li>
															<strong>
																' . $parent->name . '
															</strong>
															<ul>
														';
														// Echo $parent
														foreach($move['menu'][$parent->menu_id] as $child)
														{
															echo '
															<li>
																<label>
																	<i>' . form_checkbox('owner_menu[]', $child->menu_id, set_checkbox('owner_menu', $child->menu_id, $child->owner === $user->user_id)) . $child->name . '</i>
																</label>
															</li>
															';
														}
														echo '</ul></li>';
														// Close $parent
													}
													
												}
												else
												{
													echo '
													<li>
														<label>
															<i>' . form_checkbox('owner_menu[]', $parent->menu_id, set_checkbox('owner_menu', $parent->menu_id, $parent->owner === $user->user_id)) . $parent->name . '</i>
														</label>
													</li>
													';
													// Echo $parent, that's not a folder
												}
											}
											echo '</ul></li>';
										}
									}
									?>
								</ul>
							</div>
							<?php echo form_close(); ?>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /.box -->
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');