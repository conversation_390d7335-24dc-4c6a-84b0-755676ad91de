<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php
				echo form_button(array(
					'type' => 'submit',
					'form' => 'form-company-group',
					'class' => 'btn btn-primary',
					'title' => lang('save'),
					'content' => '<i class="fa fa-save"></i>'
				));
				echo anchor('admin/groups', '<i class="fa fa-reply" aria-hidden="true"></i>',
					array(
					'title' => lang('cancel'),
					'class' => 'btn btn-default'
					)
				);
				?>
			</div>
			<h1>
				<?php echo lang('menus_menu_rights'); ?>
				<small><?php echo lang('groups_group'); ?> > <?php echo $group->name; ?></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('edit'); ?></h3>
						</div>
						<div class="box-body">
							<p><?php echo lang('groups_group_desc'); ?></p>
							<?php 
								echo validation_errors();
								echo form_open(NULL,array(
									'id' => 'form-company-group',
									'autocomplete' => 'off'
								));
								// var_dump($group);
							?>
							<?php if( $group->type === 'position' ): ?>
								<?php echo form_label(lang('groups_type_position'),'rights_menu'); ?>
								<p class="form-text"><?php echo nl2br(lang('menus_type_position_help')); ?></p>
							<?php else: ?>
								<?php echo form_label(lang('groups_type_department'),'rights_menu'); ?>
								<p class="form-text"><?php echo nl2br(lang('menus_type_department_help')); ?></p>
							<?php endif; ?>
							<div class="document-tree-open">
								<ul>
									<?php
									foreach($move['menu'][0] as $menu_id => $menu)
									{
										// echo '<li><span>' . html_escape($menu->name) . '</span><ul>';
										echo '
										<li>
											<label>
												' . form_checkbox('rights_menu[]', $menu->menu_id, set_checkbox('rights_menu', $menu->menu_id, in_array($menu->menu_id,$groups_checked))) . $menu->name . '
											</label>
											<ul>
										';
										// var_dump($menu_id);exit;
										if( isset($move['menu'][$menu_id]) )
										{
											foreach($move['menu'][$menu_id] as $parent)
											{
												$parent->name = preg_replace('/^[0-9\.\s]+/u', '',$parent->name);
												// In case it's a folder
												if($parent->type == 1)
												{
													if(	isset($move['menu'][$parent->menu_id]) )
													{
														echo '
														<li>
															<label>
																' . form_checkbox('rights_menu[]', $parent->menu_id, set_checkbox('rights_menu', $parent->menu_id, in_array($parent->menu_id,$groups_checked))) . $parent->name . '
															</label>
															<ul>
														';
														// Echo $parent
														foreach($move['menu'][$parent->menu_id] as $child)
														{
															echo '
															<li>
																<label>
																	<i>' . form_checkbox('rights_menu[]', $child->menu_id, set_checkbox('rights_menu', $child->menu_id, in_array($child->menu_id,$groups_checked))) . $child->name . '</i>
																</label>
															</li>
															';
														}
														echo '</ul></li>';
														// Close $parent
													}
													
												}
												else
												{
													echo '
													<li>
														<label>
															<i>' . form_checkbox('rights_menu[]', $parent->menu_id, set_checkbox('rights_menu', $parent->menu_id, in_array($parent->menu_id,$groups_checked))) . $parent->name . '</i>
														</label>
													</li>
													';
													// Echo $parent, that's not a folder
												}
											}
										}
										echo '</ul></li>';
									}
									?>
								</ul>
							</div>
							<?php echo form_close(); ?>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /.box -->
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');