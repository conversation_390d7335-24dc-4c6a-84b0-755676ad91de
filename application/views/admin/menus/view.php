<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="float-right btn-group">
				<?php
				if( empty($menus) )
				{
					echo icon_anchor('admin/menus/delete', array($sub_folder_id,$folder_id) ,'<i class="fa fa-trash" aria-hidden="true"></i>',
						array(
						'title' => lang('delete'),
						'class' => 'btn btn-danger',
						)
					);												
				}
				
				if( in_array('menu',$buttons,TRUE) ) 
				{
					echo icon_anchor('admin/menus/create/menu', array($folder_id,$sub_folder_id), '<i class="fa fa-folder" aria-hidden="true"></i>',
						array(
						'title' => lang('add') . ' ' . lang('menus_menu'),
						'class' => 'btn btn-primary',
						)
					);
				}
				
				if( in_array('folder',$buttons,TRUE) && !$sub_folder_id ) 
				{
					echo icon_anchor('admin/menus/create/folder', array($folder_id,$sub_folder_id),'<i class="fa fa-plus" aria-hidden="true"></i>',
						array(
						'title' => lang('add') . ' ' . lang('menus_folder'),
						'class' => 'btn btn-primary',
						)
					);
				}
				
				if( in_array('folder',$buttons,TRUE) && $sub_folder_id )
				{
					echo icon_anchor('admin/menus/move', array($sub_folder_id,$folder_id), '<i class="fa fa-random" aria-hidden="true"></i>',
						array(
						'title' => lang('move'),
						'class' => 'btn btn-primary'
						)
					);
				}
				
				if( in_array('link',$buttons,TRUE) ) 
				{
					echo icon_anchor('admin/menus/create/link', array($folder_id,$sub_folder_id),'<i class="fa fa-link" aria-hidden="true"></i>',
						array(
						'title' => lang('add') . ' ' . lang('menus_link'),
						'class' => 'btn btn-primary',
						)
					);
				}
				
				if( in_array('app',$buttons,TRUE) ) 
				{	
					echo icon_anchor('admin/menus/create/app', array($folder_id,$sub_folder_id),'<i class="fa fa-cubes" aria-hidden="true"></i>',
						array(
						'title' => lang('add') . ' ' . lang('menus_app'),
						'class' => 'btn btn-primary',
						)
					);
				}
				
				if( isset($folder_id) && isset($sub_folder_id) )
				{
					echo icon_anchor('admin/menus/view', array($folder_id), '<i class="fa fa-reply" aria-hidden="true"></i>',
						array(
						'title' => lang('cancel'),
						'class' => 'btn btn-default'
						)
					);
				}
				
				if( isset($folder_id) && !isset($sub_folder_id) )
				{
					echo anchor('admin/menus', '<i class="fa fa-reply" aria-hidden="true"></i>',
						array(
						'title' => lang('cancel'),
						'class' => 'btn btn-default'
						)
					);
				}
				?>
			</div>
			<h1>
				<?php echo lang('menus_menus'); ?>
				<small></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title">
								<?php
								if( isset( $folder ) )
								{
									echo lang('menus_folder') . ': ' . html_escape($folder->name);
									
									if( isset($sub_folder) )
									{
										echo ' > ' . html_escape($sub_folder->name);
									}
								}
								else
								{
									echo lang('menus_folders');
								}
								?>
							</h3>
						</div>
						<div class="box-body no-padding">
							<table class="table table-striped no-data-table">
								<colgroup>
									<col>
									<col width="200px;">
								</colgroup>
								<tbody>
								<?php foreach($menus as $menu): ?>
									<tr>
										<td>
											<?php
												if( $menu->type == Menus::$types['folder'] )
												{
													echo safe_anchor('admin/menus/view', array($menu->parent_id,$menu->menu_id), $menu->name);
												}
												else
												{
													echo html_escape($menu->name);
												}
											?>
										</td>
										<td>
											<div class="btn-group btn-group-sm">
											<?php
											$type = array_search($menu->type,Menus::$types); 
											if( $menu->type == Menus::$types['folder'] ) {
												echo icon_anchor('admin/menus/view', array($menu->parent_id,$menu->menu_id), '<i class="fa fa-folder" aria-hidden="true"></i>',
												array(
												'title' => lang('folder_folders'),
												'class' => 'btn btn-default',
												)
												);
											} else {
											echo icon_anchor('folder', $menu->menu_id, '<i class="fa fa-folder" aria-hidden="true"></i>',
												array(
												'title' => lang('folder_folders'),
												'class' => 'btn btn-default',
												)
											);
											}
											echo icon_anchor('admin/menus/update', array($type,$menu->menu_id,$folder_id,$sub_folder_id), '<i class="fa fa-pencil" aria-hidden="true"></i>',
												array(
												'title' => lang('edit'),
												'class' => 'btn btn-default',
												)
											);
											if( $menu->type == 0 ) 
											{
												echo icon_anchor('admin/menus/owner', array($menu->menu_id,$folder_id,$sub_folder_id) ,'<i class="fa fa-user" aria-hidden="true"></i>',
													array(
													'title' => lang('menus_owner'),
													'class' => 'btn btn-default',
													)
												);
											}
											if( $menu->type != 1 )
											{
												echo icon_anchor('admin/menus/move', array($menu->menu_id,$folder_id,$sub_folder_id), '<i class="fa fa-random" aria-hidden="true"></i>',
													array(
													'title' => lang('move'),
													'class' => 'btn btn-default'
													)
												);
											}
											if( ! in_array($menu->menu_id,array_keys($folders)) && $menu->type != 1 )
											{
												echo icon_anchor('admin/menus/delete', array($menu->menu_id,$folder_id,$sub_folder_id) ,'<i class="fa fa-trash" aria-hidden="true"></i>',
													array(
													'title' => lang('delete'),
													'class' => 'btn btn-danger',
													)
												);												
											}

											?>
											</div>
										</td>
									</tr>
								<?php endforeach; ?>
								</tbody>
							</table>
						</div>
					</div>
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');