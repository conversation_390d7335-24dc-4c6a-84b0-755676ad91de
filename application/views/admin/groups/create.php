<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php
				echo form_button(array(
					'type' => 'submit',
					'form' => 'form-company-group',
					'class' => 'btn btn-primary',
					'title' => lang('save'),
					'content' => '<i class="fa fa-save"></i>'
				));
				echo anchor('admin/groups', '<i class="fa fa-reply" aria-hidden="true"></i>',
					array(
					'title' => lang('cancel'),
					'class' => 'btn btn-default'
					)
				);
				?>
			</div>
			<h1>
				<?php echo lang('groups_groups'); ?>
				<small></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('add') . ' ' . lang('groups_group'); ?></h3>
						</div>
						<div class="box-body">
							<p><?php echo lang('groups_group_desc'); ?></p>
							<?php 
								echo validation_errors();
								echo form_open(NULL,array(
									'id' => 'form-company-group',
									'autocomplete' => 'off'
								));
							?>
							<div class="form-group">
							<?php
								echo form_label(lang('groups_name'),'groups_name');
								echo form_input(array(
										'name'	=> 'groups_name',
										'value'	=> set_value('groups_name'),
										'class' => 'form-control'
									));
							?>
							</div>
							<div class="form-group">
							<?php
								echo form_label(lang('groups_type'),'groups_type');
								echo form_dropdown('groups_type', $groups_type, set_value('groups_type','standard'),array(
									'class' => 'form-control'
								));
							?>
								<p class="form-text">
									<?php echo nl2br(lang('groups_type_position_help')); ?><br/>
									<?php echo nl2br(lang('groups_type_department_help')); ?><br/>
									<?php if( $this->auth_god ): ?>
										<?php echo nl2br(lang('groups_type_security_help')); ?>
									<?php endif; ?>
								</p>
							</div>
							<?php
								
							echo form_close();
							?>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /.box -->
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');