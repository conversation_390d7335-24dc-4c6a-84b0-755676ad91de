<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php
				echo anchor('admin/groups/create', '<i class="fa fa-plus" aria-hidden="true"></i>',
					array(
					'title' => lang('add') . ' ' . lang('groups_group'),
					'class' => 'btn btn-primary',
					)
				);
				?>
			</div>
			<h1>
				<?php echo lang('groups_groups'); ?>
				<small></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="nav-tabs-custom">
						<ul class="nav nav-tabs">
							<li><a href="#position" data-toggle="tab" aria-expanded="true" class="active"><?php echo lang('groups_type_position'); ?></a></li>
							<li><a href="#department" data-toggle="tab" aria-expanded="false"><?php echo lang('groups_type_department'); ?></a></li>
							<?php if( $this->auth_god ): ?>
								<li><a href="#security" data-toggle="tab" aria-expanded="false"><?php echo lang('groups_type_security'); ?></a></li>
							<?php endif; ?>
						</ul>
						<div class="tab-content no-padding">
							<div class="tab-pane active" id="position">
								<?php
								if( empty($groups['position']) )
								{
									echo lang('groups_empty');
								}
								else
								{
								?>
								<table class="table table-striped no-data-table">
									<colgroup>
										<col>
										<col width="82px;">
									</colgroup>
									<tbody>
									<?php foreach($groups['position'] as $group_id => $group_name): ?>
										<tr>
											<td><?php echo safe_anchor('admin/menus/rights', $group_id, $group_name); ?></td>
											<td align="right">
												<div class="btn-group btn-group-sm">
												<?php
													if( ! in_array($group_id, $user_group) )
													{
														echo icon_anchor('admin/groups/delete', $group_id ,'<i class="fa fa-trash" aria-hidden="true"></i>',
															array(
															'title' => lang('delete') . ' ' . lang('groups_group'),
															'class' => 'btn btn-danger',
															)
														);
													}
													echo icon_anchor('admin/groups/update', $group_id ,'<i class="fa fa-pencil" aria-hidden="true"></i>',
														array(
														'title' => lang('edit') . ' ' . lang('groups_name'),
														'class' => 'btn btn-default',
														)
													);
												?>
												</div>
											</td>
										</tr>
									<?php endforeach; ?>
									</tbody>
								</table>
								<?php
								}
								?>
							</div>
							<!-- /.tab-pane -->
							<div class="tab-pane" id="department">
								<?php
								if( empty($groups['department']) )
								{
									echo lang('groups_empty');
								}
								else
								{
								?>
								<table class="table table-striped no-data-table">
									<colgroup>
										<col>
										<col width="82px;">
									</colgroup>
									<tbody>
									<?php foreach($groups['department'] as $group_id => $group_name): ?>
										<tr>
											<td><?php echo safe_anchor('admin/menus/rights', $group_id, $group_name); ?></td>
											<td align="right">
												<div class="btn-group btn-group-sm">
												<?php
													// var_dump($group_id,$user_group);
													if( ! in_array($group_id, $user_group) && ! in_array($group_id, $deviation_department) && ! in_array($group_id, $risk_assessments_department) )
													{
														echo icon_anchor('admin/groups/delete', $group_id ,'<i class="fa fa-trash" aria-hidden="true"></i>',
															array(
															'title' => lang('delete') . ' ' . lang('groups_group'),
															'class' => 'btn btn-danger',
															)
														);
													}
													echo icon_anchor('admin/groups/update', $group_id ,'<i class="fa fa-pencil" aria-hidden="true"></i>',
														array(
														'title' => lang('edit') . ' ' . lang('groups_name'),
														'class' => 'btn btn-default',
														)
													);
												?>
												</div>
											</td>
										</tr>
									<?php endforeach; ?>
									</tbody>
								</table>
								<?php
								}
								?>
							</div>
							<!-- /.tab-pane -->
							<?php if( $this->auth_god ): ?>
								<div class="tab-pane" id="security">
									<?php
									if( empty($groups['security']) )
									{
										echo lang('groups_empty');
									}
									else
									{
									?>
									<table class="table table-striped no-data-table">
										<tbody>
										<?php foreach($groups['security'] as $group_id => $group_name): ?>
											<tr>
												<td><?php echo safe_anchor('admin/groups/update', $group_id, $group_name); ?></td>
											</tr>
										<?php endforeach; ?>
										</tbody>
									</table>
									<?php
									}
									?>
								</div>
								<!-- /.tab-pane -->
							<?php endif; ?>
						</div>
					<!-- /.tab-content -->
					</div>
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');