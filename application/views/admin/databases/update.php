<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php
				echo form_button(array(
					'type' => 'submit',
					'form' => 'form-company-group',
					'class' => 'btn btn-primary',
					'title' => lang('save'),
					'content' => '<i class="fa fa-save"></i>'
				));
				echo anchor('admin/databases', '<i class="fa fa-reply" aria-hidden="true"></i>',
					array(
					'title' => lang('cancel'),
					'class' => 'btn btn-default'
					)
				);
				?>
			</div>
			<h1>
				<?php echo lang('databases_database'); ?>
				<small></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('update') . ' ' . lang('databases_database'); ?></h3>
						</div>
						<div class="box-body">
							<p><?php echo lang('databases_database_desc'); ?></p>
							<?php 
								echo validation_errors();
								echo form_open(NULL,array(
									'id' => 'form-company-group',
									'autocomplete' => 'off'
								));
							?>
							<div class="form-group">
							<?php
								echo form_label(lang('databases_name'),'databases_name');
								echo form_input(array(
										'name'	=> 'databases_name',
										'value'	=> set_value('databases_name',$database->name),
										'class' => 'form-control'
									));
							?>
							</div>
							<div class="form-group">
							<?php
								echo form_label(lang('databases_default'),'databases_default');
								$default = array(0 => lang('no'), 1 => lang('yes'));
								echo form_dropdown('databases_default', $default, set_value('databases_default',$database->default),array(
									'class' => 'form-control'
								));
							?>
							</div>
							<?php
								
							echo form_close();
							?>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /.box -->
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');