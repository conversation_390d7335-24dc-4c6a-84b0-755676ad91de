<script type="text/javascript">
	let widgets = <?php echo isset($settings['widgets']) ? json_encode($settings['widgets']) : "[]"; ?>;
	let widgetsData = {}
	window.addEventListener("load", function (){
		for (let i = 0; i < widgets.length; i++) {
			let id = ([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g, c =>
				(c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16)
			);
			widgetsData[id] = widgets[i];
			$("#multiWidgets").append(`
			<div style="display: flex; margin-bottom: 10px" id="${id}">
				<input style="margin-left: 10px"value="${widgets[i].replaceAll('"', "'")}" type="text" name="widget_1" class="form-control float-right" onchange="changeData('${id}')">
				<button onclick="removeRow(event, '${id}')" class="btn btn-danger" title="Remove"><i class="fa fa-minus"></i></button>
			</div>
			`)
		}
		$("input[name=appearance_widgets]").val(JSON.stringify(Object.values(widgetsData)));
	}, false)
	function removeRow(e, id) {
		e.preventDefault();
		delete(widgetsData[id]);
		$('#' + id).remove();
		$("input[name=appearance_widgets]").val(JSON.stringify(Object.values(widgetsData)));
	}

	function changeData(id) {
		let v1 = $(`#${id} input`)[0].value;
		widgetsData[id] = v1;
		$("input[name=appearance_widgets]").val(JSON.stringify(Object.values(widgetsData)));
	}

	function addRow(e) {
		e.preventDefault();
		let id = ([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g, c =>
			(c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16)
		);
		widgetsData[id] = "";
		$("#multiWidgets").append(`
		<div style="display: flex; margin-bottom: 10px" id="${id}">
			<input style="margin-left: 10px" type="text" name="widget_1" class="form-control float-right" onchange="changeData('${id}')">
			<button onclick="removeRow(event, '${id}')" class="btn btn-danger" title="Remove"><i class="fa fa-minus"></i></button>
		</div>
		`)
	}
</script>
<?php $this->load->view('template/header-selectize-js'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php
				if(isset($settings['logo']['data']))
				{
					echo anchor('admin/appearance/delete', '<i class="fa fa-trash" aria-hidden="true"></i>',
						array(
						'title' => lang('delete'),
						'class' => 'btn btn-danger'
						)
					);
				}

				echo form_button(array(
					'type' => 'submit',
					'form' => 'form-company-group',
					'class' => 'btn btn-primary',
					'title' => lang('save'),
					'content' => '<i class="fa fa-save"></i>'
				));
				?>
			</div>
			<h1>
				<?php echo lang('appearance_appearance'); ?>
				<small></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-9">
					<?php
						echo validation_errors();
						echo form_open_multipart(NULL,array(
							'id' => 'form-company-group',
							'autocomplete' => 'off'
						));
					?>
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('appearance_logo'); ?></h3>
						</div>
						<div class="box-body">
							<div class="form-group">
								<?php echo form_label(lang('appearance_logo_position'),'appearance_logo_position'); ?>
								<p class="form-text"><?php echo nl2br(lang('appearance_logo_position_help')); ?></p>
								<div class="form-check">
									<label>
										<?php echo form_radio('appearance_logo_vertical', 0, set_radio('appearance_logo_vertical', 0, $settings['vertical'] == 0)); ?> <?php echo lang('appearance_logo_vertical_top'); ?>
									</label>
								</div>
								<div class="form-check">
									<label>
										<?php echo form_radio('appearance_logo_vertical', 1, set_radio('appearance_logo_vertical', 1, $settings['vertical'] == 1)); ?> <?php echo lang('appearance_logo_vertical_bottom'); ?>
									</label>
								</div>
								<hr/>
								<div class="form-check">
									<label>
										<?php echo form_radio('appearance_logo_horizontal', 0, set_radio('appearance_logo_horizontal', 0, $settings['horizontal'] == 0)); ?> <?php echo lang('appearance_logo_horizontal_left'); ?>
									</label>
								</div>
								<div class="form-check">
									<label>
										<?php echo form_radio('appearance_logo_horizontal', 1, set_radio('appearance_logo_horizontal', 1, $settings['horizontal'] == 1)); ?> <?php echo lang('appearance_logo_horizontal_center'); ?>
									</label>
								</div>
								<div class="form-check">
									<label>
										<?php echo form_radio('appearance_logo_horizontal', 2, set_radio('appearance_logo_horizontal', 2, $settings['horizontal'] == 2)); ?> <?php echo lang('appearance_logo_horizontal_right'); ?>
									</label>
								</div>
							</div>
							<div class="form-group">
								<?php echo form_label(lang('appearance_logo_size'),'appearance_logo_size'); ?>
								<p class="form-text"><?php echo nl2br(lang('appearance_logo_size_help')); ?></p>
								<div class="input-group">
									<?php
										echo form_input(array(
												'name'	=> 'appearance_logo_size',
												'value'	=> set_value('appearance_logo_size', $settings['size']),
												'class' => 'form-control'
											));
									?>
									<span class="input-group-prepend">px</span>
								</div>
							</div>
							<div class="form-group">
							<?php echo form_label(lang('appearance_logo_upload'),'file'); ?>
							<p class="form-text"><?php echo nl2br(lang('appearance_logo_upload_help')); ?></p>
							<?php
								echo form_upload(array(
										'name'	=> 'file',
										'class' => 'form-control'
									));
							?>
							</div>
							<div class="form-group" style="display: none">
								<?php echo form_label(lang('appearance_widget'),'appearance_widget'); ?>
								<p class="form-text"><?php echo lang('appearance_widget_help'); ?></p>
								<?php
									echo form_input('appearance_widget', set_value('appearance_widget',isset($settings['widget']) ? $settings['widget'] : '', FALSE),array(
										'class' => 'form-control'
									));
								?>
							</div>

							<hr/>
							<div class="box-header with-border">
								<h3 class="box-title"><?php echo lang('appearance_widget_title'); ?></h3>
							</div>
							<?php echo form_label(lang('appearance_logo_position'),'appearance_logo_position'); ?>
							<p class="form-text"><?php echo nl2br(lang('appearance_widget_position_help')); ?></p>
							<div class="form-check">
								<label>
									<?php echo form_radio('appearance_widget_vertical', 0, set_radio('appearance_widget_vertical', 0, $settings['widget_vertical'] == 0)); ?> <?php echo lang('appearance_logo_vertical_top'); ?>
								</label>
							</div>
							<div class="form-check">
								<label>
									<?php echo form_radio('appearance_widget_vertical', 1, set_radio('appearance_widget_vertical', 1, $settings['widget_vertical'] == 1)); ?> <?php echo lang('appearance_logo_vertical_bottom'); ?>
								</label>
							</div>
							
							<div class="form-group">
								<?php echo form_label(lang('appearance_widget'),'appearance_widget'); ?>
								<button onclick="addRow(event)" class="btn btn-success" title="Add" style="float: right">
										<i class="fa fa-plus"></i>
								</button>
								<p class="form-text"><?php echo lang('appearance_widget_help'); ?></p>
								<input type="hidden" name="appearance_widgets" value="" class="form-control">
								<div id="multiWidgets"></div>
							</div>
							<?php if ( $this->has_tasks ): ?>
								<div class="form-group">
									<?php echo form_label(lang('task_type_landing'),'appearance_tasktype'); ?>
									<p class="form-text"><?php echo lang('tasks_type_landing_help'); ?></p>
									<?php
										echo form_multiselect('appearance_tasktype[]', $tasktype_list, set_value('appearance_tasktype[]',isset($settings['tasktypes']) ? $settings['tasktypes'] : []),array(
											'class' => 'form-control',
											'id'    => 'appearance_tasktype'
										));
									?>
								</div>
							<?php endif; ?>
						</div>

						<hr/>
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('appearance_buttons_title'); ?></h3>
						</div>
						<?php echo form_label(lang('appearance_logo_position'),'appearance_logo_position'); ?>
						<p class="form-text"><?php echo nl2br(lang('appearance_buttons_position_help')); ?></p>
						<div class="form-check">
							<label>
								<?php echo form_radio('appearance_buttons_position', 0, set_radio('appearance_buttons_position', 0, $settings['buttons_vertical'] == 0)); ?> <?php echo lang('appearance_logo_vertical_top'); ?>
							</label>
						</div>
						<div class="form-check">
							<label>
								<?php echo form_radio('appearance_buttons_position', 1, set_radio('appearance_buttons_position', 1, $settings['buttons_vertical'] == 1)); ?> <?php echo lang('appearance_logo_vertical_bottom'); ?>
							</label>
						</div>
						<!-- /.box-body -->
						<div class="box-footer text-right">
							<?php
								echo form_button(array(
									'type' => 'submit',
									'form' => 'form-company-group',
									'class' => 'btn btn-primary',
									'title' => lang('save'),
									'content' => lang('save')
								));
							?>
						</div>
					</div>
					<!-- /.box -->
					<?php echo form_close(); ?>
				</div>
				<!-- /.col-md-4-->
				<?php if(isset($settings['logo']['data'])): ?>
				<div class="col-md-8">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('appearance_logo_preview'); ?></h3>
						</div>
						<div class="box-body">
							<?php if($settings['vertical'] == 1): ?>
								<div class="alert alert-success"><?php echo lang('pages_position_notice_board'); ?></div>
							<?php endif; ?>
							<p style="<?php echo $settings['logo']['align']; ?>">
								<img style="<?php echo $settings['logo']['size']; ?>" src="<?php echo $settings['logo']['data']; ?>" />
							</p>
							<?php if($settings['vertical'] == 0): ?>
								<div class="alert alert-success"><?php echo lang('pages_position_notice_board'); ?></div>
							<?php endif; ?>
						</div>
					</div>
				</div>
				<!-- /.col-md-8-->
				<?php endif; ?>
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer-task');