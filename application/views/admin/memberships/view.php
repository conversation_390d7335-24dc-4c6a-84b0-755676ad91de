<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php
				echo anchor('admin/memberships/create', '<i class="fa fa-plus" aria-hidden="true"></i>',
					array(
					'title' => lang('add') . ' ' . lang('memberships_membership'),
					'class' => 'btn btn-primary',
					)
				);
				?>
			</div>
			<h1>
				<?php echo lang('memberships_membership'); ?>
				<small></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('memberships_membership'); ?></h3>
						</div>
						<div class="box-body no-padding">
							<table class="table table-striped no-data-table">
								<tbody>
								<?php foreach($memberships as $membership): ?>
									<tr>
										<td><?php echo safe_anchor('admin/memberships/update', $membership->membership_id, $membership->name); ?></td>
									</tr>
								<?php endforeach; ?>
								</tbody>
							</table>
						</div>
					</div>
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');