<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Menu_Controller extends User_Controller
{
	function __construct()
	{
		parent::__construct();
		
		// Load dependencies
		$this->load->model('menu_model');
		$this->load->model('folder_model');
		$this->load->model('group_model');
		$this->data['body_class']   .= 'skin-white fixed';
		$this->data['folder']        = NULL;
		$this->data['folders']       = NULL;
		$this->data['menus']         = NULL;
		$this->data['groups']        = NULL;
	}
	
	public function _get_menu_from_cache( $menu_id )
	{
		if( !isset($this->menus['all'][$menu_id]) )
			show_404();
		if( $this->menus['all'][$menu_id]->company_id !== $this->auth_company_id )
			show_404();
		
		return $this->menus['all'][$menu_id];
	}
	
	public function _get_child_from_cache( $parent_id, $child = 0 )
	{
		if( !isset($this->menus['structure'][$parent_id]) )
			return NULL;
		
		$current = array_slice($this->menus['structure'][$parent_id],$child,1);
		return array_shift($current);
	}
	
	protected function _get_menu_parents( $menu )
	{
		$this->data['menu']['parents'][] = $menu->menu_id;
		if( $menu->parent_id === NULL )
		{
			return $menu->menu_id;
		}
		return $this->_get_menu_parents($this->_get_menu_from_cache($menu->parent_id));
	}

	public function _get_accessible_categories($categories)
	{
		$categories_groups = $this->group_model->get_relationships( 'category_group', 'category_id', array_keys($categories) );
		foreach($categories as $unset_category_id => $group_details)
		{
			if (!array_key_exists($unset_category_id, $categories_groups))
			{
				unset($categories[$unset_category_id]);
			}
			else {
				$cur_groups = $categories_groups[$unset_category_id];
				if( !acl_group_permits('menu.read', $cur_groups) )
				{
					unset($categories[$unset_category_id]);
				}
			}
		}
		return $categories;
	}
	
	public function _get_menu( $menu_id = NULL, $child = 0 )
	{
		if( empty($menu_id) ) { show_404(); }
		
		$this->data['menu']['current'] = $menu_id = $this->_get_menu_from_cache($menu_id);
		if( empty($this->data['menu']['current']) ) { show_404(); }
		$menu_id = isset($menu_id->parent_id) ? $menu_id->parent_id : $menu_id->menu_id;

		// If a folder have been selected; select the first child object instead
		if( $this->data['menu']['current']->type === '1' )
		{
			$this->data['menu']['folder'] = $this->data['menu']['current'];
			$this->data['menu']['current'] = $this->_get_child_from_cache($this->data['menu']['current']->menu_id,$child);
			if( $this->data['menu']['current'] === NULL ) { return; }
			
			if( ! empty($this->data['menu']['current']) && $this->data['menu']['current']->type === '1' )
			{
				$this->data['menu']['folder'] = $this->data['menu']['current'];
				$this->data['menu']['current'] = $this->_get_child_from_cache($this->data['menu']['current']->menu_id);
				// Folder doesn't contain any menu objects, make a new loop
				if( $this->data['menu']['current'] === NULL )
				{
					$menu = $this->_get_child_from_cache($menu_id,++$child);
					if( $menu === NULL ) { return; }
					$this->_get_menu($menu->parent_id,$child);
				}
			}
			
			if( $this->data['menu']['current'] === NULL ) { return; }
		}
		
		// Select the current menu and it's parents; to be able to highlight the correct menu objects
		if( isset($this->data['menu']['folder']) )
		{
			$this->data['menu']['parents'][] = $this->data['menu']['current']->menu_id;
			$parent_id = $this->_get_menu_parents($this->data['menu']['folder']);
		}
		else
		{
			$parent_id = $this->_get_menu_parents($this->data['menu']['current']);
		}
		
		$this->data['sidebar']['menu']['header']    = preg_replace('/^[0-9\.\s]+/u', '', $this->menus['all'][$parent_id]->name);
		$this->data['sidebar']['menu']['structure'] = $this->menus['structure'];
		$this->data['sidebar']['menu']['active']    = $this->data['menu']['parents'];
		$this->data['sidebar']['active']            = $this->auth_kiv ? TRUE : FALSE;
		$this->data['sidebar']['module'] 						= 'document';
	}
	
	public function _get_folder_data( $folder_id = NULL )
	{
		if( empty($folder_id) ) { show_404(); }
		
		$this->data['folder'] = $this->folder_model->get($folder_id);
		if( empty($this->data['folder']) ) { show_404(); }
		
		$this->_get_menu($this->data['folder']->menu_id);
	}
	
	public function _get_folders_data( $menu_id = NULL, $data = TRUE )
	{
		if( empty($menu_id) ) { show_404(); }
		
		$this->_get_menu($menu_id);
		
		if( $data )
		{
			$this->data['folders'] = $this->folder_model->get_all($menu_id);
		}
	}

	protected function _get_event_analysis() {
		$this->data['eventanalysis'] = array_merge(
			$this->data['report']['messages']['eventanalysis_actionlist']['warning'],
			$this->data['report']['messages']['eventanalysis']['warning'],
			$this->data['report']['messages']['eventanalysis']['critical']
		);
		$db = [
			'hostname' => $this->db->hostname,
			'database' => $this->db->database,
			'username' => $this->db->username,
			'password' => $this->db->password,
		];
		$this->load->library('eventanalysislib', $db);
		$this->data['events'] = $this->eventanalysislib->getAllByID($this->auth_company_id, array_keys($this->data['eventanalysis']));
		$this->load->model(['report_model']);
		$this->data['critical_events'] = $this->report_model->eventanalysis_by_id( array_keys($this->data['report']['messages']['eventanalysis']['critical']) );
	}

	protected function _get_checklists() {
		$this->data['checklists'] = $this->data['report']['messages']['checklist']['warning'];

		$checklists = [];
		$this->load->model('form_model');
		foreach($this->data['checklists'] as $checklist)
		{
			$checklists[$checklist->type_id] = NULL;
		}

		$this->data['pages'] = $this->form_model->get_pages(array_keys($checklists));
	}

	protected function _get_deviations() {
		$this->load->model('deviation_model');
		$this->data['fields']  = $this->deviation_model->getDeviationFieldsByPage(array(1,2,3));
		$this->data['rights']  = $this->acl['deviation'];
		$this->data['options'] = $this->deviation_model->getDropdown();
		
		$this->_setOptions($this->data['fields'], $this->data['options']);		
		$this->load->helper(array('form','forms'));

		if (!empty($this->data['rights']['read'])) {
			$deviations = $this->deviation_model->search( $this->data['rights']['read'], true );
			$this->data['list']       = $deviations['list'];
			$this->data['deviations'] = $deviations['deviations'];
			$this->data['results']    = $deviations['results'];
		}
	}

	protected function _get_education() {
		$this->load->model('document_model');
		$this->data['education'] = array_merge($this->education['todo'], $this->education['done']);
		if( empty($this->data['education']) ) { 
			$this->data['education_documents'] = [];
			return;
		}
		$this->data['education_documents'] = $this->document_model->get_all( array_keys($this->data['education']) );
	}

	protected function _new_documents()
	{
		ini_set('memory_limit', '440M');
		$this->load->helper('cookie');
		$last_visit = get_cookie('last_visit');
		$this->load->model(['document_model']);

		if ($last_visit == NULL) {
			$last_visit = date("Y-m-d H:i:s", strtotime('-1 week'));
		}
		$documents = $this->document_model->new_documents_for_user($last_visit);
		$this->data['documents'] = $documents;
	}

	
}
