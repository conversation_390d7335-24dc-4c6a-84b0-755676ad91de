<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class User_Controller extends MY_Controller
{
	public $menus = NULL;
	public $mainmenu = NULL;
	public $users = NULL;

	function __construct()
	{
		parent::__construct();

		// Require user to be logged in
		$this->CI =& get_instance();
		if( strpos($this->CI->uri->uri_string(), 'api/') !== 0 && $this->is_logged_in() )
		{
			if( empty($this->auth_email) && $this->config->item('ldap') !== TRUE )
			{
				redirect('login/logout'); exit;
			}
			// Load sidebar menu
			$this->load->model(['menu_model','posts_model','user_model','report_model','group_model']);
			$this->menus     = $this->menu_model->get_by_groups_bin($this->groups['types']['bin']['position'],$this->groups['types']['bin']['department'], $this->acl['object']);
			$this->mainmenu  = $this->posts_model->get_all_pages_for_menu();
			$this->has_tasks  = count(array_filter($this->mainmenu[1], function($item, $key) {
				return $item->name == "Mål" || $item->name == "Tasks";
			}, ARRAY_FILTER_USE_BOTH)) > 0;
			$this->users     = $this->user_model->get_all();
			$this->users_all = $this->user_model->get_all([],TRUE);
			$this->education = $this->report_model->education($this->groups['types']['bin']['position'], $this->groups['types']['bin']['education']);

			$security = $this->group_model->get_all([], 'security', ['security' => []], TRUE);
			if( ! empty($security) )
				$this->groups = array_merge($this->groups, $security);

			// Create new messages for checklists
			$checklists = $this->report_model->checklist($this->groups['types']['bin']['position']);
			if( ! empty($checklists['messages']) )
			{
				$this->load->model(['user_messages_model','form_model']);
				if( $this->user_messages_model->create_batch($checklists['messages']) )
				{
					$this->form_model->update_survey_dates($checklists['dates']);
				}
			}

			$this->data['report']['messages'] = $this->report_model->all_user_messages();
			$this->data['report']['documents']['warning']  = $this->report_model->documents_valid_date();
			$this->data['report']['education']['warning']  = $this->education['todo'];
			$this->data['report']['deviation']['warning']  = $this->report_model->deviationNextPage( $this->acl['deviation']['deviation_two'], $this->acl['deviation']['deviation_three'] );;
			$this->data['report']['documents']['critical'] = [];

			$this->data['report']['documents']['critical'] = $this->report_model->documents_past_valid_date(
				!is_role('Systemadministratör') ? TRUE : FALSE
			);

			$this->data['report']['success'] = array_sum(array_map("count", [
				$this->data['report']['messages']['documents']['success']
			]));

			$this->data['report']['warning'] = array_sum(array_map("count", [
				$this->data['report']['documents']['warning'],
				$this->data['report']['education']['warning'],
				$this->data['report']['deviation']['warning'],
				$this->data['report']['messages']['eventanalysis']['warning'],
				$this->data['report']['messages']['eventanalysis_actionlist']['warning'],
				$this->data['report']['messages']['documents']['warning'],
				$this->data['report']['messages']['autosave']['warning'],
				$this->data['report']['messages']['checklist']['warning'],
			]));
			$this->data['report']['critical'] = array_sum(array_map("count", [
				$this->data['report']['documents']['critical'],
				$this->data['report']['messages']['eventanalysis']['critical'],
				$this->data['report']['messages']['eventanalysis_actionlist']['critical'],
				$this->data['report']['messages']['documents']['critical'],
				$this->data['report']['messages']['autosave']['critical'],
				$this->data['report']['messages']['checklist']['critical'],
			]));
		}

		$this->data['sidebar']['menu']['controller'] = NULL;
		$this->data['sidebar']['menu']['header']     = NULL;
		$this->data['sidebar']['menu']['structure']  = [];
		$this->data['sidebar']['menu']['active']     = [];
		$this->data['sidebar']['admin']              = FALSE;
		$this->data['sidebar']['search']             = FALSE;
		$this->data['sidebar']['active']             = TRUE;
		$this->data['sidebar']['module'] 						 = 'document';
	}

	protected function _setOptions($fields, &$options)
	{
		if( ! empty($fields['type']['department']) )
		{
			foreach($fields['type']['department'] as $department)
			{
				foreach($this->data['rights']['create'] as $d)
				{
					if( isset($this->data['groups']['department'][$d]) ) {
						$dropdown = new stdClass();
						$dropdown->option_id = $d;
						$dropdown->name      = $this->data['groups']['department'][$d];
						$options[$department][$d] = $dropdown;
					}
				}
			}
		}

		if( ! empty($fields['type']['email']) )
		{
			foreach($fields['type']['email'] as $email)
			{
				$emailId = $email;
				foreach($this->users as $user)
				{
					$dropdown = new stdClass();
					$dropdown->option_id = $user->user_id;
					$dropdown->name      = $user->name;
					$options[$email][$user->user_id] = $dropdown;
				}
			}
		}

		if( ! empty($fields['type']['users']) )
		{
			foreach($fields['type']['users'] as $users)
			{
				foreach($this->users as $user)
				{
					$dropdown = new stdClass();
					$dropdown->option_id = $user->user_id;
					$dropdown->name      = $user->name;
					$options[$users][$user->user_id] = $dropdown;
				}
			}
		}
		if( $this->auth_kiv )
		{
			if( ! empty($fields['type']['eventanalysis']) )
			{
				foreach($fields['type']['eventanalysis'] as $eventanalysis)
				{
					$dropdown = new stdClass();
					$dropdown->option_id = 0;
					$dropdown->name      = 'Ingen djupare händelesanalys krävs';
					$options[$eventanalysis][0] = $dropdown;
					foreach($this->users as $user)
					{
						$dropdown = new stdClass();
						$dropdown->option_id = $eventanalysis . '_' . $user->user_id;
						$dropdown->name      = $user->name;
						$options[$eventanalysis][$user->user_id] = $dropdown;
					}
				}
			}
		}
	}
}
