<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class MY_Model extends CI_Model
{
	/**
	 * ACL for a logged in user
	 * @var mixed
	 */
	public $acl = NULL;
	
	/**
	 * Groups for a logged in user
	 * @var mixed
	 */
	public $groups = NULL;
	
	/**
	 * Class constructor
	 */
	public function __construct()
	{
		parent::__construct();
	}
	
	/**
	 * Get all of the ACL records for a specific user
	 */
	public function acl_query( $user_id, $called_during_auth = FALSE )
	{
		// Get ACL only ones
		if( !is_null( $this->acl ) )
		{
			return $this->acl;
		}
		
		// Convert user_id
		$user_id = UUID_TO_BIN($user_id);
		
		// User groups
		$query = $this->db->select('b.group_id, b.type, b.name')
			->from( $this->db_table('user_group') . ' a' )
			->join( $this->db_table('groups') . ' b' , 'a.group_id = b.group_id')
			->where('user_id', $user_id )
			->get();
			
		$groups           = [];
		$groups_bin       = [];
		$groups_types     = [
			'position'   => [],
			'department' => [],
			'education'  => [],
			'security'   => [],
		];
		$groups_types_bin = [
			'position'   => [],
			'department' => [],
			'education'  => [],
			'security'   => [],
		];
			
		if( $query->num_rows() > 0 )
		{
			// Add each group to the Groups array
			foreach( $query->result() as $row )
			{
				$uuid                           = BIN_TO_UUID($row->group_id);
				$groups[]                       = $uuid;
				$groups_bin[]                   = $row->group_id;
				$groups_types[$row->type][]     = $uuid;
				$groups_types_bin[$row->type][] = $row->group_id;
			}
		}
		
		if( empty($groups_bin) )
			return FALSE;
		
		// ACL table query
		$query = $this->db->select('a.object_id, a.group_id, b.action_id, b.action_code, c.category_code')
			->from( $this->db_table('acl_users_and_groups_table') . ' a' )
			->join( $this->db_table('acl_actions_table') . ' b', 'a.action_id = b.action_id' )
			->join( $this->db_table('acl_categories_table') . ' c', 'b.category_id = c.category_id' )
			->group_start()
			->where( 'a.user_id', $user_id )
			// ->where_in( 'a.group_id ', $groups )
			->group_end()
			->or_group_start()
			->where( 'a.user_id', NULL )
			->where_in( 'a.group_id ', $groups_bin )
			->group_end()
			->get();

		/**
		 * ACL becomes an array, even if there were no results.
		 * It is this change that indicates that the query was 
		 * actually performed.
		 */
		$acl = [
			'deviation' => [
				'create'          => [],
				'read'            => [],
				'deviation_two'   => [],
				'deviation_three' => [],
				'update'          => [],
			],
			'object' => [],
			'group' => []
		];

		if( $query->num_rows() > 0 )
		{
			// Add each permission to the ACL array
			foreach( $query->result() as $row )
			{
				// Permission identified by category + "." + action code + "." + group_id/object_id
				if( $row->object_id === NULL )
				{
					// $acl['group'][BIN_TO_UUID($row->action_id)] = $row->category_code . '.' . $row->action_code . '.' . BIN_TO_UUID($row->group_id);
					$acl['group'][BIN_TO_UUID($row->group_id)][] = $row->category_code . '.' . $row->action_code;
				}
				else
				{
					$acl['object'][BIN_TO_UUID($row->object_id)][] = $row->category_code . '.' . $row->action_code;
				}
				
				if( $row->category_code === 'deviation' && $row->object_id === NULL )
				{
					$acl['deviation'][$row->action_code][] = BIN_TO_UUID($row->group_id);
				}
				
			}
		}

		if( $called_during_auth OR $user_id == config_item('auth_user_id') )
			$this->acl = $acl;

		$this->groups['uuid']          = $groups;
		$this->groups['bin']           = $groups_bin;
		$this->groups['types']['uuid'] = $groups_types;
		$this->groups['types']['bin']  = $groups_types_bin;
		return $acl;
	}
	
	
	// -----------------------------------------------------------------------

	/**
	 * Check if ACL permits user to take action.
	 *
	 * @param  string  the concatenation of ACL category 
	 *                 and action codes, joined by a period.
	 * @param  array   multiple ACL groups
	 * @param  bool    accept empty groups
	 * @return bool
	 */
	public function acl_group_permits( $str, $groups, $emptygroups = FALSE )
	{
		list( $category_code, $action_code ) = explode( '.', $str );

		// We must have a legit category and action to proceed
		if( strlen( $category_code ) < 1 OR strlen( $action_code ) < 1 )
			return FALSE;
		
		if( empty($groups) && $emptygroups )
			return TRUE;
		
		if( empty($groups) )
			return FALSE;

		// Get ACL for this user if not already available
		if( is_null( $this->acl ) )
		{
			if( $this->acl = $this->acl_query( config_item('auth_user_id') ) )
			{
				// $this->load->vars( ['acl' => $this->acl] );
				// $this->config->set_item( 'acl', $this->acl );
			}
		}
		
		foreach($groups as $group)
		{
			if( 
				// If ACL gives permission for entire category
				isset($this->acl['group'][$group]) &&
				(
					in_array( $category_code . '.*', $this->acl['group'][$group] ) OR  
					in_array( $category_code . '.all', $this->acl['group'][$group] ) OR 

					// If ACL gives permission for specific action
					in_array( $category_code . '.' . $action_code, $this->acl['group'][$group] )
				)
			)
			{
				return TRUE;
			}
		}

		return FALSE;
	}
	
	// -----------------------------------------------------------------------
	
	public function acl_deviation_permits( $action_code, $groups )
	{
		// We must have a legit category and action to proceed
		if( strlen( $action_code ) < 1 )
			return FALSE;
		
		if( empty($groups) )
			return FALSE;

		// Get ACL for this user if not already available
		if( is_null( $this->acl ) )
		{
			if( $this->acl = $this->acl_query( config_item('auth_user_id') ) )
			{
				// $this->load->vars( ['acl' => $this->acl] );
				// $this->config->set_item( 'acl', $this->acl );
			}
		}
		
		foreach($groups as $group)
		{
			if(
				// If ACL gives permission for specific action
				isset($this->acl['deviation'][$action_code]) &&	
				in_array( $group, $this->acl['deviation'][$action_code] )
			)
			{
				return TRUE;
			}
		}

		return FALSE;
	}
	
	// -----------------------------------------------------------------------

	/**
	 * Check if ACL permits user to take action.
	 *
	 * @param  string  the concatenation of ACL category 
	 *                 and action codes, joined by a period.
	 * @param  array   multiple ACL objects
	 * @return bool
	 */
	public function acl_object_permits( $str, $objects )
	{
		list( $category_code, $action_code ) = explode( '.', $str );

		// We must have a legit category and action to proceed
		if( strlen( $category_code ) < 1 OR strlen( $action_code ) < 1 )
			return FALSE;

		// Get ACL for this user if not already available
		if( is_null( $this->acl ) )
		{
			if( $this->acl = $this->acl_query( config_item('auth_user_id') ) )
			{
				// $this->load->vars( ['acl' => $this->acl] );
				// $this->config->set_item( 'acl', $this->acl );
			}
		}
		
		foreach($objects as $object)
		{
			if(
				// If ACL gives permission for entire category
				isset($this->acl['object'][$object]) &&
				// If ACL gives permission for specific action
				in_array( $category_code . '.' . $action_code, $this->acl['object'][$object] )
			)
			{
				return TRUE;
			}
		}

		return FALSE;
	}
	
	// -----------------------------------------------------------------------
	
	/**
	 * Retrieve the true name of a database table.
	 *
	 * @param  string  the alias (common name) of the table
	 * @return  string  the true name (with CI prefix) of the table
	 */
	public function db_table( $name )
	{
		$name = config_item( $name );

		return $this->db->dbprefix( $name );
	}
}
