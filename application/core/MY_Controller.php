<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class MY_Controller extends Auth_Controller
{
	protected $data;
	
	function __construct()
	{
		parent::__construct();
		
		$this->data['body_class'] = 'hold-transition ';
		// $this->output->enable_profiler(TRUE);
	}

	protected function _get_document_type_data()
	{
		$this->data['documents_type'] = array(
			1  => lang('documents_type_reporting_environment'),
			2  => lang('documents_type_reporting_quality'),
			3  => lang('documents_type_regulation_environment'),
			4  => lang('documents_type_regulation_quality'),
			5  => lang('documents_type_regulation_environment_quality'),
			0  => lang('documents_type_informational'),
		);
	}
	
	public function VALID_UUIDv4( $uuid = NULL, $required = TRUE)
	{
		if( $uuid === NULL && !$required ) return NULL;
		if( !VALID_UUIDv4($uuid) OR strlen($uuid) !== 36 )
		{
			show_error(lang('error_400_body'),400,lang('error_400_header'));
		}
		return TRUE;
	}
}