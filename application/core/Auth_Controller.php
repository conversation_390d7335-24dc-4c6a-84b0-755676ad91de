<?php
defined('BASEPATH') OR exit('No direct script access allowed');

require_once( dirname(__FILE__) . "/../../vendor/autoload.php");
use OTPHP\TOTP;

class Auth_Controller extends CI_Controller
{
	/**
	 * Is the user logged-in
	 *
	 * @var bool
	 * @access public
	 */
	public $auth_logged_in = FALSE;
	
	public $auth_god;
	public $auth_kiv;
	public $auth_flex;
	
	/**
	 * The logged-in user's user ID
	 *
	 * @var string
	 * @access public
	 */
	public $auth_user_id;
	
	/**
	 * The logged-in user's company ID
	 *
	 * @var string
	 * @access public
	 */
	public $auth_company_id;

	/**
	 * The logged-in user's username
	 *
	 * @var string
	 * @access public
	 */
	public $auth_username;
	
	/**
	 * The logged-in user's name
	 *
	 * @var string
	 * @access public
	 */
	public $auth_name;

	/**
	 * The logged-in user's authentication account type by number
	 *
	 * @var string
	 * @access public
	 */
	public $auth_level;

	/**
	 * The logged-in user's authentication account type by name
	 *
	 * @var string
	 * @access public
	 */
	public $auth_role;

    /**
     * The logged-in user's email
     *
     * @var string
     * @access public
     */
    public $auth_email;

	/**
	 * The logged-in user's authentication data,
	 * which is their user table record, but could
	 * be whatever you want it to be if you modify 
	 * the queries in the auth model.
	 *
	 * @var object
	 * @access protected
	 */
	protected $auth_data;
	
	/**
	 * The logged-in user's ACL permissions after login, 
	 * or after login status check.
	 *
	 * If query for ACL performed, this variable becomes an array.
	 *
	 * @var mixed
	 * @access public
	 */
	public $acl = NULL;
	
	/**
	 * Groups for a logged in user
	 * @var mixed
	 */
	public $groups = NULL;
	public $groups_bin = NULL;
	public $groups_types = NULL;
	public $groups_types_bin = NULL;
	
	/**
	 * Either 'https' or 'http' depending on the current environment
	 *
	 * @var string
	 * @access public
	 */
	public $protocol = 'http';
	
	// --------------------------------------------------------------
	
	public function __construct()
	{
		parent::__construct();
		
		/**
		 * Loading of dependencies done here, as opposed to autoloading
		 * through config/autoload.php, because sometimes you will want to not 
		 * have all things autoloaded.
		 */
		$this->_load_dependencies();
		
		/**
		 * Set no-cache headers so pages are never cached by the browser.
		 * This is necessary because if the browser caches a page, the 
		 * login or logout link and user specific data may not change when 
		 * the logged in status changes.
		 */
	 	header('Expires: Wed, 13 Dec 1972 18:37:00 GMT');
		header('Cache-Control: no-store, no-cache, must-revalidate, post-check=0, pre-check=0');
		header('Pragma: no-cache');
		
		/**
		 * Set the request protocol
		 */
		if( is_https() )
			$this->protocol = 'https';
		
	}
	
	// --------------------------------------------------------------

	/**
	 * Load dependencies
	 */
	private function _load_dependencies()
	{
		$this->load->database();
		$this->config->load('db_tables');
		$this->config->load('authentication');
		$this->load->library([
			'session','authentication'
		])->model('auth_model');
	}

	protected function validate_otp($otp_code) {
		$this->auth_data = $this->authentication->check_login();
		$otp = TOTP::createFromSecret($this->auth_data->totp_secret); // create TOTP object from the secret.
		return $otp->verify($otp_code); // Returns true if the input is verified, otherwise false.
	}

	protected function validate_otp_secret($otp_code, $secret) {
		$otp = TOTP::createFromSecret($secret); // create TOTP object from the secret.
		return $otp->verify($otp_code); // Returns true if the input is verified, otherwise false.
	}

	protected function generate_otp_from_secret($secret, $name) {
		$otp = TOTP::createFromSecret($secret);
		$otp->setIssuer('Orna');
		$otp->setLabel($name);
		return $otp;
	}

	protected function generate_otp($name) {
		$otp = TOTP::generate();
		$otp->setIssuer('Orna');
		$otp->setLabel($name);
		return $otp;
	}
	
	// --------------------------------------------------------------
	
	/**
	 * Verify if user logged in
	 *
	 * @return  bool  TRUE if logged in
	 */
	protected function is_logged_in()
	{
		// Has user already been authenticated?
		if( $this->auth_logged_in )
		{
			return TRUE;
		}

		// Check if logged in
		$this->auth_data = $this->authentication->check_login();
		$CI =& get_instance();
		if ($this->auth_data && !empty($this->auth_data->totp_secret) && $CI->session->userdata('totp_required')) {
			$verify_page = 'verify';
			if( $this->uri->uri_string() != $verify_page )
			{
				$this->_redirect_to_login_page($verify_page);
			}
			return FALSE;
		}

		// Set user variables if user is logged in
		if( $this->auth_data )
			$this->_set_user_variables();

		// Call the post auth hook
		$this->post_auth_hook();

		// User is logged in
		if( $this->auth_data )
		{
			return TRUE;
		}
		
		// Else check if we need to redirect to the login page
		else if( $this->uri->uri_string() != LOGIN_PAGE )
		{
			$this->_redirect_to_login_page(LOGIN_PAGE);
		}
		
		// Else this is a failed login attempt or the login page was loaded
		return FALSE;
	}
	
	// --------------------------------------------------------------

	/**
	 * Redirect to the login page
	 */
	private function _redirect_to_login_page($redirect_page)
	{
		// Determine the login redirect
		$redirect = $this->input->get(AUTH_REDIRECT_PARAM)
			? urlencode( $this->input->get(AUTH_REDIRECT_PARAM) ) 
			: urlencode( $this->uri->uri_string() );

		// Set the redirect protocol
		$redirect_protocol = USE_SSL ? 'https' : NULL;

		// Load URL helper for the site_url function
		$this->load->helper('url');

		// Redirect to the login form
		header(
			'Location: ' . site_url( $redirect_page . '?' . AUTH_REDIRECT_PARAM . '=' . $redirect, $redirect_protocol ),
			TRUE,
			302
		);
		
		// Force redirect now
		exit;
	}
	
	// --------------------------------------------------------------

	/**
	 * Set variables related to authentication, for use in views / controllers.
	 */
	protected function _set_user_variables()
	{
		// Set user specific variables to be available in controllers
		$this->auth_logged_in   = TRUE;
		$this->auth_user_id     = $this->auth_data->user_id;
		$this->auth_company_id  = $this->auth_data->company_id;
		$this->auth_username    = $this->auth_data->username;
		$this->auth_name        = $this->auth_data->name;
		// $this->auth_level    = $this->auth_data->auth_level;
		// $this->auth_role     = $this->authentication->roles[$this->auth_data->auth_level];
		$this->auth_god         = $this->auth_data->god;
		$this->auth_kiv         = $this->auth_data->kiv;
		$this->auth_flex        = $this->auth_data->flex;
		$this->auth_email       = $this->auth_data->email;

		// Set user specific variables to be available in all views
		$data = [
			'auth_logged_in'    => TRUE,
			'auth_user_id'      => $this->auth_user_id,
			'auth_company_id'   => $this->auth_company_id,
			'auth_username'     => $this->auth_username,
			'auth_name'         => $this->auth_name,
			// 'auth_level'    => $this->auth_level,
			// 'auth_role'     => $this->auth_role,
			'auth_email'        => $this->auth_email,
			'auth_god'          => $this->auth_god,
			'auth_kiv'          => $this->auth_kiv,
			'auth_flex'         => $this->auth_flex,
		];

		// Set user specific variables to be available as config items
		$this->config->set_item( 'auth_logged_in',  TRUE );
		$this->config->set_item( 'auth_user_id',  $this->auth_user_id );
		$this->config->set_item( 'auth_company_id',  $this->auth_company_id );
		$this->config->set_item( 'auth_username', $this->auth_username );
		$this->config->set_item( 'auth_name', $this->auth_name );
		// $this->config->set_item( 'auth_level',    $this->auth_level );
		// $this->config->set_item( 'auth_role',     $this->auth_role );
		$this->config->set_item( 'auth_email',    $this->auth_email );
		$this->config->set_item( 'auth_god',    $this->auth_god );
		$this->config->set_item( 'auth_kiv',    $this->auth_kiv );
		$this->config->set_item( 'auth_flex',    $this->auth_flex );

		// Add ACL permissions if ACL query turned on
		if( config_item('add_acl_query_to_auth_functions') )
		{
			$this->acl              = $this->auth_data->acl;
			$this->groups           = $this->auth_data->groups;
			// $this->groups_bin       = $this->auth_data->groups_bin;
			// $this->groups_types     = $this->auth_data->groups_types;
			// $this->groups_types_bin = $this->auth_data->groups_types_bin;
			$data['acl'] = $this->acl;
			$this->config->set_item( 'acl', $this->acl );
		}

		// Load vars
		$this->load->vars($data);
	}
	
	// --------------------------------------------------------------

	/**
	 * Setup redirect url
	 */
	protected function setup_login_form($url)
	{
		// Redirect to specified page
		$redirect = $this->input->get(AUTH_REDIRECT_PARAM)
			? '?' . AUTH_REDIRECT_PARAM . '=' . $this->input->get(AUTH_REDIRECT_PARAM) 
			: '?' . AUTH_REDIRECT_PARAM . '=' . config_item('default_login_redirect');

		// Set the link protocol
		$link_protocol = USE_SSL ? 'https' : NULL;

		// Load URL helper for site_url function
		$this->load->helper('url');

		// Set the login URL
		$view_data['login_url'] = site_url( $url . $redirect, $link_protocol );

		$this->load->vars( $view_data );
	}
	
	// --------------------------------------------------------------
	
	/**
	 * The post auth hook allows you to do something on every request 
	 * that may involve knowing if the user is logged in or not. 
	 * Notice that this method is called after user variables are set, 
	 * giving you an opportunity to do something with them.
	 *
	 * If the request is for a page that doesn't call any authentication
	 * methods, you'll need to call this method manually.
	 *
	 * By default, this method doesn't do anything, but you may 
	 * override this method in your MY_Controller.
	 */
	protected function post_auth_hook()
	{
		return;
	}
	
	// -----------------------------------------------------------------------
}
