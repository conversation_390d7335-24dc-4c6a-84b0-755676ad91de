<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Form_Controller extends User_Controller
{
	function __construct()
	{
		parent::__construct();
		
		// Load dependencies
		$this->load->model('form_model');
		$this->load->model('folder_model');
		$this->load->model('group_model');
		$this->data['body_class']        .= 'skin-white fixed';
		$this->data['form']               = NULL;
		$this->data['form_id']            = NULL;
		$this->data['page']               = NULL;
		$this->data['page_id']            = NULL;
		$this->data['sub_page']           = NULL;
		$this->data['sub_page_id']        = NULL;
		$this->data['page_current']       = NULL;
		$this->data['page_current_id']    = NULL;
		$this->data['question']           = NULL;
		$this->data['question_id']        = NULL;
		$this->data['question_parent']    = NULL;
		$this->data['question_parent_id'] = NULL;
		
		// $this->data['sidebar']['admin']  = TRUE;
		$this->data['sidebar']['active'] = TRUE;
	}
	
	public static $types = array(
		'form'     => 0,
		'page'     => 1,
		'folder'   => 1,
		'question' => 2,
		'option'   => 3
	);
	
	protected function _get_form_types()
	{
		$this->data['forms_types'] = array(
			'checklist'     => lang('forms_checklist')
		);
		
		if( $this->auth_god )
		{
			$this->data['forms_types'] = array_merge($this->data['forms_types'],[
				'form'          => lang('forms_form'),
				'qa_kiv'        => lang('forms_qa_kiv'),
				'qa_flex'       => lang('forms_qa_flex'),
				'yp_kiv'        => lang('forms_yp_kiv'),
				'yp_flex'       => lang('forms_yp_flex'),
				'deviaton_kiv'  => lang('forms_deviaton_kiv'),
				'deviaton_flex' => lang('forms_deviaton_flex')
			]);
		}
	}
	
	protected function _get_field_types()
	{
		$this->data['field_types'] = array(
			'input'        => lang('field_input'),
			'text'         => lang('field_text'),
			'text_wysiwyg' => lang('field_text_wysiwyg'),
			'dropdown'     => lang('field_dropdown'),
			'date'         => lang('field_date'),
			'checkbox'     => lang('field_checkbox'),
			// 'yes_no'       => lang('field_yes_no'),
			'radio'        => lang('field_radio'),
			'users'        => lang('field_users'),
			'table'        => lang('field_table'),
			'heading'      => lang('field_heading'),
		);
	}
	
	protected function _get_multiple_pages_data( $form_id = NULL, $page_id = NULL, $sub_page_id = NULL, $current_id = NULL, $question_id = NULL, $option_id = NULL )
	{
		if( $form_id)
		{
			$this->data['form'] = $this->form_model->get($form_id);
			if( empty($this->data['form']) ) { show_404(); }
			if( $this->data['form']->company_id !== $this->auth_company_id ) { show_404(); }
		}
		
		if( $page_id )
		{
			$this->data['page'] = $this->form_model->get_page($page_id);
			$this->data['page_current'] = $this->data['page'];
			if( empty($this->data['page']) ) { show_404(); }
			if( $this->data['page']->form_id !== $this->data['form']->form_id ) { show_404(); }
		}
		
		if( $sub_page_id )
		{
			$this->data['sub_page'] = $this->form_model->get_page($sub_page_id);
			$this->data['page_current'] = $this->data['sub_page'];
			if( empty($this->data['sub_page']) ) { show_404(); }
			if( $this->data['sub_page']->form_id !== $this->data['form']->form_id ) { show_404(); }
		}
		
		if( $current_id )
		{
			$this->data['page_current'] = $this->form_model->get_page($current_id);
			if( empty($this->data['page_current']) ) { show_404(); }
			if( $this->data['page_current']->form_id !== $this->data['form']->form_id ) { show_404(); }
		}
		
		if( $question_id )
		{
			$this->data['question'] = $this->form_model->get_question($question_id);
			if( empty($this->data['question']) ) { show_404(); }
			if( $this->data['question']->page_id !== $this->data['page_current']->page_id ) { show_404(); }
		}
		
		if( $option_id )
		{
			$this->data['option'] = $this->form_model->get_option($option_id);
			if( empty($this->data['option']) ) { show_404(); }
			if( $this->data['option']->question_id !== $this->data['question']->question_id ) { show_404(); }
		}
		
		if( isset($this->data['question']->parent_id) )
		{
			$this->data['question_parent'] = $this->form_model->get_question($this->data['question']->parent_id);
			if( empty($this->data['question_parent']) ) { show_404(); }
			if( $this->data['question_parent']->page_id !== $this->data['page_current']->page_id ) { show_404(); }
		}
		
		if( $this->data['form'] )
			$this->data['form_id']            = $this->data['form']->form_id;
		if( $this->data['page'] )
			$this->data['page_id']            = $this->data['page']->page_id;
		if( $this->data['sub_page'] )
			$this->data['sub_page_id']        = $this->data['sub_page']->page_id;
		if( $this->data['page_current'] )
			$this->data['page_current_id']    = $this->data['page_current']->page_id;
		if( $this->data['question'] )
			$this->data['question_id']        = $this->data['question']->question_id;
		if( $this->data['question_parent'] )
			$this->data['question_parent_id'] = $this->data['question_parent']->question_id;
	}
	
	protected function _get_urls( $question_id = NULL, $current_id = NULL, $form_id = NULL, $page_id = NULL, $sub_page_id = NULL )
	{
		$complete_url = array(
			$question_id,
			$current_id,
			$form_id,
			$page_id,
			$sub_page_id
		);
		
		$question_url = array(
			$current_id,
			$form_id,
			$page_id,
			$sub_page_id
		);
		
		$page_url = array(
			$form_id,
			$page_id,
			$sub_page_id
		);
		
		$this->data['complete_url'] = array_filter($complete_url);
		$this->data['question_url'] = array_filter($question_url);
		$this->data['page_url'] = array_filter($page_url);
	}
	
}
